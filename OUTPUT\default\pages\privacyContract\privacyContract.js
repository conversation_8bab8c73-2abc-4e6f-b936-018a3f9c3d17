var n = null;
Page({
  data: {
    name: "沃贝"
  },
  open: function() {
    wx.openPrivacyContract()
  },
  handleAgreePrivacyAuthorization: function() {
    n && n({
      buttonId: "agree-btn",
      event: "agree"
    }), wx.reLaunch({
      url: "/pages/Home/Home"
    })
  },
  back: function() {
    wx.exitMiniProgram()
  },
  onShow: function() {
    console.log("测试哦"), wx.onNeedPrivacyAuthorization((function(o) {
      console.log("resolve", o), n = o
    }))
  },
  onLoad: function() {}
});