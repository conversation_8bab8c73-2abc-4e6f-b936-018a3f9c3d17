var style = require('..\..\..\..\require('.\miniprogram_npm\ @vant\ weapp\ wxs\ style.wxs ')();');
var addUnit = require('..\..\..\..\require('.\miniprogram_npm\ @vant\ weapp\ wxs\ add - unit.wxs ')();');

function inputStyle(autosize) {
  if (autosize && autosize.constructor === 'Object') {
    return (style(({
      'min-height': addUnit(autosize.minHeight),
      'max-height': addUnit(autosize.maxHeight),
    })))
  };
  return ('')
};
module.exports = ({
  inputStyle: inputStyle,
});