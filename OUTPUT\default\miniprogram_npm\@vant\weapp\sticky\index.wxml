<view class="custom-class van-sticky" style="{{computed.containerStyle( {fixed:fixed,height:height,zIndex:zIndex} )}}">
    <view class="{{utils.bem( 'sticky-wrap',{fixed:fixed} )}}" style="{{computed.wrapStyle( {fixed:fixed,offsetTop:offsetTop,transform:transform,zIndex:zIndex} )}}">
        <slot></slot>
    </view>
</view>

<wxs module="utils" src="..\wxs\utils.wxs"/>
<wxs module="computed" src="index.wxs"/>