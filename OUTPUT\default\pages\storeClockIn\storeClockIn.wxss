.storeClockIn {
    background: #000;
    min-height: 100vh;
    position: relative;
    width: 100%
}

.storeClockIn_bg {
    background-size: 750rpx 1000rpx;
    height: 1405rpx;
    overflow: hidden;
    position: relative;
    width: 100%
}

.storeClockIn_jl {
    border: 1px solid #000;
    border-radius: 40rpx;
    font-size: 26rpx;
    height: 40rpx;
    line-height: 40rpx;
    position: absolute;
    right: 18rpx;
    text-align: center;
    top: 20rpx;
    width: 140rpx
}

.storeClockIn_look {
    height: 24rpx;
    left: 42rpx;
    position: absolute;
    top: 239rpx;
    width: 169rpx
}

.storeClockIn_button {
    height: 64rpx;
    left: 50%;
    position: absolute;
    top: 936rpx;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 318rpx
}

.storeClockIn_Refresh {
    height: 27rpx;
    position: absolute;
    right: 73rpx;
    top: 946rpx;
    width: 74rpx
}

.storeClockIn_item {
    width: 100%
}

.storeClockIn_item_name {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    height: 60rpx
}

.storeClockIn_item_name_icon {
    height: 38rpx;
    margin-left: 26rpx;
    width: 38rpx
}

.storeClockIn_item_name_text {
    font-size: 24rpx;
    margin-left: 20rpx
}

.storeClockIn_box,.storeClockIn_item_name_text {
    color: #fff;
    font-family: Source Han Sans CN;
    font-weight: 500
}

.storeClockIn_box {
    box-sizing: border-box;
    font-size: 22rpx;
    margin: 0 auto;
    padding-bottom: 4rpx;
    padding-left: 23rpx;
    padding-top: 4rpx;
    width: 737rpx
}

.storeClockIn_bq {
    color: #fff;
    font-family: Arial;
    font-size: 18rpx;
    font-weight: 400;
    margin-top: 10rpx;
    text-align: center
}

.home_swiperBox {
    position: relative
}

.home_swiper {
    height: 1000rpx;
    margin: 0 auto;
    overflow: hidden;
    width: 100%
}

.home_swiper_left {
    left: 14rpx
}

.home_swiper_left,.home_swiper_right {
    height: 42rpx;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 42rpx;
    z-index: 999
}

.home_swiper_right {
    right: 14rpx
}

.swiperItem {
    height: 360rpx;
    width: 100%
}

.swiper-item,.swiperItem {
    margin: 0 auto;
    overflow: hidden
}

.swiper-item {
    height: 1000rpx
}

.swiper-item wx-image {
    display: block;
    height: auto;
    width: 100%
}
