<view class="OrderInfo">
    <view class="OrderInfo_top" wx:if="{{orderStateInfo.state==37||orderStateInfo.state==82}}">
        <view class="OrderInfo_top_icon">
            <image mode="" src="{{img}}newVersion/020.png"></image>
        </view>
        <view class="OrderInfo_top_text">订单已完成</view>
    </view>
    <view class="OrderInfo_top2" wx:elif="{{orderStateInfo.state==36}}">
        <view class="OrderInfo_top_icon2">
            <image mode="" src="{{img}}newVersion/019.png"></image>
        </view>
        <view class="OrderInfo_top_text">
            <block wx:if="{{!isGiftType}}">
                <view class="OrderInfo_top_text_1">订单待收货</view>
                <view class="OrderInfo_top_text_2">请于30天之内收货，超时订单将自动收货</view>
            </block>
            <view class="OrderInfo_top_text_1" wx:else>订单待收货</view>
        </view>
    </view>
    <view class="OrderInfo_top3" wx:elif="{{orderStateInfo.state==31}}">
        <view class="OrderInfo_top_icon3">
            <image mode="" src="{{img}}newVersion/018.png"></image>
        </view>
        <view class="OrderInfo_top_text">订单待发货</view>
    </view>
    <view class="OrderInfo_top3" wx:else>
        <view class="OrderInfo_top_icon3">
            <image mode="" src="{{img}}newVersion/018.png"></image>
        </view>
        <view class="OrderInfo_top_text">{{orderStateInfo.name}}</view>
    </view>
    <view class="OrderInfo_goodInfo">
        <view class="OrderInfo_goodInfo_top">
            <view class="OrderInfo_goodInfo_top_item" wx:for="{{orderInfo.orderDetail}}" wx:key="index">
                <view class="OrderInfo_goodInfo_top_img">
                    <image mode="" src="{{item.giftImage}}"></image>
                </view>
                <view class="OrderInfo_goodInfo_top_r">
                    <view class="OrderInfo_goodInfo_top_r_name">{{item.giftName}}</view>
                    <view class="OrderInfo_goodInfo_top_r_num">
                        <view class="OrderInfo_goodInfo_top_r_num_price">{{item.unitScore}}<text style="font-size:24rpx;">能量</text>
                        </view>
                        <view class="OrderInfo_goodInfo_top_r_num_num" wx:if="{{!isGiftType}}">x{{item.giftCount}}</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="OrderInfo_goodInfo_bot">
            <view class="OrderInfo_goodInfo_bot_item">
                <view class="text">订单状态:</view>
                <view class="value" wx:if="{{orderInfo.orderStatus==31}}">待发货</view>
                <view class="value" wx:elif="{{orderInfo.orderStatus==36}}">待收货</view>
                <view class="value" wx:elif="{{orderInfo.orderStatus==37||orderInfo.orderStatus==82}}">已完成</view>
                <view class="value" wx:else>{{orderInfo.orderStatusName}}</view>
            </view>
            <view class="OrderInfo_goodInfo_bot_item">
                <view class="text">订单总价:</view>
                <view class="values">{{totalPrice}}</view>
            </view>
            <view class="OrderInfo_goodInfo_bot_item">
                <view class="text">订单编号:</view>
                <view class="value">{{orderNumber}}</view>
                <view bindtap="gotoCopyOrder" class="OrderInfo_goodInfo_copy">
                    <view class="OrderInfo_goodInfo_copyImg">
                        <image mode="" src="{{img}}copyIcon.png"></image>
                    </view>
                    <view class="OrderInfo_goodInfo_copyText">复制</view>
                </view>
            </view>
            <view class="OrderInfo_goodInfo_bot_item">
                <view class="text">兑换时间:</view>
                <view class="value">{{orderInfo.paidTime}}</view>
            </view>
        </view>
    </view>
    <view bind:tap="copyUrl" style="padding:20rpx 40rpx;background-color:#ffffff;font-size:24rpx;" wx:if="{{item.detail.giftType==1&&item.detail.sendType==4}}" wx:for="{{orderInfo.orderDetail}}" wx:key="index">
        <block wx:if="{{item.detail.giftName=='高德虚拟车标'}}">
            <view>兑换流程</view>
            <view>本兑换码仅限在高德APP使用，请复制兑换码，并前往“高德APP-我的—钱包卡券—券码兑换——数字资产兑换”进行兑换使用。本兑换时间截止至2024年11月30日。</view>
        </block>
        <block wx:else>
            <view>兑换流程</view>
            <view>1.兑换入口：https://22233.cn/1689</view>
            <view>2.进入页面后，选择对应卡券类型后，点击立即兑换即可。</view>
        </block>
    </view>
    <block wx:if="{{!isGiftType}}">
        <view class="OrderInfo_logistics" wx:if="{{orderInfo.orderStatus==36||orderInfo.orderStatus==37||orderInfo.orderStatus==82}}">
            <view class="OrderInfo_logistics_title">物流信息</view>
            <view class="OrderInfo_logistics_item">
                <view class="text">快递单号:</view>
                <view class="value">{{orderInfo.expressNo}}</view>
                <view bindtap="gotoCopyKd" class="OrderInfo_logistics_copy">
                    <view class="OrderInfo_goodInfo_copyImg">
                        <image mode="" src="{{img}}copyIcon.png"></image>
                    </view>
                    <view class="OrderInfo_goodInfo_copyText">复制</view>
                </view>
            </view>
            <view class="OrderInfo_logistics_item">
                <view class="text">快递公司:</view>
                <view class="value">{{orderInfo.expressCompanyName}}</view>
            </view>
        </view>
        <view class="OrderInfo_logistics">
            <view class="OrderInfo_logistics_title">地址信息</view>
            <view class="OrderInfo_logistics_item">
                <view class="text">收件人:</view>
                <view class="value">{{orderInfo.consigneeName}}</view>
            </view>
            <view class="OrderInfo_logistics_item">
                <view class="text">联系方式:</view>
                <view class="value">{{orderInfo.consigneeMobile}}</view>
            </view>
            <view class="OrderInfo_logistics_item">
                <view class="text">收件地址:</view>
                <view class="value">{{orderInfo.consigneeAddress}}</view>
            </view>
        </view>
    </block>
    <block wx:if="{{!isGiftType&&orderInfo.orderStatus==36}}">
        <view style="height:300rpx;"></view>
        <view class="OrderInfo_bot">
            <footer></footer>
            <view bindtap="gotoSh" class="OrderInfo_confirmGoods">确认收货</view>
        </view>
    </block>
    <block wx:else>
        <view style="height:200rpx;"></view>
        <footer class="footer"></footer>
    </block>
</view>
