var n = require("../../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  o = require("../../../../95D1B746549B04BFF3B7DF41DA740D65.js");
Page({
  data: {
    statusHeaderBarHeight: n.statusHeaderBarHeight,
    imgUrl: n.imgUrl,
    imgVersion: n.imgVersion,
    screenInfo: (0, o.getScreenInfo)()
  },
  onLogin: function(n) {
    n.detail.loginStatus && this.initData()
  },
  initData: function() {
    console.log("初始化登录")
  },
  handleTest: function(n) {},
  onLoad: function(n) {},
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return n.shareOptions
  }
});