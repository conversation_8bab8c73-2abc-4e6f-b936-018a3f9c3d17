var e = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  t = require("../../../A5622344549B04BFC3044B435F450D65.js"),
  a = null;
Component({
  properties: {},
  data: {
    imgUrl: e.imgUrl,
    imgVersion: e.imgVersion,
    open: !1,
    currentState: "front",
    hideFront: !1,
    RedCoverType: "",
    RedCoverUrl: "",
    Coupon: {}
  },
  pageLifetimes: {
    show: function() {}
  },
  methods: {
    openMask: function(e, a, n) {
      this.triggerEvent("isGet", {
        flag: !1
      }), this.initRest(), this.setData({
        open: !0,
        RedCoverType: e,
        RedCoverUrl: a,
        Coupon: n
      }), t.pagePopupViews({
        page_type: "红包中签",
        page_name: "抽签页面",
        popup_name: ["幸运祈愿", "健康快乐签", "好运花生"][Number(e) - 1],
        page_path: "cny/pages/drawLots/index"
      })
    },
    closeMask: function() {
      this.setData({
        open: !1
      })
    },
    handleClose: function() {
      this.closeMask()
    },
    handleMyTask: function() {
      wx.navigateTo({
        url: "/cny/pages/task/index"
      })
    },
    changeCurrentState: function(e) {
      var n = this;
      this.setData({
        currentState: e
      }), "back" === e && (clearTimeout(a), a = setTimeout((function() {
        n.setData({
          hideFront: !0
        })
      }), 1100), t.pagePopupViews({
        page_type: "优惠券",
        page_name: "抽签页面",
        popup_name: this.data.Coupon.infoTitleTwo,
        page_path: "cny/pages/drawLots/index"
      }))
    },
    handleStartUse: function() {
      var a = this.data.Coupon,
        n = a.couponUrl,
        o = a.couponType,
        p = a.appid;
      "外部小程序" === o ? wx.navigateToMiniProgram({
        appId: p,
        path: n,
        success: function(e) {}
      }) : wx.setClipboardData({
        data: n,
        success: function(t) {
          wx.showToast({
            icon: "none",
            duration: 5e3,
            title: e.tipInfos.copyTitle
          })
        }
      }), t.pagePopupClickEvents({
        page_type: "红包中签",
        page_name: "抽签页面",
        popup_name: this.data.Coupon.infoTitleTwo,
        page_path: "cny/pages/drawLots/index",
        button_name: "前往领取"
      })
    },
    handleGoTask: function() {
      this.closeMask(), wx.navigateTo({
        url: "/cny/pages/task/index"
      }), t.pagePopupClickEvents({
        page_type: "红包中签",
        page_name: "抽签页面",
        popup_name: this.data.Coupon.infoTitleTwo,
        page_path: "cny/pages/drawLots/index",
        button_name: "参与活动"
      })
    },
    initRest: function() {
      this.setData({
        currentState: "front",
        hideFront: !1
      })
    },
    handleGoGetRedCover: function() {
      this.triggerEvent("isGet", {
        flag: !0
      }), wx.showRedPackage({
        url: this.data.RedCoverUrl
      }), t.pagePopupClickEvents({
        page_type: "红包中签",
        page_name: "抽签页面",
        popup_name: ["幸运祈愿", "健康快乐签", "好运花生"][Number(this.data.RedCoverType) - 1],
        page_path: "cny/pages/drawLots/index",
        button_name: "前往领取"
      })
    }
  }
});