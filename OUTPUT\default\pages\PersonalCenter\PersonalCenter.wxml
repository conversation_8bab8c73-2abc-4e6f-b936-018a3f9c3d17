<view class="pc">
    <view bindtap="gotoSetUp" class="pc_top">
        <view class="pc_top_head">
            <image class="imageHead" mode="" src="{{userInfo.headImage?userInfo.headImage:img+'banner10.jpeg'}}"></image>
        </view>
        <view class="pc_top_content">
            <view catchtap="gotoEmpower" class="pc_top_content_1" wx:if="{{!phoneEmpowerState}}">点击授权登陆</view>
            <view class="pc_top_content_1s" wx:else>{{userInfo.memberName}}</view>
            <view class="pc_top_content_text">绑定号码：{{phone}}</view>
            <view class="pc_top_content_text">可用能量：{{userInfo.currentJf}}</view>
        </view>
        <view class="pc_top_jian">
            <image mode="" src="{{img}}newVersion/006.png"></image>
        </view>
    </view>
    <view class="pc_bot">
        <view bindtap="gotoMpUrl" class="pc_bot_item" data-item="{{item}}" style="border-bottom:{{list.length-1!=index?'2rpx solid #E9E9E9':'none'}}" wx:for="{{list}}" wx:key="index">
            <view class="pc_bot_item_icon_{{index}}">
                <image mode="" src="{{img+item.src}}"></image>
            </view>
            <view class="pc_bot_item_text">{{item.name}}</view>
            <view class="pc_bot_item_j">
                <image mode="" src="{{img}}newVersion/006.png"></image>
            </view>
        </view>
    </view>
    <prop bindcloseProp="closeProp" propNum="{{propNum}}" wx:if="{{propState}}"></prop>
    <view style="height:320rpx;"></view>
    <view class="tabbarProp">
        <footer></footer>
        <view class="tabbarProp_bot">
            <tabbar actice="my"></tabbar>
        </view>
    </view>
</view>
