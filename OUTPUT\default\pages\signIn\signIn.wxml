<view class="signIn">
    <view bindtap="showRule" class="signIn_rule">
        <image mode="" src="{{img}}newVersion/022.png"></image>
    </view>
    <view class="signIn_top">
        <image mode="" src="{{img}}newVersion/067.png"></image>
    </view>
    <view class="signIn_box">
        <view class="signIn_box_title">{{curMonth}}.<text style="font-size:33rpx;">{{curYear}}</text>
        </view>
        <view class="signIn_box_week">
            <view class="signIn_box_week_item" wx:for="{{daylist}}" wx:key="index">{{item.name}}</view>
        </view>
        <view class="signIn_box_calendar">
            <view bindtap="gotoPreviousMouth" class="signIn_box_left" wx:if="{{isHaveUpper}}">
                <image mode="" src="{{img}}newVersion/001.png"></image>
            </view>
            <view bindtap="gotoNextMouth" class="signIn_box_right" wx:if="{{isHaveNext}}">
                <image mode="" src="{{img}}newVersion/002.png"></image>
            </view>
            <view bindtap="signCur" class="signIn_box_calendar_item" data-index="{{index}}" data-item="{{item}}" wx:for="{{calendarList}}" wx:key="index">
                <view class="signIn_box_calendar_item_radio" wx:if="{{item.isChooseState}}"></view>
                <view wx:if="{{item.num!=0}}">
                    <view class="signIn_box_calendar_item_img" wx:if="{{item.sign==1}}">
                        <image mode="" src="{{img}}draw/jiaoyin.png"></image>
                    </view>
                    <view class="signIn_box_calendar_item_img2" wx:if="{{item.sign==2}}">
                        <image mode="" src="{{img}}draw/icon-box.png"></image>
                    </view>{{item.num}}</view>
            </view>
        </view>
    </view>
    <view class="signIn_buttons">
        <view bindtap="gotoRepair" class="signIn_buttons_l">
            <view>
                <view class="signIn_buttons_l_title">补 签</view>
                <view class="signIn_buttons_l_text">剩余补签次数：{{residueNumber}}</view>
            </view>
        </view>
        <view bindtap="gotoSignIn" class="signIn_buttons_r">
            <view>
                <view class="signIn_buttons_r_title">立即签到</view>
                <view class="signIn_buttons_r_text">本月已签到{{totalMonthSignCount}}天</view>
            </view>
        </view>
    </view>
    <view class="signIn_tips">
        <view class="signIn_tips_title">
            <view class="signIn_tips_title_l">
                <image mode="" src="{{img}}draw/icon-calendar.png"></image>
                <view>连续签到可获得额外能量:</view>
            </view>
            <view>已连续签到<text class="title-tag">{{continuousSignDays||0}}</text>天</view>
        </view>
        <view class="signIn_tips_num">
            <view class="signIn_tips_num_item">
                <view style="display:flex;justify-content:space-between;">
                    <view>
                        <view>连续签到</view>
                        <view>15天</view>
                    </view>
                    <view class="signIn_tips_num_item_r">
                        <view>
                            <text class="num_item_max">20</text>能量</view>
                    </view>
                </view>
                <view class="num_item_samll" style="text-align:right;">(包含每日签到在内)</view>
            </view>
            <view class="signIn_tips_num_item">
                <view style="display:flex;justify-content:space-between;">
                    <view>
                        <view>连续签到</view>
                        <view>30天</view>
                    </view>
                    <view class="signIn_tips_num_item_r">
                        <view>
                            <text class="num_item_max">40</text>能量</view>
                    </view>
                </view>
                <view class="num_item_samll" style="text-align:right;">(包含每日签到和连续签到能量在内)</view>
            </view>
        </view>
    </view>
    <footer class="footer"></footer>
    <prop bindcloseProp="closeProp" bindconfirmProp="confirmProp" propNum="{{propNum}}" ruleText="{{ruleText}}" signNum="{{signNum}}" wx:if="{{propState}}"></prop>
</view>
