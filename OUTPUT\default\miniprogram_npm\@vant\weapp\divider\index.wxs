var style = require('..\..\..\..\require('.\miniprogram_npm\ @vant\ weapp\ wxs\ style.wxs ')();');
var addUnit = require('..\..\..\..\require('.\miniprogram_npm\ @vant\ weapp\ wxs\ add - unit.wxs ')();');

function rootStyle(data) {
  return (style([({
    'border-color': data.borderColor,
    color: data.textColor,
    'font-size': addUnit(data.fontSize),
  }), data.customStyle]))
};
module.exports = ({
  rootStyle: rootStyle,
});