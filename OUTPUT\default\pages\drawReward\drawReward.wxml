<view class="drawReward">
    <view class="drawReward_head">
        <image mode="" src="{{userInfo.headImage}}"></image>
    </view>
    <view bindtap="gotoShop" class="drawReward_dh">
        <view class="drawReward_dh_l">
            <view class="drawReward_dh_l_num">{{currentJf}}</view>
            <view class="drawReward_dh_l_text">可用能量值</view>
        </view>
        <view class="drawReward_dh_r">兑换</view>
    </view>
    <view bindtap="showRule" class="drawReward_rule">
        <image mode="" src="{{img}}newVersion/022.png"></image>
    </view>
    <view class="container-out">
        <view class="container-in">
            <view class="award" style="top:{{item.awardTop}}rpx;left:{{item.awardLeft}}rpx;" wx:for="{{awardList}}" wx:key="index">
                <image class="awardBg" mode="" src="{{img}}newVersion/052.png" wx:if="{{selectedIdx!=index}}"></image>
                <image class="awardBg" mode="" src="{{img}}newVersion/053.png" wx:else></image>
                <view class="award_item">
                    <view class="award_item_img" wx:if="{{item.awardsType!=1}}">
                        <image mode="" src="{{item.awardsImg}}"></image>
                    </view>
                    <view class="award_item_img" wx:else>
                        <image mode="" src="{{img}}nopriceNew.png?123123"></image>
                    </view>
                    <view class="award_item_text">{{item.awardsName}}</view>
                </view>
            </view>
            <view bindtap="showExchangeProp" class="start-btn">
                <view class="startFlex">
                    <view class="start_text" style="padding-top:10rpx;"></view>
                </view>
            </view>
        </view>
    </view>
    <view class="drawReward_tt">
        <image mode="" src="{{img}}newVersion/050.png"></image>
    </view>
    <view bindtap="getMoreEnergy" class="drawReward_nl">
        <image mode="" src="{{img}}newVersion/051.png"></image>
    </view>
    <view class="drawReward_rw">
        <image mode="" src="{{img}}newVersion/048.png"></image>
    </view>
    <footer class="footer"></footer>
    <prop activityId="{{activityId}}" allThanks="{{allThanks}}" bindcloseProp="closeProp" bindconfirmProp="confirmProp" bindsubmitUserInfo="submitUserInfo" energyNumber="{{energyNumber}}" exchangeEnergyNumber="{{exchangeEnergyNumber}}" giftDetailInfo="{{giftDetailInfo}}" newDrawRaffieInfo="{{newDrawRaffieInfo}}" priceData="{{priceData}}" priceInfo="{{priceInfo}}" propNum="{{propNum}}" wx:if="{{propState}}"></prop>
</view>
