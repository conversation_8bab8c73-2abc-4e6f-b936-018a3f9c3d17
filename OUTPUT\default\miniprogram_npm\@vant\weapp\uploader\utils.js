var e = function() {
  return (e = Object.assign || function(e) {
    for (var t, i = 1, r = arguments.length; i < r; i++)
      for (var u in t = arguments[i]) Object.prototype.hasOwnProperty.call(t, u) && (e[u] = t[u]);
    return e
  }).apply(this, arguments)
};
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.chooseFile = exports.isVideoFile = exports.isImageFile = void 0;
var t = require("../common/utils"),
  i = require("../common/validator");
exports.isImageFile = function(e) {
  return null != e.isImage ? e.isImage : e.type ? "image" === e.type : !!e.url && (0, i.isImageUrl)(e.url)
}, exports.isVideoFile = function(e) {
  return null != e.isVideo ? e.isVideo : e.type ? "video" === e.type : !!e.url && (0, i.isVideoUrl)(e.url)
}, exports.chooseFile = function(i) {
  var r = i.accept,
    u = i.multiple,
    a = i.capture,
    o = i.compressed,
    n = i.maxDuration,
    s = i.sizeType,
    c = i.camera,
    p = i.maxCount,
    m = i.mediaType,
    l = i.extension;
  return new Promise((function(i, h) {
    switch (r) {
      case "image":
        wx.chooseMedia({
          count: u ? Math.min(p, 9) : 1,
          mediaType: ["image"],
          sourceType: a,
          maxDuration: n,
          sizeType: s,
          camera: c,
          success: function(r) {
            return i(function(i) {
              return i.tempFiles.map((function(i) {
                return e(e({}, (0, t.pickExclude)(i, ["path"])), {
                  type: "image",
                  url: i.tempFilePath,
                  thumb: i.tempFilePath
                })
              }))
            }(r))
          },
          fail: h
        });
        break;
      case "media":
        wx.chooseMedia({
          count: u ? Math.min(p, 9) : 1,
          mediaType: m,
          sourceType: a,
          maxDuration: n,
          sizeType: s,
          camera: c,
          success: function(r) {
            return i(function(i) {
              return i.tempFiles.map((function(r) {
                return e(e({}, (0, t.pickExclude)(r, ["fileType", "thumbTempFilePath", "tempFilePath"])), {
                  type: i.type,
                  url: r.tempFilePath,
                  thumb: "video" === i.type ? r.thumbTempFilePath : r.tempFilePath
                })
              }))
            }(r))
          },
          fail: h
        });
        break;
      case "video":
        wx.chooseVideo({
          sourceType: a,
          compressed: o,
          maxDuration: n,
          camera: c,
          success: function(r) {
            return i(function(i) {
              return [e(e({}, (0, t.pickExclude)(i, ["tempFilePath", "thumbTempFilePath", "errMsg"])), {
                type: "video",
                url: i.tempFilePath,
                thumb: i.thumbTempFilePath
              })]
            }(r))
          },
          fail: h
        });
        break;
      default:
        wx.chooseMessageFile(e(e({
          count: u ? p : 1,
          type: r
        }, l ? {
          extension: l
        } : {}), {
          success: function(r) {
            return i(function(i) {
              return i.tempFiles.map((function(i) {
                return e(e({}, (0, t.pickExclude)(i, ["path"])), {
                  url: i.path
                })
              }))
            }(r))
          },
          fail: h
        }))
    }
  }))
};