var e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  n = require("../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  a = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  r = require("../../../71D07D80549B04BF17B615870C540D65.js");
require("../../../03E40C94549B04BF6582649340450D65.js").mount();
var o = require("../../../A5622344549B04BFC3044B435F450D65.js");
(0, n.preloadFonts)(), Page({
  data: {
    statusHeaderBarHeight: a.statusHeaderBarHeight,
    imgUrl: a.imgUrl,
    imgVersion: a.imgVersion,
    screenInfo: (0, n.getScreenInfo)(),
    isLike: !1,
    isDel: "rank",
    userInfo: {},
    storyId: "",
    detailInfo: {}
  },
  onLoad: function(a) {
    var i = this;
    return t(e().mark((function t() {
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            if (console.log("details", a), null != a && a.isDel && i.setData({
                isDel: a.isDel
              }), null != a && a.storyId && i.setData({
                storyId: a.storyId
              }), (0, n.getAccessToken)() && !(0, n.isTokenExpired)()) {
              e.next = 6;
              break
            }
            return e.next = 6, (0, r.getLogin)();
          case 6:
            i.getUserInfoFn(), i.getStoryDetailFn(), o.pageView({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "rank" === a.isDel ? "故事详情" : "故事详情-我的",
              page_name: "rank" === a.isDel ? "故事详情" : "故事详情-我的",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/details/index"
            });
          case 9:
          case "end":
            return e.stop()
        }
      }), t)
    })))()
  },
  getUserInfoFn: function() {
    var n = this;
    return t(e().mark((function t() {
      var a, o, i;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.prev = 0, e.next = 3, (0, r.getUserInfo)();
          case 3:
            a = e.sent, o = a.code, i = a.data, 200 === o && n.setData({
              userInfo: i.userInfo
            }), e.next = 12;
            break;
          case 9:
            e.prev = 9, e.t0 = e.catch(0), console.log("index userInfo error: ", e.t0);
          case 12:
          case "end":
            return e.stop()
        }
      }), t, null, [
        [0, 9]
      ])
    })))()
  },
  getStoryDetailFn: function() {
    var n = this;
    return t(e().mark((function t() {
      var a, o, i;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.prev = 0, e.next = 3, (0, r.getStoryDetail)({
              storyId: n.data.storyId
            });
          case 3:
            a = e.sent, o = a.code, i = a.data, 200 === o && (i ? (i.likeNub = n.formatLikes(i.likeNub), n.setData({
              detailInfo: i
            })) : (wx.showToast({
              mask: !0,
              icon: "none",
              title: "该故事正在审核中",
              duration: 2e3
            }), setTimeout((function() {
              wx.redirectTo({
                url: "/cny/pages/home/<USER>"
              })
            }), 2e3))), e.next = 12;
            break;
          case 9:
            e.prev = 9, e.t0 = e.catch(0), console.log("getStoryDetail:", e.t0);
          case 12:
          case "end":
            return e.stop()
        }
      }), t, null, [
        [0, 9]
      ])
    })))()
  },
  formatLikes: function(e) {
    return e >= 1e4 ? (e / 1e4).toFixed(1) + "w" : e.toString()
  },
  handleDel: function() {
    this.selectComponent("#c-75-del").openMask(this.data.storyId)
  },
  handleConfirm: function(e) {
    e.detail.flag && wx.navigateBack({})
  },
  handleLikeStory: function() {
    var n = this;
    return t(e().mark((function t() {
      var a, i, s, l, u, c;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            if (e.prev = 0, a = n.data.userInfo.id, i = n.data.detailInfo, s = i.userId, l = i.likeFlag, console.log("likeFlag: ", l), a !== s) {
              e.next = 7;
              break
            }
            return wx.showToast({
              icon: "none",
              title: "不能给自己点赞"
            }), e.abrupt("return");
          case 7:
            if (0 !== Number(l)) {
              e.next = 16;
              break
            }
            return wx.showLoading({
              mask: !0
            }), e.next = 11, (0, r.likeStory)({
              storyId: n.data.storyId
            });
          case 11:
            u = e.sent, c = u.code, u.data, 200 === c && n.getStoryDetailFn(), wx.hideLoading();
          case 16:
            o.pageClickEvent({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "rank" === n.data.isDel ? "故事详情" : "故事详情-我的",
              page_name: "rank" === n.data.isDel ? "故事详情" : "故事详情-我的",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/details/index",
              button_name: "故事详情-删除"
            }), e.next = 23;
            break;
          case 19:
            e.prev = 19, e.t0 = e.catch(0), console.log(e.t0), wx.hideLoading();
          case 23:
          case "end":
            return e.stop()
        }
      }), t, null, [
        [0, 19]
      ])
    })))()
  },
  handleShare: function() {
    o.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "rank" === this.data.isDel ? "故事详情" : "故事详情-我的",
      page_name: "rank" === this.data.isDel ? "故事详情" : "故事详情-我的",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/details/index",
      button_name: "故事详情-分享"
    })
  },
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return {
      title: a.shareOptions.title,
      path: "/cny/pages/details/index?storyId=" + this.data.storyId + "&isDel=rank",
      imageUrl: a.shareOptions.imageUrl
    }
  }
});