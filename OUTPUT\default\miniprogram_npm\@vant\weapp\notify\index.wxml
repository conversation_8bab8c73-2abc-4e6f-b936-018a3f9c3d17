<van-transition bind:tap="onTap" customClass="van-notify__container" customStyle="{{computed.rootStyle( {zIndex:zIndex,top:top} )}}" name="slide-down" show="{{show}}">
    <view class="van-notify van-notify--{{type}}" style="{{computed.notifyStyle( {background:background,color:color} )}}">
        <view style="height:{{statusBarHeight}}px" wx:if="{{safeAreaInsetTop}}"></view>
        <text>{{message}}</text>
    </view>
</van-transition>

<wxs module="utils" src="..\wxs\utils.wxs"/>
<wxs module="computed" src="index.wxs"/>