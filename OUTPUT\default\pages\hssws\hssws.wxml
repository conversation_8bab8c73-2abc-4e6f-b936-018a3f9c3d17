<view class="hssws">
    <view class="hssws_banner">
        <view class="hssws_banner_box">
            <view class="hssws_banner_box_x"></view>
            <view bindtap="changeTab" class="{{tabIndex==index?'hssws_banner_box_itemAct':'hssws_banner_box_item'}}" data-item="{{item}}" wx:for="{{tabList}}" wx:key="index">{{item.value}}<view class="hssws_banner_box_index" wx:if="{{tabIndex==index}}"></view>
            </view>
        </view>
    </view>
    <view class="hssws_bot">
        <block wx:if="{{tabIndex==0}}">
            <view bindtap="gotoInfo" class="EnergyMall_list_item" data-item="{{item}}" wx:for="{{list}}" wx:key="index">
                <view class="EnergyMall_list_item_top">
                    <image mode="" src="{{item.activityImg}}"></image>
                    <view class="EnergyMall_list_item_radio">
                        <view class="EnergyMall_list_item_radio_num">{{item.consumeIntegral}}</view>
                        <view class="EnergyMall_list_item_radio_text">申请能量</view>
                    </view>
                </view>
                <view class="EnergyMall_list_item_bot2">
                    <view class="EnergyMall_list_item_bot2_r">{{item.activityName}}</view>
                </view>
            </view>
        </block>
        <block wx:else>
            <view bindtap="gotoInfo" class="hssws_bot_sq" data-item="{{item}}" wx:for="{{list}}" wx:key="index">
                <view class="hssws_bot_sq_img">
                    <image mode="" src="{{item.activityImg}}"></image>
                </view>
                <view class="hssws_bot_sq_c">
                    <view class="hssws_bot_sq_c_name">{{item.activityName}}</view>
                    <view class="hssws_bot_sq_c_time" wx:if="{{item.lotteryFlag}}">已于 {{item.lotteryOpenTime}} 开奖</view>
                    <view class="hssws_bot_sq_c_time" wx:else>将于 {{item.lotteryOpenTime}} 开奖</view>
                    <view class="hssws_bot_sq_c_b">
                        <view class="hssws_bot_sq_c_b_icon">
                            <image mode="" src="{{img}}newVersion/046.png"></image>
                        </view>
                        <view class="hssws_bot_sq_c_b_num">{{item.consumeIntegral}}<text style="font-size:24rpx;">能量</text>
                        </view>
                    </view>
                </view>
                <view class="hssws_bot_sq_r" wx:if="{{item.lotteryFlag}}">
                    <image mode="" src="{{img}}sws/004.png"></image>
                </view>
                <view class="hssws_bot_sq_r" wx:else>
                    <image mode="" src="{{img}}newVersion/045.png"></image>
                </view>
            </view>
        </block>
    </view>
    <footer class="footer"></footer>
</view>
