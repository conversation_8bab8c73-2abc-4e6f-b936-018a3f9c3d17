var e, n = require("./@babel/runtime/helpers/regeneratorRuntime.js"),
  t = require("./@babel/runtime/helpers/asyncToGenerator.js"),
  i = require("./@babel/runtime/helpers/objectSpread2.js"),
  s = {
    uid: null,
    startTime: null,
    pageInitInfo: {},
    sessionId: "",
    options: {
      baseUrl: "https://conviviality.omnimkt.com/monitor/moitor_api",
      unionid: "",
      openid: "",
      projectId: "",
      projectName: "",
      api_key: "",
      currentPageUrl: "",
      ad_id: "",
      url_link_id: "",
      short_link_id: "",
      debug: !1
    },
    init: function(e) {
      var n = this;
      e.openid ? this.uid = e.openid : this.uid = wx.getStorageSync("uid"), this.uid || (this.uid = Math.random().toString(36).substr(2), wx.setStorageSync("uid", this.uid)), e.debug && console.log("大数据调试开启"), this.options = Object.assign(this.options, e), this.options.ad_id && this.eventAddAdmerics(), this.options.url_link_id && this.eventAddUrlLinkMetrics(), this.options.short_link_id && this.eventAddShortLinkMetrics(), wx.onError((function(e) {
        n.sendEvent("/event/error", {
          message: e
        })
      })), this.recordPageView(e), this.recordReferrer(e, (function() {
        setTimeout((function() {
          n.getAddress()
        }), 3e3)
      }))
    },
    recordPageView: function(e) {
      this.pageInitInfo = e
    },
    recordPageChange: function(e) {
      this.sendEvent("/pv", {
        params: e,
        scene: "wx-" + this.pageInitInfo.scene
      }).then((function(e) {
        e.code
      }))
    },
    clickEvent: function(e) {
      this.sendEvent("/event/scene", e)
    },
    pageView: function(e) {
      var n = this;
      setTimeout((function() {
        n.sendEvent("/event/pageView", e)
      }), 500)
    },
    pageClickEvent: function(e) {
      this.sendEvent("/event/pageClickEvent", e)
    },
    pagePopupViews: function(e) {
      var n = this;
      setTimeout((function() {
        n.sendEvent("/event/pagePopupViews", e)
      }), 500)
    },
    pagePopupClickEvents: function(e) {
      this.sendEvent("/event/pagePopupClickEvent", e)
    },
    getAddress: function(e) {
      var n = this;
      this.sendEvent("/getIpAddress", e).then((function(e) {
        n.getDeviceInfo(e)
      }))
    },
    getDeviceInfo: function(e) {
      var n, t, s = wx.getSystemInfoSync();
      200 === e.code ? null !== (n = e.data) && void 0 !== n && n.city ? this.sendEvent("/event/device", i(i({}, s), {}, {
        location: e.data.city
      })) : null !== (t = e.data) && void 0 !== t && t.province ? this.sendEvent("/event/device", i(i({}, s), {}, {
        location: e.data.province
      })) : this.sendEvent("/event/device", s) : this.sendEvent("/event/device", s)
    },
    recordReferrer: function(e, n) {
      var t;
      this.sendEvent("/event/referrer", {
        referrer: (null == e || null === (t = e.referrerInfo) || void 0 === t ? void 0 : t.appId) || e.openid
      }).then((function(e) {
        200 === e.code && n && n()
      }))
    },
    recordConversion: function(e, n) {
      this.sendEvent("/event/conversion", {
        eventType: e,
        data: n
      })
    },
    createSession: (e = t(n().mark((function e(t) {
      var i = this;
      return n().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            this.sendEvent("/event/createSession").then((function(e) {
              200 === e.code && (i.sessionId = e.data.sessionId, t && t())
            }));
          case 1:
          case "end":
            return e.stop()
        }
      }), e, this)
    }))), function(n) {
      return e.apply(this, arguments)
    }),
    endSession: function() {
      var e = this;
      this.sendEvent("/event/endSession", {
        sessionId: this.sessionId
      }).then((function(n) {
        e.sessionId = ""
      }))
    },
    eventShare: function() {
      this.sendEvent("/event/share")
    },
    eventAddAdmerics: function() {
      this.sendEvent("/event/addAdMetrics")
    },
    eventAddUrlLinkMetrics: function() {
      this.sendEvent("/event/addUrlLinkMetrics")
    },
    eventAddShortLinkMetrics: function() {
      this.sendEvent("/event/addShortLinkMetrics")
    },
    sendEvent: function(e) {
      var n = this,
        t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
      return this.options.debug && console.log("Sending event:", e, t), new Promise((function(i, s) {
        wx.request({
          url: n.options.baseUrl + e,
          method: "POST",
          header: {
            "Content-Type": "application/json",
            "x-api-key": n.options.api_key
          },
          data: Object.assign({
            uid: n.uid,
            openid: n.options.openid,
            unionid: n.options.unionid,
            pageUrl: o(),
            projectId: n.options.projectId,
            projectName: n.options.projectName,
            sessionId: n.sessionId,
            ad_id: n.options.ad_id,
            url_link_id: n.options.url_link_id,
            short_link_id: n.options.short_link_id
          }, t),
          success: function(e) {
            200 === e.statusCode && i(e.data)
          },
          fail: function(e) {
            s(e)
          }
        })
      }))
    }
  };

function o() {
  var e = getCurrentPages();
  return 0 === e.length ? "" : e[e.length - 1].route
}
module.exports = s;