<view class="addReceiving">
    <view class="addReceiving_top">
        <view class="addReceiving_top_item">
            <view class="addReceiving_top_items">
                <view class="addReceiving_top_item_l">
                    <text decode="{{true}}">收&nbsp;&nbsp;件&nbsp;&nbsp;人</text>
                </view>
                <view class="addReceiving_top_item_r">
                    <input bindinput="setUserName" class="inputPhones" maxlength="20" placeholder="请输入收件人" placeholderClass="placeholder" type="text" value="{{UserName}}"></input>
                </view>
            </view>
        </view>
        <view class="addReceiving_top_item">
            <view class="addReceiving_top_items">
                <view class="addReceiving_top_item_l">联系方式</view>
                <view class="addReceiving_top_item_r">
                    <input bindinput="setUserPhone" class="inputPhones" maxlength="11" placeholder="请输入联系方式" placeholderClass="placeholder" type="number" value="{{UserPhone}}"></input>
                </view>
            </view>
        </view>
        <view class="addReceiving_top_item">
            <view class="addReceiving_top_items">
                <view class="addReceiving_top_item_l">
                    <text decode="{{true}}">所在地区</text>
                </view>
                <view class="addReceiving_top_item_r">
                    <view bindtap="changeShowArea" style="width:384rpx;">
                        <input class="inputPhones" disabled="true" placeholder="请选择所在地区" placeholderClass="placeholder" type="text" value="{{area}}"></input>
                    </view>
                    <view class="addReceiving_top_item_r_icon">
                        <image src="http://dm-assets.supercarrier8.com/hgst-mp/grayJian.png"></image>
                    </view>
                    <view class="addReceiving_top_item_r_x"></view>
                    <view bindtap="gotoChooseAdd" class="addReceiving_top_item_r_addIcon">
                        <image mode="" src="{{img}}newVersion/026.png"></image>
                    </view>
                </view>
            </view>
        </view>
        <view class="addReceiving_top_item">
            <view class="addReceiving_top_items">
                <view class="addReceiving_top_item_l">
                    <text decode="{{true}}">详细地址</text>
                </view>
                <view class="addReceiving_top_item_r">
                    <input bindinput="setAreaAddress" class="inputPhones" maxlength="150" placeholder="请输入详细地址" placeholderClass="placeholder" type="text" value="{{AreaAddress}}"></input>
                </view>
            </view>
        </view>
        <view class="addReceiving_top_moren">
            <view bindtap="onChange" class="addReceiving_top_moren_L">
                <image mode="" src="{{img}}newVersion/025.png" wx:if="{{!checked}}"></image>
                <image mode="" src="{{img}}newVersion/024.png" wx:else></image>
            </view>
            <view class="addReceiving_top_moren_r">设为默认方式</view>
        </view>
    </view>
    <view class="addReceiving_bot">
        <footer></footer>
        <view bindtap="setAddress" class="addReceiving_button">保存</view>
    </view>
    <CityPicker bind:onConfirm="onCityPicker" county="{{county}}" isShowArea="{{isShowArea}}" province="{{city}}" town="{{town}}"></CityPicker>
</view>
