Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var e = require("./@babel/runtime/helpers/typeof.js"),
  t = require("./@babel/runtime/helpers/createForOfIteratorHelper.js"),
  n = require("./@babel/runtime/helpers/slicedToArray.js"),
  i = require("./@babel/runtime/helpers/classCallCheck.js"),
  a = require("./@babel/runtime/helpers/createClass.js"),
  o = require("0CADEA11549B04BF6ACB8216E4250D65.js"),
  r = require("52B91EA4549B04BF34DF76A32B050D65.js"),
  c = 6291456,
  l = {},
  s = function() {
    function e() {
      i(this, e), getApp().PAINTER_MAX_LRU_SPACE && (c = getApp().PAINTER_MAX_LRU_SPACE), wx.getStorage({
        key: "savedFiles",
        success: function(e) {
          e.data && (l = e.data)
        }
      })
    }
    return a(e, [{
      key: "download",
      value: function(e, t) {
        return new Promise((function(i, a) {
          if (e && o.isValidUrl(e)) {
            var c = function(e) {
              if (o.isDataUrl(e)) {
                var t = /data:image\/(\w+);base64,(.*)/.exec(e) || [],
                  i = n(t, 3),
                  a = i[1],
                  c = i[2];
                return "".concat(r.hex_sha1(c), ".").concat(a)
              }
              return e
            }(e);
            if (t) {
              var s = function(e) {
                if (!l[e]) return;
                return l[e].time = (new Date).getTime(), wx.setStorage({
                  key: "savedFiles",
                  data: l
                }), l[e]
              }(c);
              s ? -1 !== s.path.indexOf("//usr/") ? wx.getFileInfo({
                filePath: s.path,
                success: function() {
                  i(s.path)
                },
                fail: function(n) {
                  console.error("base64 file broken, ".concat(JSON.stringify(n))), f(e, t).then((function(e) {
                    i(e)
                  }), (function() {
                    a()
                  }))
                }
              }) : wx.getSavedFileInfo({
                filePath: s.path,
                success: function(e) {
                  i(s.path)
                },
                fail: function(n) {
                  console.error("the file is broken, redownload it, ".concat(JSON.stringify(n))), u(e, t).then((function(e) {
                    i(e)
                  }), (function() {
                    a()
                  }))
                }
              }) : o.isOnlineUrl(e) ? u(e, t).then((function(e) {
                i(e)
              }), (function() {
                a()
              })) : o.isDataUrl(e) && f(e, t).then((function(e) {
                i(e)
              }), (function() {
                a()
              }))
            } else wx.getFileInfo({
              filePath: c,
              success: function() {
                i(e)
              },
              fail: function() {
                o.isOnlineUrl(e) ? u(e, t).then((function(e) {
                  i(e)
                }), (function() {
                  a()
                })) : o.isDataUrl(e) && f(e, t).then((function(e) {
                  i(e)
                }), (function() {
                  a()
                }))
              }
            })
          } else i(e)
        }))
      }
    }]), e
  }();

function f(e, t) {
  return new Promise((function(i, a) {
    var o = /data:image\/(\w+);base64,(.*)/.exec(e) || [],
      c = n(o, 3),
      l = c[1],
      s = c[2];
    if (!l) return console.error("base parse failed"), void a();
    var f = "".concat(r.hex_sha1(s), ".").concat(l),
      u = "".concat(wx.env.USER_DATA_PATH, "/").concat(f),
      h = wx.base64ToArrayBuffer(s.replace(/[\r\n]/g, ""));
    wx.getFileSystemManager().writeFile({
      filePath: u,
      data: h,
      encoding: "binary",
      success: function() {
        wx.getFileInfo({
          filePath: u,
          success: function(e) {
            var n = e.size;
            t ? v(n).then((function() {
              d(f, n, u, !0).then((function(e) {
                i(e)
              }))
            }), (function() {
              i(u)
            })) : i(u)
          },
          fail: function(e) {
            console.error("getFileInfo ".concat(u, " failed, ").concat(JSON.stringify(e))), i(u)
          }
        })
      },
      fail: function(e) {
        console.log(e)
      }
    })
  }))
}

function u(e, t) {
  return new Promise((function(n, i) {
    (e.startsWith("cloud://") ? wx.cloud.downloadFile : wx.downloadFile)({
      url: e,
      fileID: e,
      success: function(a) {
        if (200 !== a.statusCode) return console.error("downloadFile ".concat(e, " failed res.statusCode is not 200")), void i();
        var o = a.tempFilePath;
        wx.getFileInfo({
          filePath: o,
          success: function(i) {
            var a = i.size;
            t ? v(a).then((function() {
              d(e, a, o).then((function(e) {
                n(e)
              }))
            }), (function() {
              n(o)
            })) : n(o)
          },
          fail: function(e) {
            console.error("getFileInfo ".concat(a.tempFilePath, " failed, ").concat(JSON.stringify(e))), n(a.tempFilePath)
          }
        })
      },
      fail: function(e) {
        console.error("downloadFile failed, ".concat(JSON.stringify(e), " ")), i()
      }
    })
  }))
}

function d(e, t, n) {
  var i = arguments.length > 3 && void 0 !== arguments[3] && arguments[3];
  return new Promise((function(a, o) {
    if (i) {
      var r = l.totalSize ? l.totalSize : 0;
      return l[e] = {}, l[e].path = n, l[e].time = (new Date).getTime(), l[e].size = t, l.totalSize = t + r, wx.setStorage({
        key: "savedFiles",
        data: l
      }), void a(n)
    }
    wx.saveFile({
      tempFilePath: n,
      success: function(n) {
        var i = l.totalSize ? l.totalSize : 0;
        l[e] = {}, l[e].path = n.savedFilePath, l[e].time = (new Date).getTime(), l[e].size = t, l.totalSize = t + i, wx.setStorage({
          key: "savedFiles",
          data: l
        }), a(n.savedFilePath)
      },
      fail: function(t) {
        console.error("saveFile ".concat(e, " failed, then we delete all files, ").concat(JSON.stringify(t))), a(n), h()
      }
    })
  }))
}

function h() {
  wx.removeStorage({
    key: "savedFiles",
    success: function() {
      wx.getSavedFileList({
        success: function(e) {
          g(e.fileList)
        },
        fail: function(e) {
          console.error("getSavedFileList failed, ".concat(JSON.stringify(e)))
        }
      })
    }
  })
}

function v(e) {
  return e > c ? Promise.reject() : new Promise((function(n, i) {
    var a = l.totalSize ? l.totalSize : 0;
    if (e + a <= c) n();
    else {
      var o = [],
        r = JSON.parse(JSON.stringify(l));
      delete r.totalSize;
      var s, f = Object.keys(r).sort((function(e, t) {
          return r[e].time - r[t].time
        })),
        u = t(f);
      try {
        for (u.s(); !(s = u.n()).done;) {
          var d = s.value;
          if (a -= l[d].size, o.push(l[d].path), delete l[d], a + e < c) break
        }
      } catch (e) {
        u.e(e)
      } finally {
        u.f()
      }
      l.totalSize = a, wx.setStorage({
        key: "savedFiles",
        data: l,
        success: function() {
          o.length > 0 && g(o), n()
        },
        fail: function(e) {
          console.error("doLru setStorage failed, ".concat(JSON.stringify(e))), i()
        }
      })
    }
  }))
}

function g(n) {
  var i, a = t(n);
  try {
    var o = function() {
      var t = i.value,
        n = t;
      "object" === e(t) && (n = t.filePath), -1 !== n.indexOf("//usr/") ? wx.getFileSystemManager().unlink({
        filePath: n,
        fail: function(e) {
          console.error("removeSavedFile ".concat(t, " failed, ").concat(JSON.stringify(e)))
        }
      }) : wx.removeSavedFile({
        filePath: n,
        fail: function(e) {
          console.error("removeSavedFile ".concat(t, " failed, ").concat(JSON.stringify(e)))
        }
      })
    };
    for (a.s(); !(i = a.n()).done;) o()
  } catch (e) {
    a.e(e)
  } finally {
    a.f()
  }
}
exports.default = s;