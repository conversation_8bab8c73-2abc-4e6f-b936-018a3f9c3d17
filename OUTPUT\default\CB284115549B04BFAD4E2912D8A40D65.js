Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = s;
var e = require("./@babel/runtime/helpers/regeneratorRuntime.js"),
  t = require("./@babel/runtime/helpers/asyncToGenerator.js"),
  n = require("6D59C885549B04BF0B3FA082E5940D65.js"),
  o = require("95D1B746549B04BFF3B7DF41DA740D65.js"),
  r = {
    showLoadingText: "数据加载中.."
  },
  a = {
    "Content-Type": "application/json"
  };
(new Date).getTime(), wx.getStorageSync("expires_in");

function s(e) {
  var t = Object.assign(r, e);
  return console.log("接口的配置: ", t), (0, o.getAccessToken)() && (a.Authorization = wx.getStorageSync("access_token")), new Promise((function(e, o) {
    wx.request({
      url: n.ApiBaseUri + t.url,
      timeout: 6e4,
      method: t.method || "get",
      header: a,
      data: t.data || {},
      success: function(n) {
        if (console.log("接口返回的参数: ", t.url, n.data), 200 !== n.statusCode) 502 === n.statusCode ? wx.showToast({
          icon: "none",
          title: "当前访问人数过多"
        }) : o("接口错误");
        else {
          var r = n.data,
            a = r.code,
            s = (r.data, r.msg);
          200 == a || 10008 == a || 10009 == a ? e(n.data) : 401 == a || 403 == a ? function() {
            i.apply(this, arguments)
          }() : 500 === a ? wx.showToast({
            icon: "none",
            title: "当前访问人数过多"
          }) : 20003 === a || 20002 === a ? wx.showToast({
            icon: "none",
            title: s
          }) : 10007 === a ? wx.showToast({
            icon: "none",
            title: "当前积分不足"
          }) : wx.showToast({
            icon: "none",
            title: "当前访问人数过多"
          })
        }
      },
      fail: function(e) {
        o(e)
      },
      complete: function(e) {}
    })
  }))
}

function i() {
  return (i = t(e().mark((function t() {
    var r, a, i;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.next = 2, (0, o.getJsCode)();
        case 2:
          if (!(r = e.sent)) {
            e.next = 15;
            break
          }
          return e.next = 6, s({
            url: "/snoopy/login",
            method: "POST",
            data: {
              authorizerAppid: n.APPID,
              jsCode: r
            }
          });
        case 6:
          if (!(a = e.sent)) {
            e.next = 14;
            break
          }
          return i = a.data, (0, o.setAccessToken)(i.token), wx.setStorageSync("token_expire", Date.now()), e.abrupt("return", a);
        case 14:
          throw new Error("获取新 token 失败");
        case 15:
        case "end":
          return e.stop()
      }
    }), t)
  })))).apply(this, arguments)
}