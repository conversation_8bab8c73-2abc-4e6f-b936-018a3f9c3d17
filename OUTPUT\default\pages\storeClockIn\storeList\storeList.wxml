<view class="storeList">
    <view class="ClockInRecords_top">
        <view bindtap="changeTopTab" class="top_item" data-item="{{item}}" wx:for="{{topList}}" wx:key="index">
            <view class="top_item_choose" wx:if="{{topIndex==item.index}}">{{item.name}}</view>
            <view class="top_item_noChoose" wx:else>{{item.name}}</view>
            <view class="top_item_xian" wx:if="{{topIndex==item.index}}"></view>
        </view>
    </view>
    <block wx:if="{{list.length!=0}}">
        <view class="storeList_item" wx:for="{{list}}" wx:key="index">
            <view bindtap="gotoWebMap" class="storeList_item_icon" data-item="{{item}}">
                <image mode="" src="{{img}}store/storeIcon.png"></image>
            </view>
            <view class="storeList_item_name">{{item.name}}</view>
            <view class="storeList_item_phone">
                <text style="color:#8E8E8E;">电话：</text>{{item.f2}}</view>
            <view class="storeList_item_phone">
                <text style="color:#8E8E8E;">地址：</text>{{item.address}}</view>
        </view>
    </block>
    <view class="pageNoList" wx:else>
        <view class="pageNoList_img">
            <image mode="" src="{{img}}noListIcon.png"></image>
        </view>
        <view class="pageNoList_text">附近暂未发现可参与活动门店</view>
    </view>
    <footer class="footer"></footer>
</view>
