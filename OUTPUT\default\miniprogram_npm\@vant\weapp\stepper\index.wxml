<view class="{{utils.bem( 'stepper',[theme] )}} custom-class">
    <view bind:tap="onTap" bind:touchend="onTouchEnd" bind:touchstart="onTouchStart" class="minus-class {{utils.bem( 'stepper__minus',{disabled:disabled||disableMinus||currentValue<=min} )}}" data-type="minus" hoverClass="van-stepper__minus--hover" hoverStayTime="70" style="{{computed.buttonStyle( {buttonSize:buttonSize} )}}" wx:if="{{showMinus}}">
        <slot name="minus"></slot>
    </view>
    <input alwaysEmbed="{{alwaysEmbed}}" bind:blur="onBlur" bind:focus="onFocus" bindinput="onInput" class="input-class {{utils.bem( 'stepper__input',{disabled:disabled||disableInput} )}}" disabled="{{disabled||disableInput}}" focus="{{focus}}" style="{{computed.inputStyle( {buttonSize:buttonSize,inputWidth:inputWidth} )}}" type="{{integer?'number':'digit'}}" value="{{currentValue}}"></input>
    <view bind:tap="onTap" bind:touchend="onTouchEnd" bind:touchstart="onTouchStart" class="plus-class {{utils.bem( 'stepper__plus',{disabled:disabled||disablePlus||currentValue>=max} )}}" data-type="plus" hoverClass="van-stepper__plus--hover" hoverStayTime="70" style="{{computed.buttonStyle( {buttonSize:buttonSize} )}}" wx:if="{{showPlus}}">
        <slot name="plus"></slot>
    </view>
</view>

<wxs module="utils" src="..\wxs\utils.wxs"/>
<wxs module="computed" src="index.wxs"/>