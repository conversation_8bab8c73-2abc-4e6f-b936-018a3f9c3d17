.sk {
    background: #fff;
    box-sizing: border-box;
    min-height: 100vh;
    padding-bottom: 180rpx;
    width: 100%
}

.sk_top {
    height: 362rpx;
    width: 100%
}

.sk_bot {
    box-sizing: border-box;
    height: calc(100vh - 362rpx);
    overflow: hidden;
    padding-left: 24rpx;
    position: relative;
    width: 100%
}

.sk_bot_text {
    color: #202020;
    font-family: Source Han Sans CN;
    font-size: 36rpx;
    margin-top: 10rpx
}

.sk_bot_text_title {
    font-weight: 700
}

.oldCodeStyle {
    margin-top: 50rpx
}

.sk_bot_rule {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 24rpx;
    font-weight: 400;
    height: 39rpx;
    line-height: 46rpx;
    position: absolute;
    right: 33rpx;
    text-align: center;
    top: 40rpx;
    width: 39rpx
}

.sk_bot_resultImg {
    border-radius: 50%;
    height: 160rpx;
    margin: 26rpx auto 36rpx;
    width: 160rpx
}

.sk_bot_codeNum {
    color: #000;
    font-family: Source <PERSON> Sans CN;
    font-size: 28rpx;
    font-weight: 400;
    margin-bottom: 10rpx;
    margin-left: 3rpx
}

.sk_bot_codeNumState {
    color: #2e9341;
    font-size: 32rpx;
    font-weight: 700
}

.sk_bot_codeText {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 32rpx;
    font-weight: 400;
    margin-bottom: 10rpx
}

.sk_bot_codeSeach {
    background: #f9f9f9;
    box-sizing: border-box;
    margin-bottom: 16rpx;
    padding: 17rpx;
    width: 706rpx
}

.sk_bot_codeSeach_time {
    color: #646464;
    font-family: Source Han Sans CN;
    font-size: 24rpx;
    font-weight: 400
}

.sk_bot_button {
    background: #000;
    border-radius: 43rpx;
    color: #fff;
    font-family: Source Han Sans CN;
    font-size: 40rpx;
    font-weight: 700;
    height: 86rpx;
    line-height: 86rpx;
    margin: 40rpx auto 0;
    position: relative;
    text-align: center;
    width: 526rpx
}

.buttonPhone {
    background: none;
    height: 86rpx!important;
    left: 0;
    position: absolute;
    top: 0;
    width: 526rpx!important
}

.prop {
    background: rgba(0,0,0,.6);
    height: 100vh;
    left: 0;
    position: fixed;
    right: 0;
    width: 100%
}
