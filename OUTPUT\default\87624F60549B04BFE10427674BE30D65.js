Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var e = require("./@babel/runtime/helpers/regeneratorRuntime.js"),
  r = require("./@babel/runtime/helpers/asyncToGenerator.js"),
  t = {
    token: "",
    openid: "",
    userInfo: {},
    registerUserInfo: {},
    addressInfo: {
      province: "",
      city: "",
      district: "",
      adcode: "",
      formattedAddress: "",
      isChooseAdd: !1
    },
    goodsInfoId: "",
    orderChooseAddress: {},
    BMAP_GPS: {},
    userInfoGPS: {
      latitude: "",
      longitude: ""
    },
    orderChooseAddressState: !1,
    config: {}
  },
  o = function() {
    var o = r(e().mark((function r() {
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.abrupt("return", new Promise((function(e) {
              wx.request({
                url: "https://dm-assets.supercarrier8.com/wobei/config.json?v=" + (new Date).getTime(),
                success: function(r) {
                  t.config = r.data, e(r.data)
                }
              })
            })));
          case 1:
          case "end":
            return e.stop()
        }
      }), r)
    })));
    return function() {
      return o.apply(this, arguments)
    }
  }(),
  s = {
    data: t,
    getConfigJson: o
  };
exports.default = s;