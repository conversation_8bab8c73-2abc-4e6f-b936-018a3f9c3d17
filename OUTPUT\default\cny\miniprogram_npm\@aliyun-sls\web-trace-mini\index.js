var e = require("../../../../@babel/runtime/helpers/assertThisInitialized"),
  t = require("../../../../@babel/runtime/helpers/inherits"),
  r = require("../../../../@babel/runtime/helpers/createSuper"),
  n = require("../../../../@babel/runtime/helpers/regeneratorRuntime"),
  o = require("../../../../@babel/runtime/helpers/toConsumableArray"),
  i = require("../../../../@babel/runtime/helpers/defineProperty");
require("../../../../@babel/runtime/helpers/Objectentries");
var s = require("../../../../@babel/runtime/helpers/slicedToArray");
require("../../../../@babel/runtime/helpers/Arrayincludes");
var a, u, c = require("../../../../@babel/runtime/helpers/createForOfIteratorHelper"),
  l = require("../../../../@babel/runtime/helpers/classCallCheck"),
  f = require("../../../../@babel/runtime/helpers/createClass"),
  p = require("../../../../@babel/runtime/helpers/typeof");
module.exports = (a = {}, u = function(e, t) {
  if (!a[e]) return require(t);
  if (!a[e].status) {
    var r = a[e].m;
    r._exports = r._tempexports;
    var n = Object.getOwnPropertyDescriptor(r, "exports");
    n && n.configurable && Object.defineProperty(r, "exports", {
      set: function(e) {
        "object" === p(e) && e !== r._exports && (r._exports.__proto__ = e.__proto__, Object.keys(e).forEach((function(t) {
          r._exports[t] = e[t]
        }))), r._tempexports = e
      },
      get: function() {
        return r._tempexports
      }
    }), a[e].status = 1, a[e].func(a[e].req, r, r.exports)
  }
  return a[e].m.exports
}, function(e, t, r) {
  a[e] = {
    status: 0,
    func: t,
    req: r,
    m: {
      exports: {},
      _tempexports: {}
    }
  }
}(1739784025838, (function(a, u, h) {
  var d, b = function() {
      function e(t) {
        l(this, e), this.onFirstSubscribe = t, this.observers = []
      }
      return f(e, [{
        key: "subscribe",
        value: function(e) {
          var t = this;
          return !this.observers.length && this.onFirstSubscribe && (this.onLastUnsubscribe = this.onFirstSubscribe() || void 0), this.observers.push(e), {
            unsubscribe: function() {
              t.observers = t.observers.filter((function(t) {
                return e !== t
              })), !t.observers.length && t.onLastUnsubscribe && t.onLastUnsubscribe()
            }
          }
        }
      }, {
        key: "notify",
        value: function(e) {
          this.observers.forEach((function(t) {
            return t(e)
          }))
        }
      }]), e
    }(),
    v = Object.defineProperty,
    m = Object.defineProperties,
    g = Object.getOwnPropertyDescriptors,
    y = Object.getOwnPropertySymbols,
    S = Object.prototype.hasOwnProperty,
    O = Object.prototype.propertyIsEnumerable,
    R = function(e, t, r) {
      return t in e ? v(e, t, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: r
      }) : e[t] = r
    };

  function E(e, t) {
    return function() {
      try {
        return e.apply(void 0, arguments)
      } catch (e) {
        console.error(t, e)
      }
    }
  }

  function I(e, t) {
    return e.includes("/track?APIVersion=0.6.0") || e === function(e) {
      return e.host.startsWith("http://") || e.host.startsWith("https://") ? e.host + "/logstores/" + e.logstore + (e.stsPlugin ? "" : "/track?APIVersion=0.6.0") : "https://" + e.project + "." + e.host + "/logstores/" + e.logstore + (e.stsPlugin ? "" : "/track?APIVersion=0.6.0")
    }(t)
  }
  var _ = /^(?:([^:\/?#]+):\/\/)?((?:([^\/?#@]*)@)?([^\/?#:]*)(?:\:(\d*))?)?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n)*))?/i;

  function P(e) {
    var t = function(e) {
      try {
        return decodeURIComponent(e)
      } catch (t) {
        return unescape(e)
      }
    }(e || "").match(_);
    if (null == t) return null;
    var r = (t[3] || "").split(":"),
      n = r.length ? (t[2] || "").replace(/(.*\@)/, "") : t[2];
    return {
      uri: t[0],
      protocol: t[1],
      host: n,
      hostname: t[4],
      port: t[5],
      auth: t[3],
      user: r[0],
      password: r[1],
      path: t[6],
      search: t[7],
      hash: t[8]
    }
  }

  function N(e, t, r, n) {
    var o = {
      url: e,
      method: t,
      status_code: r
    };
    return null == n ? o : function(e, t) {
      return m(e, g(t))
    }(function(e, t) {
      for (var r in t || (t = {})) S.call(t, r) && R(e, r, t[r]);
      if (y) {
        var n, o = c(y(t));
        try {
          for (o.s(); !(n = o.n()).done;) r = n.value, O.call(t, r) && R(e, r, t[r])
        } catch (e) {
          o.e(e)
        } finally {
          o.f()
        }
      }
      return e
    }({}, o), {
      host: n.host,
      scheme: n.protocol
    })
  }

  function k(e, t) {
    for (var r = arguments.length, n = new Array(r > 2 ? r - 2 : 0), o = 2; o < r; o++) n[o - 2] = arguments[o];
    return !(null == t || !Array.isArray(t)) && t.some((function(t) {
      if ("function" == typeof t) try {
        return t.apply(void 0, [e].concat(n))
      } catch (e) {
        return console.error("user function callback threw an error:", e), !1
      }
      return "string" == typeof t ? e.includes(t) : "[object RegExp]" === Object.prototype.toString.call(t) && t.test(e)
    }))
  }

  function w(e, t) {
    return null == t || k(e, t)
  }

  function j() {
    return Date.now()
  }

  function T(e, t) {
    return t - e
  }
  var B = Array(32);

  function A(e) {
    for (var t = 0; t < 2 * e; t++) B[t] = Math.floor(16 * Math.random()) + 48, B[t] >= 58 && (B[t] += 39);
    return String.fromCharCode.apply(null, B.slice(0, 2 * e))
  }

  function M() {
    return A(16)
  }

  function C() {
    return A(8)
  }

  function q(e, t) {
    return "".concat(e, "-").concat(t, "-1")
  }

  function D(e, t) {
    return "00-".concat(e, "-").concat(t, "-01")
  }

  function x(e, t) {
    return "".concat(e, ":").concat(t, ":0:1")
  }
  var L = {
    b3: {
      name: "b3",
      getter: q
    },
    traceparent: {
      name: "traceparent",
      getter: D
    },
    uber: {
      name: "uber-trace-id",
      getter: x
    }
  };

  function U(e, t, r, n, o) {
    var i, a = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : "headers",
      u = null != r ? r : {};
    u[a] = null != (i = u[a]) ? i : {}, "Headers" === u[a].constructor.name ? (u[a].append("b3", q(e, t)), u[a].append("traceparent", D(e, t)), u[a].append("uber-trace-id", x(e, t)), o && u[a].append("X-B3-TraceId", e), o && u[a].append("X-B3-SpanId", t)) : (u[a].b3 = q(e, t), u[a].traceparent = D(e, t), u[a]["uber-trace-id"] = x(e, t), o && (u[a]["X-B3-TraceId"] = e), o && (u[a]["X-B3-SpanId"] = t));
    try {
      if (n)
        if (n instanceof Function) {
          var c = E(n, "run customTraceHeaders func error.")(e, t);
          if (c)
            for (var l = 0, f = Object.entries(c); l < f.length; l++) {
              var p = s(f[l], 2),
                h = p[0],
                d = p[1];
              "Headers" === u[a].constructor.name ? u[a].append(h, d) : u[a][h] = d
            }
        } else
          for (var b = 0, v = Object.entries(n); b < v.length; b++) {
            var m = s(v[b], 2),
              g = m[0],
              y = m[1];
            L[y] && ("Headers" === u[a].constructor.name ? u[a].append(g, L[y].getter(e, t)) : u[a][g] = L[y].getter(e, t))
          }
    } catch (e) {
      console.error("Failed utilizing customTraceHeaders.", e)
    }
    return u
  }

  function W(e, t, r) {
    null != e && Object.keys(e).map((function(n) {
      null == r || "" == r ? t[n] = e[n] : t["".concat(r, ".").concat(n)] = e[n]
    }))
  }
  var F = function(e) {
      return e.ERROR = "error", e.LOG = "log", e.LOCATION = "pv", e.API = "api", e.RESOURCE = "res", e.RESOURCE_ERROR = "res_err", e.PERF = "perf", e.CONSOLE_LOG = "console", e.DOM_CLICK = "dom_click", e
    }(F || {}),
    K = function(e) {
      return e.LOG_SEND = "LOG_SEND", e.BROWSER_SEND = "BROWSER_SEND", e.BASE_TRANSFORM = "BASE_TRANSFORM", e.BROWSER_BASE_TRANSFORM = "BROWSER_BASE_TRANSFORM", e.BROWSER_FETCH = "BROWSER_FETCH", e.BROWSER_XHR = "BROWSER_XHR", e.BROWSER_DOM = "BROWSER_DOM", e.BROWSER_LOCATION = "BROWSER_LOCATION", e.BROWSER_RUNTIME_ERROR = "BROWSER_RUNTIME_ERROR", e.BROWSER_CUSTOM_ERROR = "BROWSER_CUSTOM_ERROR", e.BROWSER_RESOURCE_ERROR = "BROWSER_RESOURCE_ERROR", e.BROWSER_CONSOLE = "BROWSER_CONSOLE", e.BROWSER_PERF = "BROWSER_PERF", e.BROWSER_RESOURSE = "BROWSER_RESOURSE", e.MINI_SEND = "MINI_SEND", e.MINI_REQUEST = "MINI_REQUEST", e.MINI_BASE_TRANSFORM = "MINI_BASE_TRANSFORM", e.MINI_ROUTE = "MINI_ROUTE", e
    }(K || {}),
    H = (i(d = {}, "BROWSER_SEND", -1), i(d, "MINI_SEND", -2), i(d, "BASE_TRANSFORM", 100), i(d, "BROWSER_BASE_TRANSFORM", 200), i(d, "MINI_BASE_TRANSFORM", 201), i(d, "BROWSER_PERF", 301), i(d, "BROWSER_FETCH", 400), i(d, "BROWSER_XHR", 401), i(d, "MINI_REQUEST", 402), i(d, "BROWSER_RUNTIME_ERROR", 500), d),
    G = function(e) {
      return e.OK = "OK", e.ERROR = "ERROR", e.UNSET = "UNSET", e
    }(G || {}),
    X = Object.defineProperty,
    Q = Object.defineProperties,
    V = Object.getOwnPropertyDescriptors,
    z = Object.getOwnPropertySymbols,
    J = Object.prototype.hasOwnProperty,
    Y = Object.prototype.propertyIsEnumerable,
    Z = function(e, t, r) {
      return t in e ? X(e, t, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: r
      }) : e[t] = r
    },
    $ = function(e, t) {
      for (var r in t || (t = {})) J.call(t, r) && Z(e, r, t[r]);
      if (z) {
        var n, o = c(z(t));
        try {
          for (o.s(); !(n = o.n()).done;) r = n.value, Y.call(t, r) && Z(e, r, t[r])
        } catch (e) {
          o.e(e)
        } finally {
          o.f()
        }
      }
      return e
    };

  function ee() {
    return {
      name: K.BASE_TRANSFORM,
      run: function() {
        var e = this;
        this.subscribe("*", (function(t, r) {
          var n, o, i, s, a = t.otBase,
            u = t.extra;
          a.service = e.options.service, a.attribute = (i = $($({}, null != (n = e.options.attribute) ? n : {}), a.attribute), s = {
            sid: e.session.getSessionId(),
            pid: e.session.getPageId(),
            uid: e.options.uid
          }, Q(i, V(s))), e.options.nickname && (a.attribute.nickname = e.options.nickname), W(e.options.custom, a.attribute, "custom"), a.resource = $($({}, null != (o = e.options.resource) ? o : {}), a.resource), W({
            "uem.sdk.version": "0.3.8",
            workspace: e.options.workspace,
            "deployment.environment": e.options.env
          }, a.resource), e.options.namespace && (a.resource["service.namespace"] = e.options.namespace), e.options.version && (a.resource["service.version"] = e.options.version);
          var c = {
            otBase: a,
            extra: u
          };
          K.BASE_TRANSFORM, r(c)
        }), H[K.BASE_TRANSFORM])
      }
    }
  }
  var te = function() {
      function e(t) {
        l(this, e), this.subscribeMap = {}, this.allNotifiers = [], this.allNotifiers = t
      }
      return f(e, [{
        key: "subscribeOne",
        value: function(e, t, r, n) {
          var o, i = null != (o = this.subscribeMap[t]) ? o : [];
          this.subscribeMap[t] = i, i.push({
            name: e,
            priority: n,
            callback: r
          }), i.sort((function(e, t) {
            return e.priority - t.priority
          }))
        }
      }, {
        key: "notify",
        value: function(e, t) {
          var r, n = null != (r = this.subscribeMap[e]) ? r : [];
          ! function e(t, r, n) {
            if (!(r < 0)) {
              var o = function(n) {
                  e(t, r - 1, n)
                },
                i = t[r];
              E((function() {
                i.callback.call(void 0, n, o)
              }), "plugin notify run error")()
            }
          }(n, n.length - 1, t)
        }
      }, {
        key: "subscribe",
        value: function(e, t, r, n) {
          if ("*" === t)
            for (var o = 0; o < this.allNotifiers.length; o++) {
              var i = this.allNotifiers[o];
              e !== i && this.subscribeOne(e, i, r, n)
            } else this.subscribeOne(e, t, r, n)
        }
      }, {
        key: "getSubscribeMap",
        value: function() {
          return this.subscribeMap
        }
      }]), e
    }(),
    re = Object.defineProperty,
    ne = Object.defineProperties,
    oe = Object.getOwnPropertyDescriptors,
    ie = Object.getOwnPropertySymbols,
    se = Object.prototype.hasOwnProperty,
    ae = Object.prototype.propertyIsEnumerable,
    ue = function(e, t, r) {
      return t in e ? re(e, t, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: r
      }) : e[t] = r
    },
    ce = ["uid", "nickname", "env", "service", "version", "custom", "namespace"],
    le = function() {
      function e(t) {
        var r, n, o, i;
        l(this, e), this.isInit = !1, this.pendingPlugins = [], this.pluginMap = {}, this.session = (r = M(), n = C(), {
          getSessionId: function() {
            return r
          },
          getPageId: function() {
            return n
          },
          refreshPageId: function() {
            n = C()
          }
        }), this.options = t, this.use({
          name: K.LOG_SEND,
          run: function() {}
        }), this.use(ee()), this.options.env = null != (o = t.env) ? o : "prod", this.options.version = null != (i = t.version) ? i : "-"
      }
      return f(e, [{
        key: "initPlugin",
        value: function(e) {
          var t = this;
          if (null != e && "object" === p(e))
            if (null != e.name && "" !== e.name)
              if (this.pluginMap[e.name]) console.error("plugin name: ".concat(e.name, " is conflict"));
              else if ("function" == typeof e.run) {
            var r = function(e, t) {
              return ne(e, oe(t))
            }(function(e, t) {
              for (var r in t || (t = {})) se.call(t, r) && ue(e, r, t[r]);
              if (ie) {
                var n, o = c(ie(t));
                try {
                  for (o.s(); !(n = o.n()).done;) r = n.value, ae.call(t, r) && ue(e, r, t[r])
                } catch (e) {
                  o.e(e)
                } finally {
                  o.f()
                }
              }
              return e
            }({}, this.context), {
              subscribe: function(r, n, o) {
                t.sub.subscribe(e.name, r, n, o)
              },
              notify: function(r) {
                t.sub.notify(e.name, r)
              }
            });
            E((function() {
              return e.run.call(r)
            }), "plugin ".concat(e.name, " init failed"))()
          } else console.error("plugin.run is not a function");
          else console.error("plugin name is required.");
          else console.error("plugin is not a object")
        }
      }, {
        key: "addLog",
        value: function(e) {
          if (this.isInit) {
            var t = {
              t: F.LOG
            };
            W(e, t, F.LOG);
            var r = {
              start: 1e3 * j(),
              attribute: t,
              resource: {}
            };
            this.sub.notify(K.LOG_SEND, {
              otBase: r,
              extra: {}
            })
          } else console.error("log should call after start")
        }
      }, {
        key: "setOptions",
        value: function(e) {
          var t = this;
          this.isInit ? ce.forEach((function(r) {
            e[r] !== t.options[r] && null != e[r] && "" != e[r] && (t.options[r] = e[r], "uid" === r && "function" == typeof t.setLocalStorage && t.setLocalStorage("sls-trace-uid", e[r]))
          })) : console.error("setOptions should call after start")
        }
      }, {
        key: "start",
        value: function() {
          if (!this.isInit) {
            this.isInit = !0;
            var e = this.options.uid;
            null != e && "" !== e || ("function" == typeof this.getLocalStorage && (e = this.getLocalStorage("sls-trace-uid")), null != e && "" !== e || (e = function() {
              for (var e, t, r = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 20, n = new Array(r), o = Date.now().toString(36).split(""); r-- > 0;) t = (e = 36 * Math.random() | 0).toString(36), n[r] = e % 3 ? t : t.toUpperCase();
              for (var i = 0; i < 8; i++) n.splice(3 * i + 2, 0, o[i]);
              return n.join("")
            }())), this.options.uid = e, "function" == typeof this.setLocalStorage && this.setLocalStorage("sls-trace-uid", e);
            var t = this.pendingPlugins.map((function(e) {
              return null == e ? void 0 : e.name
            })).filter((function(e) {
              return null != e
            }));
            this.sub = new te(t), this.context = {
              options: this.options,
              session: this.session
            };
            var r, n = c(this.pendingPlugins);
            try {
              for (n.s(); !(r = n.n()).done;) {
                var o = r.value;
                this.initPlugin(o)
              }
            } catch (e) {
              n.e(e)
            } finally {
              n.f()
            }
          }
        }
      }, {
        key: "use",
        value: function(e) {
          this.isInit ? console.error("plugin: ".concat(null == e ? void 0 : e.name, " use should run before start")) : this.pendingPlugins.push(e)
        }
      }]), e
    }(),
    fe = Object.defineProperty,
    pe = Object.getOwnPropertySymbols,
    he = Object.prototype.hasOwnProperty,
    de = Object.prototype.propertyIsEnumerable,
    be = function(e, t, r) {
      return t in e ? fe(e, t, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: r
      }) : e[t] = r
    };

  function ve(e) {
    var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
      r = [];
    return Object.keys(t).forEach((function(e) {
      r.push("".concat(e, "=").concat(t[e]))
    })), 0 === r.length ? "/".concat(e) : "/".concat(e).concat(-1 !== e.indexOf("?") ? "&" : "?").concat(r.join("&"))
  }
  var me = Object.defineProperty,
    ge = Object.getOwnPropertySymbols,
    ye = Object.prototype.hasOwnProperty,
    Se = Object.prototype.propertyIsEnumerable,
    Oe = function(e, t, r) {
      return t in e ? me(e, t, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: r
      }) : e[t] = r
    },
    Re = function(e) {
      var t, r, n = {
          request: function() {},
          httpRequest: function() {},
          getSystemInfoSync: function() {}
        },
        o = "unknown";
      if ("object" === ("undefined" == typeof wx ? "undefined" : p(wx))) n = wx, o = "wechat";
      else if ("object" === ("undefined" == typeof dd ? "undefined" : p(dd))) n = dd, o = "dingtalk";
      else if ("object" === ("undefined" == typeof my ? "undefined" : p(my))) n = my, o = "alipay";
      else if ("object" === ("undefined" == typeof tt ? "undefined" : p(tt))) n = tt, o = "bytedance";
      else if ("object" === ("undefined" == typeof qq ? "undefined" : p(qq))) n = qq, o = "qq";
      else if ("object" === ("undefined" == typeof swan ? "undefined" : p(swan))) n = swan, o = "swan";
      else {
        if (!e.platformSDK) throw new Error("Current platform is not default supported by SLS API, Pleace config platformSDK or contack Aliyun SLS team.");
        n = e.platformSDK
      }
      return function(e, t) {
        for (var r in t || (t = {})) ye.call(t, r) && Oe(e, r, t[r]);
        if (ge) {
          var n, o = c(ge(t));
          try {
            for (o.s(); !(n = o.n()).done;) r = n.value, Se.call(t, r) && Oe(e, r, t[r])
          } catch (e) {
            o.e(e)
          } finally {
            o.f()
          }
        }
        return e
      }({
        sdk: n,
        appName: o
      }, (t = n, {
        getStorageSync: function(e) {
          return "function" == typeof t.getStorageSync ? t.getStorageSync(e) : null
        },
        setStorageSync: function(e, r) {
          "function" == typeof t.setStorageSync && t.setStorageSync(e, r)
        },
        getCurrentPagesInterop: r = function() {
          return "function" != typeof getCurrentPages ? [] : getCurrentPages()
        },
        getNavigateBackUrl: function() {
          var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 1,
            t = getCurrentPages();
          if (!t.length) return "";
          var r = t[t.length - e];
          return ve(r.route, r.options)
        },
        getCurrentUrl: function() {
          var e = r();
          if (!e.length) return "";
          var t = e.pop();
          return ve(t.route, t.options)
        }
      }))
    };

  function Ee(e, t) {
    var r, n = "unknown";
    if (t && e[t]) r = e[t], n = t;
    else if (e.request) r = e.request, n = "request";
    else {
      if (!e.httpRequest) throw new Error("Current platform is not default supported by SLS API, Pleace config platformRequestName or contack Aliyun SLS team.");
      r = e.httpRequest, n = "httpRequest"
    }
    return {
      request: r,
      requestName: n
    }
  }
  var Ie = function() {
      function e(t) {
        l(this, e), this.total = 0, this.count = 0, this.peddings = [], this.total = t
      }
      return f(e, [{
        key: "append",
        value: function(e) {
          this.peddings.push(e), this.run()
        }
      }, {
        key: "run",
        value: function() {
          var e = this;
          if (this.peddings.length && !(this.count >= this.total)) {
            var t = this.peddings.shift();
            t && (this.count++, t().finally((function() {
              e.count--, e.run()
            })))
          }
        }
      }]), e
    }(),
    _e = function() {
      function e(t) {
        var r, n, o, i = this;
        l(this, e), this.timer = null, this.time = 10, this.count = 10, this.arr = [], this.time = null != (r = t.time) ? r : 10, this.count = null != (n = t.count) ? n : 10, this.ccController = new Ie(null != (o = t.maxReqCount) ? o : 10), t.host.startsWith("http://") || t.host.startsWith("https://") ? this.url = t.host + "/logstores/" + t.logstore + "/track" : this.url = "https://" + t.project + "." + t.host + "/logstores/" + t.logstore + "/track", this.opt = t, t.installUnloadHook && "function" == typeof t.installUnloadHook && t.installUnloadHook((function() {
          i.sendImmediateInner()
        }))
      }
      return f(e, [{
        key: "assemblePayload",
        value: function(e) {
          var t = {
            __logs__: e
          };
          return this.opt.tags && (t.__tags__ = this.opt.tags), this.opt.topic && (t.__topic__ = this.opt.topic), this.opt.source && (t.__source__ = this.opt.source), JSON.stringify(t)
        }
      }, {
        key: "platformSend",
        value: function() {
          var e = this;
          if (this.opt.sendPayload && "function" == typeof this.opt.sendPayload) {
            var t = this.assemblePayload(this.arr);
            this.ccController.append((function() {
              return e.opt.sendPayload(e.url, t)
            }))
          }
        }
      }, {
        key: "transString",
        value: function(e) {
          var t = {};
          for (var r in e) "object" == p(e[r]) ? t[r] = JSON.stringify(e[r]) : t[r] = String(e[r]);
          return t
        }
      }, {
        key: "sendImmediateInner",
        value: function() {
          this.arr && this.arr.length > 0 && (this.platformSend(), null != this.timer && (clearTimeout(this.timer), this.timer = null), this.arr = [])
        }
      }, {
        key: "sendInner",
        value: function() {
          if (this.timer) this.arr.length >= this.count && (clearTimeout(this.timer), this.timer = null, this.sendImmediateInner());
          else {
            var e = this;
            this.arr.length >= this.count || this.time <= 0 ? this.sendImmediateInner() : this.timer = setTimeout((function() {
              e.sendImmediateInner()
            }), 1e3 * this.time)
          }
        }
      }, {
        key: "send",
        value: function(e) {
          var t = this.transString(e);
          this.arr.push(t), this.sendInner()
        }
      }, {
        key: "sendImmediate",
        value: function(e) {
          var t = this.transString(e);
          this.arr.push(t), this.sendImmediateInner()
        }
      }, {
        key: "sendBatchLogs",
        value: function(e) {
          var t, r = this,
            n = e.map((function(e) {
              return r.transString(e)
            }));
          (t = this.arr).push.apply(t, o(n)), this.sendInner()
        }
      }, {
        key: "sendBatchLogsImmediate",
        value: function(e) {
          var t, r = this,
            n = e.map((function(e) {
              return r.transString(e)
            }));
          (t = this.arr).push.apply(t, o(n)), this.sendImmediateInner()
        }
      }, {
        key: "overwriteTransString",
        value: function(e) {
          this.transString = e.transString
        }
      }, {
        key: "getOpt",
        value: function() {
          return this.opt
        }
      }]), e
    }(),
    Pe = Object.defineProperty,
    Ne = Object.getOwnPropertySymbols,
    ke = Object.prototype.hasOwnProperty,
    we = Object.prototype.propertyIsEnumerable,
    je = function(e, t, r) {
      return t in e ? Pe(e, t, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: r
      }) : e[t] = r
    },
    Te = function(e, t, r) {
      return new Promise((function(n, o) {
        var i = function(e) {
            try {
              a(r.next(e))
            } catch (e) {
              o(e)
            }
          },
          s = function(e) {
            try {
              a(r.throw(e))
            } catch (e) {
              o(e)
            }
          },
          a = function(e) {
            return e.done ? n(e.value) : Promise.resolve(e.value).then(i, s)
          };
        a((r = r.apply(e, t)).next())
      }))
    };

  function Be(e, t, r, n, o) {
    var i = {
      success: function(e) {
        e && (200 === e.statusCode || o && o(t, e))
      },
      fail: function(e) {
        o && o(t, e)
      }
    };
    (0, Ee(r, n).request)(function(e, t) {
      for (var r in t || (t = {})) ke.call(t, r) && je(e, r, t[r]);
      if (Ne) {
        var n, o = c(Ne(t));
        try {
          for (o.s(); !(n = o.n()).done;) r = n.value, we.call(t, r) && je(e, r, t[r])
        } catch (e) {
          o.e(e)
        } finally {
          o.f()
        }
      }
      return e
    }({
      url: "".concat(e, "?APIVersion=0.6.0"),
      method: "POST",
      data: t
    }, i))
  }

  function Ae(e, t, r, o, i, s, a, u) {
    return Te(this, null, n().mark((function c() {
      var l = this;
      return n().wrap((function(c) {
        for (;;) switch (c.prev = c.next) {
          case 0:
            return c.abrupt("return", new Promise((function(c) {
              return Te(l, null, n().mark((function l() {
                var f, p, h, d, b, v, m;
                return n().wrap((function(n) {
                  for (;;) switch (n.prev = n.next) {
                    case 0:
                      return e = e.endsWith("/track") ? e.slice(0, -6) : e, n.next = 3, i.process(e, t, s);
                    case 3:
                      f = n.sent, p = f.data, h = f.header, d = Ee(r, a), b = d.request, o && (v = "alipay" == o || "dingtalk" == o ? {
                        url: e,
                        method: "POST",
                        data: p,
                        headers: h
                      } : {
                        url: e,
                        method: "POST",
                        data: p,
                        header: h
                      }, m = {
                        success: function(t) {
                          var n;
                          if (t)
                            if (200 === t.statusCode);
                            else if (u && u(v, t), 400 === t.statusCode && t.data && "RequestTimeExpired" === t.data.errorCode && !s) try {
                            var c = t.header;
                            if ("AliyunSLS" === c.Server) {
                              var l = Number(null != (n = c["x-log-time"]) ? n : 0);
                              Ae(e, p, r, o, i, l, a, u)
                            }
                          } catch (e) {}
                        },
                        complete: function() {
                          c()
                        }
                      }, b(Object.assign(v, m)));
                    case 8:
                    case "end":
                      return n.stop()
                  }
                }), l)
              })))
            })));
          case 1:
          case "end":
            return c.stop()
        }
      }), c)
    })))
  }
  var Me, Ce = function(o) {
      t(s, o);
      var i = r(s);

      function s(t) {
        var r;
        l(this, s);
        var o = Re(t).sdk,
          a = Object.assign({}, t, {
            sendPayload: function(i, s) {
              return Te(e(r), null, n().mark((function e() {
                return n().wrap((function(e) {
                  for (;;) switch (e.prev = e.next) {
                    case 0:
                      Be(i, s, o, t.platformRequestName, t.onPutlogsError);
                    case 1:
                    case "end":
                      return e.stop()
                  }
                }), e)
              })))
            }
          });
        return r = i.call(this, a)
      }
      return f(s, [{
        key: "useStsPlugin",
        value: function(e) {
          var t = this,
            r = Re(this.getOpt()),
            o = r.sdk,
            i = r.appName;
          this.getOpt().sendPayload = function(r, s) {
            return Te(t, null, n().mark((function t() {
              var a;
              return n().wrap((function(t) {
                for (;;) switch (t.prev = t.next) {
                  case 0:
                    return a = this.getOpt(), t.next = 3, Ae(r, s, o, i, e, void 0, a.platformRequestName, a.onPutlogsError);
                  case 3:
                  case "end":
                    return t.stop()
                }
              }), t, this)
            })))
          }, this.overwriteTransString(e)
        }
      }]), s
    }(_e),
    qe = Object.defineProperty,
    De = Object.defineProperties,
    xe = Object.getOwnPropertyDescriptors,
    Le = Object.getOwnPropertySymbols,
    Ue = Object.prototype.hasOwnProperty,
    We = Object.prototype.propertyIsEnumerable,
    Fe = function(e, t, r) {
      return t in e ? qe(e, t, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: r
      }) : e[t] = r
    },
    Ke = function(e, t) {
      for (var r in t || (t = {})) Ue.call(t, r) && Fe(e, r, t[r]);
      if (Le) {
        var n, o = c(Le(t));
        try {
          for (o.s(); !(n = o.n()).done;) r = n.value, We.call(t, r) && Fe(e, r, t[r])
        } catch (e) {
          o.e(e)
        } finally {
          o.f()
        }
      }
      return e
    },
    He = function(e, t) {
      return De(e, xe(t))
    };

  function Ge(e, t) {
    return Me || (Me = function(e, t) {
      var r = new b((function() {
        var n = t.sdk,
          o = t.appName,
          i = Ee(n, e.platformRequestName).requestName,
          s = n[i];
        Object.defineProperty(n, i, {
          writable: !0,
          enumerable: !0,
          configurable: !0,
          value: function() {
            var t, n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
              i = n.url,
              a = C(),
              u = M(),
              c = j(),
              l = null != (t = n.method) ? t : "GET";
            e.enableTrace && w(i, e.enableTraceRequestConfig) && U(u, a, n, e.customTraceHeaders, e.enableXb3, !o || "alipay" != o && "dingtalk" != o ? "header" : "headers");
            var f = {
                config: He(Ke({}, n), {
                  method: l
                }),
                start: c,
                spanID: a,
                traceID: u,
                isInternal: I(i, e)
              },
              p = n.success,
              h = function() {
                for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
                var o = t[0],
                  i = f;
                if (i.end = j(), i.duration = T(i.start, i.end), i.status = o.statusCode, i.responseText = o.errMsg, i.body = o.data, i.resArgs = t, r.notify(i), "function" == typeof p) return p(o)
              },
              d = n.fail,
              b = function() {
                for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
                var o = t[0],
                  i = f;
                if (i.end = j(), i.duration = T(i.start, i.end), i.status = o.statusCode, i.responseText = o.errMsg, i.resArgs = t, r.notify(i), "function" == typeof d) return d(o)
              },
              v = He(Ke({}, n), {
                success: h,
                fail: b
              });
            return s.call(this, v)
          }
        })
      }));
      return r
    }(e, t)), Me
  }

  function Xe(e) {
    return {
      name: K.MINI_REQUEST,
      run: function() {
        var t = this;
        Ge(this.options, e).subscribe((function(e) {
          var r;
          if (!e.isInternal) {
            var n = e.status >= 200 && e.status < 400,
              o = e.config,
              i = o.url,
              s = o.method;
            if (!k(i, t.options.ignoreRequestConfig, e.status)) {
              var a = P(i),
                u = {
                  start: 1e3 * e.start,
                  end: 1e3 * e.end,
                  duration: 1e3 * e.duration,
                  host: "",
                  kind: "client",
                  links: [],
                  logs: [],
                  name: "".concat(s, " ").concat(null != a ? a.path : "unkonwn"),
                  parentSpanID: "",
                  spanID: e.spanID,
                  statusCode: n ? G.OK : G.ERROR,
                  statusMessage: null != (r = e.responseText) ? r : "",
                  traceID: e.traceID,
                  attribute: {
                    t: F.API
                  },
                  resource: {}
                };
              W(N(i, s, e.status, a), u.attribute, "http");
              var c = {
                otBase: u,
                extra: {
                  start: 1e3 * e.start,
                  relativeTime: e.relativeTime,
                  duration: 1e3 * e.duration,
                  resArgs: e.resArgs
                }
              };
              (function(e, t, r) {
                return !1 !== e && (!0 === e || "error" === e && !r || "function" == typeof e && E(e, "call shouldTrackBody failed")(t))
              })(t.options.enableRequestBodyConfig, c, n) && (u.attribute["http.body"] = e.body), K.MINI_REQUEST, t.notify(c)
            }
          }
        }))
      }
    }
  }
  var Qe = Object.defineProperty,
    Ve = Object.defineProperties,
    ze = Object.getOwnPropertyDescriptors,
    Je = Object.getOwnPropertySymbols,
    Ye = Object.prototype.hasOwnProperty,
    Ze = Object.prototype.propertyIsEnumerable,
    $e = function(e, t, r) {
      return t in e ? Qe(e, t, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: r
      }) : e[t] = r
    };

  function et(e) {
    return {
      name: K.MINI_BASE_TRANSFORM,
      run: function() {
        this.subscribe("*", (function(t, r) {
          var n = t.otBase,
            o = t.extra,
            i = e.getCurrentUrl();
          W({
            target: encodeURIComponent(i),
            target_original: i
          }, n.attribute, "page");
          var s = "",
            a = "";
          try {
            s = e.sdk.getSystemInfoSync().system.split(" ").shift()
          } catch (e) {}
          try {
            a = e.sdk.getSystemInfoSync().model
          } catch (e) {}
          n.resource = function(e, t) {
            return Ve(e, ze(t))
          }(function(e, t) {
            for (var r in t || (t = {})) Ye.call(t, r) && $e(e, r, t[r]);
            if (Je) {
              var n, o = c(Je(t));
              try {
                for (o.s(); !(n = o.n()).done;) r = n.value, Ze.call(t, r) && $e(e, r, t[r])
              } catch (e) {
                o.e(e)
              } finally {
                o.f()
              }
            }
            return e
          }({}, n.resource), {
            "os.name": s,
            "device.model.name": a,
            "uem.data.type": "miniprogram",
            "uem.miniprogram.platform": e.appName
          });
          var u = {
            otBase: n,
            extra: o
          };
          K.MINI_BASE_TRANSFORM, r(u)
        }), H[K.MINI_BASE_TRANSFORM])
      }
    }
  }
  var rt, nt, ot, it, st, at, ut = function(e) {
      t(o, e);
      var n = r(o);

      function o(e) {
        var t, r;
        return l(this, o), (t = n.call(this, e)).options.service = null != (r = e.service) ? r : "miniprogram", t.platformSDKInterop = Re(e), t
      }
      return f(o, [{
        key: "setLocalStorage",
        value: function(e, t) {
          this.platformSDKInterop.setStorageSync(e, t)
        }
      }, {
        key: "getLocalStorage",
        value: function(e) {
          return this.platformSDKInterop.getStorageSync(e)
        }
      }, {
        key: "getPlatformSDKInterop",
        value: function() {
          return this.platformSDKInterop
        }
      }]), o
    }(le),
    ct = (rt = function(e) {
      var t, r, n, o, i = new ut(e);
      return t = e.enableRequest, r = !0, (null == t ? r : t) && i.use(Xe(i.getPlatformSDKInterop())), i.use((n = e.stsPlugin, {
        name: K.MINI_SEND,
        run: function() {
          var e = new Ce({
            project: this.options.project,
            host: this.options.host,
            logstore: this.options.logstore,
            count: this.options.trackCountThreshold,
            time: this.options.trackTimeThreshold,
            platformRequestName: this.options.platformRequestName,
            platformSDK: this.options.platformSDK
          });
          n && e.useStsPlugin(n), this.subscribe("*", (function(t, r) {
            var n = t.otBase;
            K.MINI_SEND, e.send(n), r(t)
          }), H[K.MINI_SEND])
        }
      })), i.use(et(i.getPlatformSDKInterop())), i.use((o = i.getPlatformSDKInterop(), {
        name: K.MINI_ROUTE,
        run: function() {
          var e = this,
            t = function(t, r) {
              var n = {
                t: F.LOCATION,
                launch: t ? "true" : "false"
              };
              r && W(r, n, "page");
              var o = {
                start: 1e3 * j(),
                attribute: n,
                resource: {}
              };
              e.notify({
                otBase: o,
                extra: {}
              })
            };
          t(!0), ["switchTab", "reLaunch", "redirectTo", "navigateTo", "navigateBack"].forEach((function(e) {
            var r = o.sdk,
              n = r[e];
            Object.defineProperty(r, e, {
              writable: !0,
              enumerable: !0,
              configurable: !0,
              value: function() {
                var r = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                  i = "";
                i = "navigateBack" === e ? o.getNavigateBackUrl(null == r ? void 0 : r.delta) : r.url;
                var s = o.getCurrentUrl(),
                  a = {
                    target_from: encodeURIComponent(s),
                    target_from_original: s,
                    target_to: encodeURIComponent(i),
                    target_to_original: i
                  };
                return t(!1, a), n.call(this, r)
              }
            })
          }))
        }
      })), i
    }, it = {
      current: void 0
    }, st = [], at = {}, "function" == typeof nt && (at = null != (ot = nt(it)) ? ot : {}), function(e, t) {
      for (var r in t || (t = {})) he.call(t, r) && be(e, r, t[r]);
      if (pe) {
        var n, o = c(pe(t));
        try {
          for (o.s(); !(n = o.n()).done;) r = n.value, de.call(t, r) && be(e, r, t[r])
        } catch (e) {
          o.e(e)
        } finally {
          o.f()
        }
      }
      return e
    }({
      init: function(e) {
        if (null == it.current) {
          it.current = rt(e);
          var t = it.current;
          st.forEach((function(e) {
            t.use(e)
          })), t.start()
        }
      },
      use: function(e) {
        st.push(e)
      },
      addLog: function(e) {
        var t = it.current;
        t && t.addLog(e)
      },
      onReady: function(e) {
        e()
      },
      setOptions: function(e) {
        var t = it.current;
        t && t.setOptions(e)
      }
    }, at));
  h.SLSTraceMiniClient = ut, h.SLS_TRACER = ct
}), (function(e) {
  return u({} [e], e)
})), u(1739784025838));