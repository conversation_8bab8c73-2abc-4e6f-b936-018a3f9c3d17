var e = require("../../../6D59C885549B04BF0B3FA082E5940D65.js");
Component({
  properties: {},
  data: {
    imgUrl: e.imgUrl,
    imgVersion: e.imgVersion,
    open: !1
  },
  methods: {
    openMask: function() {
      this.setData({
        open: !0
      })
    },
    closeMask: function() {
      this.setData({
        open: !1
      })
    },
    handleClose: function() {
      this.closeMask()
    },
    handleMyTask: function() {
      wx.navigateTo({
        url: "/cny/pages/drawLots/index"
      }), this.closeMask()
    }
  }
});