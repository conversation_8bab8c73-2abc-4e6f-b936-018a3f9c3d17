var e, n = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  a = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  r = (e = require("../../../A5622344549B04BFC3044B435F450D65.js")) && e.__esModule ? e : {
    default: e
  },
  t = (require("../../../71D07D80549B04BF17B615870C540D65.js"), require("../../../95D1B746549B04BFF3B7DF41DA740D65.js")),
  o = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  i = require("../../../6A1B1695549B04BF0C7D7E9251850D65.js");
require("../../../03E40C94549B04BF6582649340450D65.js").mount(), Page({
  data: {
    statusHeaderBarHeight: o.statusHeaderBarHeight,
    imgUrl: o.imgUrl,
    imgVersion: o.imgVersion,
    screenInfo: (0, t.getScreenInfo)(),
    listData: i.data
  },
  handleOpen: function(e) {
    var n = e.currentTarget.dataset.info,
      a = n.appid,
      t = n.link,
      o = n.shortLink,
      i = n.name;
    wx.navigateToMiniProgram({
      appId: a,
      path: t,
      shortLink: o ? t : "",
      envVersion: "release",
      success: function(e) {}
    }), r.default.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "新品合辑",
      page_name: "新品合辑",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/collection/index",
      button_name: i
    })
  },
  onLoad: function(e) {
    return a(n().mark((function e() {
      return n().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            r.default.pageView({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "新品合辑",
              page_name: "新品合辑",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/collection/index"
            });
          case 1:
          case "end":
            return e.stop()
        }
      }), e)
    })))()
  },
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return o.shareOptions
  }
});