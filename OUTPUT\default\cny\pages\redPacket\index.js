var e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  n = require("../../../71D07D80549B04BF17B615870C540D65.js"),
  r = require("../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  a = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  i = require("../../../A5622344549B04BFC3044B435F450D65.js");
Page({
  data: {
    statusHeaderBarHeight: a.statusHeaderBarHeight,
    imgUrl: a.imgUrl,
    imgVersion: a.imgVersion,
    screenInfo: (0, r.getScreenInfo)(),
    redCurrentIndex: 1,
    redPacketList: [{
      type: 1,
      isEnd: !1,
      isLock: !0,
      img: a.imgUrl + "redPacket/r1.png?v=" + a.imgVersion,
      active: !1
    }, {
      type: 2,
      isEnd: !1,
      isLock: !0,
      img: a.imgUrl + "redPacket/r2.png?v=" + a.imgVersion,
      active: !0
    }, {
      type: 3,
      isEnd: !1,
      isLock: !0,
      img: a.imgUrl + "redPacket/r3.png?v=" + a.imgVersion,
      active: !1
    }],
    RedCoverList: []
  },
  handleChangeSwiper: function(e) {
    var t = e.detail.current;
    this.setData({
      redCurrentIndex: t
    }), this.changeRedPacketList(t)
  },
  changeRedPacketList: function(e) {
    var t = this.data.redPacketList;
    t.forEach((function(e) {
      e.active = !1
    })), t[e].active = !0, this.setData({
      redPacketList: t
    })
  },
  handleMyTask: function() {
    wx.navigateTo({
      url: "/cny/pages/task/index"
    }), i.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "我的红包",
      page_name: "我的红包",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/redPacket/index",
      button_name: "前往我的任务"
    })
  },
  getRedCoverListFn: function() {
    var r = this;
    return t(e().mark((function t() {
      var a, i, o, c;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.prev = 0, e.next = 3, (0, n.getRedCoverList)();
          case 3:
            a = e.sent, i = a.code, o = a.data, 200 == i && ((c = r.data.redPacketList).forEach((function(e, t) {
              e.isLock = !0, o.RedCoverList.some((function(t) {
                return t.type == e.type
              })) && (e.isLock = !1)
            })), r.setData({
              redPacketList: c,
              RedCoverList: o.RedCoverList
            }, (function() {
              r.getRedCoverConfigFn()
            }))), e.next = 12;
            break;
          case 9:
            e.prev = 9, e.t0 = e.catch(0), console.log("redPacket getRedCoverList error: ", e.t0);
          case 12:
          case "end":
            return e.stop()
        }
      }), t, null, [
        [0, 9]
      ])
    })))()
  },
  getRedCoverConfigFn: function() {
    var r = this;
    return t(e().mark((function t() {
      var a, i, o, c;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.prev = 0, e.next = 3, (0, n.getRedCoverConfig)();
          case 3:
            a = e.sent, i = a.code, o = a.data, 200 == i && ((c = r.data.redPacketList).forEach((function(e, t) {
              e.isEnd = !1, o.RedCoverConfig.some((function(t) {
                return t.type == e.type && 0 == t.amount
              })) && (e.isEnd = !0)
            })), r.setData({
              redPacketList: c
            })), e.next = 12;
            break;
          case 9:
            e.prev = 9, e.t0 = e.catch(0), console.log("redPacket getRedCoverConfig error: ", e.t0);
          case 12:
          case "end":
            return e.stop()
        }
      }), t, null, [
        [0, 9]
      ])
    })))()
  },
  handleOpenCounponRecord: function() {
    this.selectComponent("#couponRecord").openMask()
  },
  handleGetRedPack: function(e) {
    var t = e.currentTarget.dataset.info.type,
      n = this.data.RedCoverList.find((function(e) {
        return e.type == t
      })).coverUrl;
    wx.showRedPackage({
      url: n
    })
  },
  onLoad: function(n) {
    var r = this;
    return t(e().mark((function t() {
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            r.getRedCoverListFn(), i.pageView({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "我的红包",
              page_name: "我的红包",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/redPacket/index"
            });
          case 2:
          case "end":
            return e.stop()
        }
      }), t)
    })))()
  },
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return a.shareOptions
  }
});