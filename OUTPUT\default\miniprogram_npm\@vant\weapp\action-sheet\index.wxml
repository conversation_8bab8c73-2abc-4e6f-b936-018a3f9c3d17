<van-popup bind:close="onClickOverlay" closeOnClickOverlay="{{closeOnClickOverlay}}" customClass="van-action-sheet custom-class" overlay="{{overlay}}" position="bottom" round="{{round}}" safeAreaInsetBottom="{{safeAreaInsetBottom}}" show="{{show}}" zIndex="{{zIndex}}">
    <view class="van-action-sheet__header" wx:if="{{title}}">{{title}}<van-icon bind:click="onClose" customClass="van-action-sheet__close" name="cross"></van-icon>
    </view>
    <view class="van-action-sheet__description van-hairline--bottom" wx:if="{{description}}">{{description}}</view>
    <view class="list-class" wx:if="{{actions&&actions.length}}">
        <button appParameter="{{appParameter}}" bindcontact="onContact" binderror="onError" bindgetphonenumber="onGetPhoneNumber" bindgetuserinfo="onGetUserInfo" bindlaunchapp="onLaunchApp" bindopensetting="onOpenSetting" bindtap="{{item.disabled||item.loading?'':'onSelect'}}" class="{{utils.bem( 'action-sheet__item',{disabled:item.disabled||item.loading} )}} {{item.className||''}}" data-index="{{index}}" hoverClass="van-action-sheet__item--hover" lang="{{lang}}" openType="{{item.disabled||item.loading||canIUseGetUserProfile&&item.openType==='getUserInfo'?'':item.openType}}" sendMessageImg="{{sendMessageImg}}" sendMessagePath="{{sendMessagePath}}" sendMessageTitle="{{sendMessageTitle}}" sessionFrom="{{sessionFrom}}" showMessageCard="{{showMessageCard}}" style="{{item.color?'color: '+item.color:''}}" wx:for="{{actions}}" wx:key="index">
            <block wx:if="{{!item.loading}}">{{item.name}}<view class="van-action-sheet__subname" wx:if="{{item.subname}}">{{item.subname}}</view>
            </block>
            <van-loading customClass="van-action-sheet__loading" size="22px" wx:else></van-loading>
        </button>
    </view>
    <slot></slot>
    <block wx:if="{{cancelText}}">
        <view class="van-action-sheet__gap"></view>
        <view bind:tap="onCancel" class="van-action-sheet__cancel" hoverClass="van-action-sheet__cancel--hover" hoverStayTime="70">{{cancelText}}</view>
    </block>
</van-popup>

<wxs module="utils" src="..\wxs\utils.wxs"/>