var style = require('..\..\..\..\require('.\miniprogram_npm\ @vant\ weapp\ wxs\ style.wxs ')();');
var addUnit = require('..\..\..\..\require('.\miniprogram_npm\ @vant\ weapp\ wxs\ add - unit.wxs ')();');

function rootStyle(data) {
  if (!data.gutter) {
    return ('')
  };
  return (style(({
    'margin-right': addUnit(-data.gutter / 2),
    'margin-left': addUnit(-data.gutter / 2),
  })))
};
module.exports = ({
  rootStyle: rootStyle,
});