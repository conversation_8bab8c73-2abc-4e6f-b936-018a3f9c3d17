<import src="./calendar.wxml"></import>
<van-popup bind:after-enter="onOpened" bind:after-leave="onClosed" bind:close="onClose" bind:enter="onOpen" closeIconClass="van-calendar__close-icon" closeOnClickOverlay="{{closeOnClickOverlay}}" closeable="{{showTitle||showSubtitle}}" customClass="van-calendar__popup--{{position}}" position="{{position}}" round="{{round}}" safeAreaInsetBottom="{{safeAreaInsetBottom}}" show="{{show}}" wx:if="{{poppable}}">
    <include src="./calendar.wxml"></include>
</van-popup>
<include src="./calendar.wxml" wx:else></include>
<van-toast id="van-toast"></van-toast>

<wxs module="computed" src="index.wxs"/>
<wxs module="utils" src="..\wxs\utils.wxs"/>