@import "wickedcss.min.wxss";

wx-image {
    display: block;
    position: relative
}

.scroll-view {
    bottom: 0;
    overflow: hidden;
    z-index: 10
}

.maskContainer,.scroll-view {
    position: absolute;
    width: 100%
}

.maskContainer {
    background-color: rgba(0,0,0,.7);
    height: 100%;
    left: 0;
    top: 0;
    z-index: 88
}

.maskImgCenter {
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
}

.AaHouDiHei {
    font-family: AaHouDiHei
}

.SourceHanSansCN-Normal {
    font-family: SourceHanSansCN-Normal
}

.SourceHanSerifCN-Bold {
    font-family: "SourceHanSerifCN-Bold"
}

.SourceHanSerifCN-Light {
    font-family: "SourceHanSerifCN-Light"
}

.SourceHanSerifCN-Regular {
    font-family: "SourceHanSerifCN-Regular"
}

.SourceHanSansCN-Medium {
    font-family: SourceHanSansCN-Medium
}

.LogoSCUnboundedSans-Regular {
    font-family: LogoSCUnboundedSans-Regular
}
