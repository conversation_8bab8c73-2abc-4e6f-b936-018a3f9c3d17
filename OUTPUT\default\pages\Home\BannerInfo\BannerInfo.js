var t, e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  a = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  n = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  o = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  s = (t = require("../../../87624F60549B04BFE10427674BE30D65.js")) && t.__esModule ? t : {
    default: t
  };
var i = getApp();
Page({
  data: {
    img: i.globalData.img,
    imgNew: i.globalData.imgNew,
    peanutFamily: [],
    chooseBanner: 0,
    taskInfo: {},
    status: 0,
    taskDuration: 0,
    taskNum: 0
  },
  gotoWebUrl: function() {
    var t = this.data.peanutFamily[this.data.chooseBanner].linkUrl;
    t ? -1 != t.indexOf("http") ? wx.navigateTo({
      url: "/pages/WebUrl/WebUrl?url=".concat(encodeURIComponent(t))
    }) : -1 != item.linkUrl.indexOf("#小程序://") ? wx.navigateToMiniProgram({
      shortLink: t,
      envVersion: "release"
    }) : wx.navigateTo({
      url: t
    }) : wx.showToast({
      title: "敬请期待",
      icon: "none"
    })
  },
  changeIndex: function(t) {
    var e = t.currentTarget.dataset.index;
    this.setData({
      chooseBanner: e
    })
  },
  onLoad: function(t) {
    var n = this;
    return a(e().mark((function a() {
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.t0 = n, e.next = 3, s.default.getConfigJson();
          case 3:
            if (e.t1 = e.sent, e.t1) {
              e.next = 6;
              break
            }
            e.t1 = {};
          case 6:
            e.t2 = e.t1.peanutFamily, e.t3 = {
              peanutFamily: e.t2
            }, e.t0.setData.call(e.t0, e.t3), n.setData({
              chooseBanner: t.index
            });
          case 10:
          case "end":
            return e.stop()
        }
      }), a)
    })))()
  },
  onReady: function() {},
  onShow: function() {},
  getConfig: function() {
    var t = this;
    (0, o.getBrowseConfig)({}).then((function(e) {
      200 == e.code ? e.data.status && (t.setData({
        taskDuration: e.data.taskDuration
      }), console.log("数据", t.data.taskInfo), t.beginAct()) : (0, n.toastModel)(e.message)
    }))
  },
  beginAct: function() {
    var t = this;
    (0, o.begin)({
      openId: s.default.data.userInfo.openid,
      phone: s.default.data.userInfo.mobile,
      userId: s.default.data.userInfo.id,
      userName: s.default.data.userInfo.memberName
    }).then((function(e) {
      200 == e.code ? (t.setData({
        taskInfo: e.data
      }), setInterval((function() {
        t.setData({
          taskNum: t.data.taskNum += 1
        })
      }), 1e3)) : (0, n.toastModel)(e.message)
    }))
  },
  onHide: function() {
    console.log("隐藏当前页面")
  },
  onUnload: function() {
    console.log("页面卸载")
  },
  setTask: function() {
    this.data.taskNum < this.data.taskDuration ? (0, o.cancel)({
      openId: s.default.data.userInfo.openid,
      userId: s.default.data.userInfo.id,
      sign: this.data.taskInfo.sign,
      startTime: this.data.taskInfo.startTime
    }).then((function(t) {})) : (0, o.end)({
      openId: s.default.data.userInfo.openid,
      userId: s.default.data.userInfo.id,
      sign: this.data.taskInfo.sign,
      startTime: this.data.taskInfo.startTime
    }).then((function(t) {}))
  },
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {}
});