Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.wxPromise = exports.wxAuth = exports.toast = exports.qqMapToBMap = exports.newGuid = exports.getUrl = exports.getLocation = exports.getGPSAddress = exports.formatTime = exports.formatNumber = exports.formatDate = exports.default = exports.baiduGPS = void 0;
var t = require("./@babel/runtime/helpers/regeneratorRuntime.js"),
  e = require("./@babel/runtime/helpers/slicedToArray.js"),
  r = require("./@babel/runtime/helpers/asyncToGenerator.js"),
  o = a(require("87624F60549B04BFE10427674BE30D65.js")),
  n = a(require("59FF9135549B04BF3F99F93236040D65.js"));

function a(t) {
  return t && t.__esModule ? t : {
    default: t
  }
}
var u = function(t) {
  var e = t.getFullYear(),
    r = t.getMonth() + 1,
    o = t.getDate(),
    n = t.getHours(),
    a = t.getMinutes(),
    u = t.getSeconds();
  return [e, r, o].map(s).join(".") + " " + [n, a, u].map(s).join(":")
};
exports.formatTime = u;
var i = function(t, e) {
  return [t.getFullYear(), t.getMonth() + 1, t.getDate()].map(s).join(e)
};
exports.formatDate = i;
var s = function(t) {
  return (t = t.toString())[1] ? t : "0" + t
};
exports.formatNumber = s;
var c = function() {
  for (var t = "", e = 1; e <= 32; e++) {
    t += Math.floor(16 * Math.random()).toString(16), 8 !== e && 12 !== e && 16 !== e && 20 !== e || (t += "")
  }
  return t
};
exports.newGuid = c;
var l = function() {
  var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "";
  wx.showToast({
    title: "".concat(t),
    icon: "none",
    duration: 1500,
    mask: !0
  })
};
exports.toast = l;
var f = function(t) {
  var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
  return new Promise((function(r, o) {
    Object.assign(e, {
      success: r,
      fail: o
    }), t(e)
  }))
};
exports.wxPromise = f;
var d = function(t) {
  var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "",
    r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : null,
    o = "scope.".concat(t);
  return new Promise((function(t, n) {
    f(wx.getSetting).then((function(e) {
      if (console.log(e.authSetting), !1 === e.authSetting[o]) return Promise.reject();
      t()
    })).catch((function() {
      wx.hideLoading(), wx.showModal({
        title: "提示",
        content: r || "未开启".concat(e, "授权,是否去设置打开？"),
        showCancel: !1,
        success: function(e) {
          if (!e.confirm) return n();
          wx.openSetting({
            success: function(e) {
              if (!e.authSetting[o]) return n();
              t(e)
            }
          })
        }
      })
    }))
  }))
};
exports.wxAuth = d;
var p = function() {
  return d("userLocation", "地理位置", "未获取到您的位置信息，请确保已开启系统设置的GPS定位和微信GPS定位授权").then((function() {
    return f(wx.getLocation).then((function(t) {
      return require("87624F60549B04BFE10427674BE30D65.js").default.data.userLocation = t, n.default.info("gps", t), Promise.resolve(t || {})
    })).catch((function(t) {
      return Promise.resolve({
        flag: !1
      })
    }))
  }))
};
exports.getLocation = p;
var g = function(t, e) {
  var r = "";
  return Object.keys(t).forEach((function(e) {
    r += e + "=" + t[e] + "&"
  })), r.length > 0 && (r = "?" + r.substr(0, r.length - 1)), e.replace("?", "") + r
};
exports.getUrl = g;
var x = function(t, e) {
  if (null == e || "" == e || null == t || "" == t) return [e, t];
  var r = 3.141592653589793,
    o = parseFloat(e),
    n = parseFloat(t),
    a = Math.sqrt(o * o + n * n) + 2e-5 * Math.sin(n * r),
    u = Math.atan2(n, o) + 3e-6 * Math.cos(o * r),
    i = a * Math.cos(u) + .0065;
  return [a * Math.sin(u) + .006, i]
};
exports.qqMapToBMap = x;
var h = function() {
  var n = r(t().mark((function r(n) {
    var a, u, i, s, c, l, f, d, p, g, h, v;
    return t().wrap((function(t) {
      for (;;) switch (t.prev = t.next) {
        case 0:
          if (console.log("LongAndlat", n), a = n, console.log([a.latitude, a.longitude]), a.latitude && a.longitude) {
            t.next = 5;
            break
          }
          return t.abrupt("return", "");
        case 5:
          if (u = x(a.latitude, a.longitude), i = e(u, 2), s = i[0], c = i[1], console.log([s, c]), s && c) {
            t.next = 9;
            break
          }
          return t.abrupt("return", "");
        case 9:
          return o.default.data.userInfoGPS.latitude = s, o.default.data.userInfoGPS.longitude = c, console.log("fetchData.data.userInfoGPS", o.default.data.userInfoGPS), t.next = 14, m(s, c);
        case 14:
          return l = o.default.data.BMAP_GPS || {}, d = (f = l || {}).formatted_address, p = void 0 === d ? "" : d, g = f.sematic_description, h = void 0 === g ? "" : g, v = "".concat(p).concat(h), console.log("解析地理位置:", v), t.abrupt("return", v);
        case 19:
        case "end":
          return t.stop()
      }
    }), r)
  })));
  return function(t) {
    return n.apply(this, arguments)
  }
}();
exports.getGPSAddress = h;
var m = function(t, e) {
  return new Promise((function(r, a) {
    wx.request({
      url: "https://api.map.baidu.com/geocoder/v2/?ak=DAkHeza7GMvAuA5FL2p8GAZxC84WoMLh&location=".concat(t, ",").concat(e, "&output=json&pois=1"),
      success: function(t) {
        var e = (t.data || {}).result;
        n.default.info("BMAP_GPS", e), o.default.data.BMAP_GPS = e, r(e)
      },
      fail: function(t) {
        l("GPS解析失败"), a(t)
      }
    })
  }))
};
exports.baiduGPS = m;
var v = {
  baiduGPS: m,
  qqMapToBMap: x,
  log: n.default,
  formatTime: u,
  formatDate: i,
  newGuid: c,
  toast: l,
  wxPromise: f,
  wxAuth: d,
  getLocation: p,
  getUrl: g
};
exports.default = v;