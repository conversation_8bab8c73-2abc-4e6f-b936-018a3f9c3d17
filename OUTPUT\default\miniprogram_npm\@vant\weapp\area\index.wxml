<van-picker activeClass="active-class" bind:cancel="onCancel" bind:change="onChange" bind:confirm="onConfirm" cancelButtonText="{{cancelButtonText}}" class="van-area__picker" columnClass="column-class" columns="{{computed.displayColumns(columns,columnsNum)}}" confirmButtonText="{{confirmButtonText}}" itemHeight="{{itemHeight}}" loading="{{loading}}" showToolbar="{{showToolbar}}" title="{{title}}" toolbarClass="toolbar-class" valueKey="name" visibleItemCount="{{visibleItemCount}}"></van-picker>

<wxs module="computed" src="index.wxs"/>