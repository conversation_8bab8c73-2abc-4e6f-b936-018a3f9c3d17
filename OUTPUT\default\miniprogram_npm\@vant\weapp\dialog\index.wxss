@import "..\common\index.wxss";

.van-dialog {
    background-color: var(--dialog-background-color,#fff);
    border-radius: var(--dialog-border-radius,16px);
    font-size: var(--dialog-font-size,16px);
    overflow: hidden;
    top: 45%!important;
    width: var(--dialog-width,320px)
}

@media (max-width:321px) {
    .van-dialog {
        width: var(--dialog-small-screen-width,90%)
    }
}

.van-dialog__header {
    font-weight: var(--dialog-header-font-weight,500);
    line-height: var(--dialog-header-line-height,24px);
    padding-top: var(--dialog-header-padding-top,24px);
    text-align: center
}

.van-dialog__header--isolated {
    padding: var(--dialog-header-isolated-padding,24px 0)
}

.van-dialog__message {
    -webkit-overflow-scrolling: touch;
    font-size: var(--dialog-message-font-size,14px);
    line-height: var(--dialog-message-line-height,20px);
    max-height: var(--dialog-message-max-height,60vh);
    overflow-y: auto;
    padding: var(--dialog-message-padding,24px);
    text-align: center
}

.van-dialog__message-text {
    word-wrap: break-word
}

.van-dialog__message--hasTitle {
    color: var(--dialog-has-title-message-text-color,#646566);
    padding-top: var(--dialog-has-title-message-padding-top,8px)
}

.van-dialog__message--round-button {
    color: #323233;
    padding-bottom: 16px
}

.van-dialog__message--left {
    text-align: left
}

.van-dialog__message--right {
    text-align: right
}

.van-dialog__message--justify {
    text-align: justify
}

.van-dialog__footer {
    display: -webkit-flex;
    display: flex
}

.van-dialog__footer--round-button {
    padding: 8px 24px 16px!important;
    position: relative!important
}

.van-dialog__button {
    -webkit-flex: 1;
    flex: 1
}

.van-dialog__cancel,.van-dialog__confirm {
    border: 0!important
}

.van-dialog-bounce-enter {
    opacity: 0;
    -webkit-transform: translate3d(-50%,-50%,0) scale(.7);
    transform: translate3d(-50%,-50%,0) scale(.7)
}

.van-dialog-bounce-leave-active {
    opacity: 0;
    -webkit-transform: translate3d(-50%,-50%,0) scale(.9);
    transform: translate3d(-50%,-50%,0) scale(.9)
}
