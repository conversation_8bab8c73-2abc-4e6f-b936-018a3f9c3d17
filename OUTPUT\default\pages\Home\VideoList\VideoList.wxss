.ProList {
    background: #f7f7f7;
    box-sizing: border-box;
    min-height: 100vh;
    overflow: hidden;
    padding-bottom: 160rpx;
    width: 750rpx
}

.ProList_item {
    background: #fff;
    border-radius: 30rpx;
    height: 400rpx;
    margin: 12rpx auto 0;
    width: 724rpx
}

.ProList_item_top {
    overflow: hidden;
    position: relative
}

.ProList_item_top,.ProList_item_top_prop {
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
    height: 305rpx
}

.ProList_item_top_prop {
    background: rgba(0,0,0,.5);
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.ProList_item_top_prop_play {
    bottom: 21rpx;
    height: 69rpx;
    position: absolute;
    right: 23rpx;
    width: 69rpx
}

.ProList_item_bot {
    -webkit-align-items: center;
    align-items: center;
    box-sizing: border-box;
    color: #000;
    display: -webkit-flex;
    display: flex;
    font-family: Source Han Sans CN;
    font-size: 26rpx;
    font-weight: 400;
    height: 95rpx;
    padding: 0 20rpx;
    width: 724rpx
}

.ProList_item_bot_text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 700rpx
}
