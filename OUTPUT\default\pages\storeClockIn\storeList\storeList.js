var t = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  e = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  n = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  i = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  o = getApp();
Page({
  data: {
    img: o.globalData.img,
    topList: [{
      name: "全部门店",
      index: 0
    }, {
      name: "附近门店",
      index: 1
    }],
    topIndex: 0,
    latitude: "",
    longitude: "",
    districtCode: "",
    list: []
  },
  changeTopTab: function(t) {
    var e = t.currentTarget.dataset.item;
    console.log("index", e), this.setData({
      topIndex: e.index
    }), 0 == this.data.topIndex ? this.getStoreListAll() : this.getStoreList()
  },
  onLoad: function(t) {
    console.log("options", t), this.setData({
      latitude: t.latitude,
      longitude: t.longitude,
      districtCode: t.districtCode
    }), this.getStoreListAll()
  },
  getStoreList: function() {
    var n = this;
    return e(t().mark((function e() {
      var i, o;
      return t().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return t.next = 2, n.getStore(n.data.latitude, n.data.longitude, n.data.districtCode);
          case 2:
            i = t.sent, o = i.data.length > 20 ? i.data.slice(0, 20) : i.data, n.setData({
              list: o
            });
          case 5:
          case "end":
            return t.stop()
        }
      }), e)
    })))()
  },
  getStoreListAll: function() {
    var n = this;
    return e(t().mark((function e() {
      var i, o;
      return t().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return t.next = 2, n.getStoreAll();
          case 2:
            i = t.sent, o = i.data, n.setData({
              list: o
            });
          case 5:
          case "end":
            return t.stop()
        }
      }), e)
    })))()
  },
  gotoWebMap: function(t) {
    var e = t.currentTarget.dataset.item;
    wx.openLocation({
      latitude: Number(e.f1),
      longitude: Number(e.f0),
      name: e.name,
      address: e.address,
      scale: 18
    })
  },
  getStore: function(t, e, o) {
    return new Promise((function(a, r) {
      (0, i.loadingOpen)(), (0, n.storeList)({
        latitude: t,
        longitude: e,
        districtCode: o
      }).then((function(t) {
        (0, i.loadingClose)(), a(t)
      }))
    }))
  },
  getStoreAll: function() {
    return new Promise((function(t, e) {
      (0, i.loadingOpen)(), (0, n.storeListAll)().then((function(e) {
        (0, i.loadingClose)(), t(e)
      }))
    }))
  },
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {}
});