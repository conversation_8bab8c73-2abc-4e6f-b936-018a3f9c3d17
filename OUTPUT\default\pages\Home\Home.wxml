<view class="home">
    <view class="home_gg" wx:if="{{ggState}}"></view>
    <view class="home_swiperBox">
        <swiper autoplay="{{autoplay}}" circular="{{circular}}" class="home_swiper" current="{{currentIndex}}" duration="{{duration}}" indicatorActiveColor="#D20303" indicatorDots="{{indicatorDots}}" interval="{{interval}}">
            <swiper-item wx:for="{{bannerImgs}}" wx:key="index">
                <view catchtap="gotoWebUrl" class="swiper-item" data-item="{{item}}">
                    <image mode="" src="{{item.bannerUrl}}"></image>
                </view>
            </swiper-item>
        </swiper>
        <view bindtap="onPrev" class="home_swiper_left">
            <image mode="" src="{{img}}/newVersion/001.png"></image>
        </view>
        <view bindtap="onNext" class="home_swiper_right">
            <image mode="" src="{{img}}/newVersion/002.png"></image>
        </view>
    </view>
    <view class="home_banner">
        <view class="home_banner_title">
            <image mode="" src="{{img}}newImgs/hsjz.png"></image>
        </view>
        <view class="home_banner_items">
            <view bindtap="gotoBannerInfo" class="home_banner_item" data-index="{{index}}" wx:for="{{peanutFamily}}" wx:key="index">
                <view class="home_banner_item_img">
                    <image mode="" src="{{item.imgUrl}}"></image>
                </view>
                <view class="home_banner_item_text">{{item.name}}</view>
            </view>
        </view>
    </view>
    <view class="home_newB">
        <view bindtap="gotoGZH" class="home_newB_gzh">
            <image mode="" src="{{img}}draw/gzgzh.png"></image>
        </view>
        <view bindtap="gotoTaskCenter" class="home_newB_jf">
            <image mode="" src="{{img}}newImgs/rwzx.png"></image>
        </view>
    </view>
    <view class="home_vip">
        <block wx:if="{{showsws}}">
            <view class="home_vip_title">
                <image mode="" src="{{img}}sws/hsxp.png"></image>
            </view>
            <view bindtap="gotoHsSws" class="home_vip_sws">
                <image mode="" src="{{img}}sws/001.png"></image>
            </view>
        </block>
        <view class="home_vip_title">
            <image mode="" src="{{img}}newImgs/hshs.png"></image>
        </view>
        <view class="home_vip_items">
            <view bindtap="gotoZp" class="home_vip_items2">
                <image mode="" src="{{img}}newImgs/xydcj.png"></image>
            </view>
            <view class="home_vip_items2">
                <view bindtap="gotoSignIn" class="home_vip_item">
                    <image mode="" src="{{img}}newImgs/qdyl.png"></image>
                </view>
                <view bindtap="gotoSecurity" class="home_vip_item">
                    <image mode="" src="{{img}}newImgs/smcfw.png"></image>
                </view>
            </view>
        </view>
        <view class="home_vip_title">
            <image mode="" src="{{img}}sws/hszx.png"></image>
        </view>
        <view class="home_vip_items">
            <view bindtap="gotoBrandList" class="home_vip_item">
                <image mode="" src="{{img}}newImgs/pphd.png"></image>
            </view>
            <view bindtap="gotoVideoList" class="home_vip_item">
                <image mode="" src="{{img}}newImgs/spzs.png"></image>
            </view>
            <view bindtap="gotoProList" class="home_vip_item">
                <image mode="" src="{{img}}newImgs/cpck.png"></image>
            </view>
            <view bindtap="gotoStoreClockIn" class="home_vip_item">
                <image mode="" src="{{img}}newImgs/mddkNew2.png"></image>
            </view>
        </view>
    </view>
    <prop bindcloseProp="closeProp" bindconfirmProp="confirmProp" bindgotoSureHead="gotoSureHead" propNum="{{propNum}}" webUrlSrc="{{webUrlSrc}}" wx:if="{{propState}}"></prop>
    <footer></footer>
    <view class="footHeight"></view>
    <view class="tabbarProps">
        <tabbar actice="home"></tabbar>
    </view>
</view>
