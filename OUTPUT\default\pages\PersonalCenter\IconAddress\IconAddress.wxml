<view class="IconAddress">
    <map class="map" id="mapId" latitude="{{latitude}}" longitude="{{longitude}}"></map>
    <view class="IconAddress_input">
        <view class="IconAddress_input_b">
            <view class="IconAddress_input_icon">
                <image mode="" src="{{img}}newVersion/013.png"></image>
            </view>
            <input value="{{query}}" placeholder="请输入搜索地点" placeholderClass="placeholder"></input>
            <view bindtap="search" class="IconAddress_input_b_seach">搜索</view>
        </view>
    </view>
    <view class="IconAddress_box">
        <block wx:if="{{!noListState}}">
            <view bindtap="select" class="IconAddress_box_item" data-lat="{{item.location.lat}}" data-lng="{{item.location.lng}}" wx:for="{{poiResults}}" wx:key="index">
                <view class="IconAddress_box_item_title">{{item.name}}</view>
                <view class="IconAddress_box_item_subtitle">{{item.address}}</view>
            </view>
        </block>
        <view class="noListData" wx:else>暂无数据</view>
    </view>
    <footer class="footer"></footer>
</view>
