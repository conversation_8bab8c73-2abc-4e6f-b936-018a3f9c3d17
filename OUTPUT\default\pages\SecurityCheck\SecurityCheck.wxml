<view class="sk">
    <view class="sk_top">
        <image mode="" src="{{bakcgroundImg}}" wx:if="{{bakcgroundImg}}"></image>
        <image mode="" src="{{img}}newVersion/068.png" wx:else></image>
    </view>
    <view class="sk_bot" wx:if="{{!isOldCode}}">
        <view class="sk_bot_text">
            <view class="sk_bot_text_title">{{welComeTitle}}</view>
            <view class="sk_bot_text_subtitle">官方防伪验证系统</view>
        </view>
        <view bindtap="showRule" class="sk_bot_rule" wx:if="{{isShowRule!=0}}">
            <image mode="" src="{{img}}newVersion/022.png"></image>
        </view>
        <view class="sk_bot_resultImg" wx:if="{{queryState!=null}}">
            <image mode="" src="{{img}}newVersion/027.png" wx:if="{{queryState}}"></image>
            <image mode="" src="{{img}}newVersion/069.png" wx:else></image>
        </view>
        <rich-text class="cnt" nodes="{{queryResults}}"></rich-text>
        <view class="sk_bot_codeSeach">
            <view class="sk_bot_codeSeach_time">首次查询时间：<text style="color:#000000;">{{firstData||'-'}}</text>
            </view>
            <view class="sk_bot_codeSeach_time">被授权商名称：<text style="color:#000000;">{{shopName||'-'}}</text>
            </view>
            <view class="sk_bot_codeSeach_time">授权期限：<text style="color:#000000;">{{shopData||'-'}}</text>
            </view>
        </view>
        <block wx:if="{{templateId!=''&&isShowActButton!=0}}">
            <view bindtap="gotoActivities" class="sk_bot_button" wx:if="{{userInfo.mobile}}">参与抽奖活动</view>
            <view class="sk_bot_button" wx:else>参与抽奖活动<button bindgetphonenumber="getPhoneNumber" class="buttonPhone" openType="getPhoneNumber"></button>
            </view>
        </block>
    </view>
    <view class="sk_bot" wx:else>
        <view class="sk_bot_text">
            <view class="sk_bot_text_title">{{welComeTitle}}</view>
            <view class="sk_bot_text_subtitle">官方防伪验证系统</view>
        </view>
        <view bindtap="showRule" class="sk_bot_rule">
            <image mode="" src="{{img}}newVersion/022.png"></image>
        </view>
        <view class="sk_bot_resultImg" wx:if="{{queryState!=null}}">
            <image mode="" src="{{img}}newVersion/027.png" wx:if="{{queryState}}"></image>
            <image mode="" src="{{img}}newVersion/069.png" wx:else></image>
        </view>
        <view class="oldCodeStyle" wx:if="{{isOldCodeState}}">
            <view style="margin-top:20px;">您好，您所查询的产品是PEANUTS花生漫画正版授权产品，感谢购买!</view>
        </view>
        <view class="oldCodeStyle" wx:else>您所查询的数码不存在，谨防假冒，或核对后重新输入。谢谢您的使用！</view>
        <block wx:if="{{isOldCodeState}}">
            <view bindtap="gotoActivities" class="sk_bot_button" style="margin-top:100px;" wx:if="{{userInfo.mobile}}">参与抽奖活动</view>
            <view class="sk_bot_button" style="margin-top:100px;" wx:else>参与抽奖活动<button bindgetphonenumber="getPhoneNumber" class="buttonPhone" openType="getPhoneNumber"></button>
            </view>
        </block>
    </view>
    <footer class="footer"></footer>
    <prop bindcloseProp="closeProp" bindshowGZH="showGZH" energyNumber="{{energyNumber}}" propNum="{{propNum}}" ruleText="{{ruleText}}" wx:if="{{propState}}"></prop>
</view>
