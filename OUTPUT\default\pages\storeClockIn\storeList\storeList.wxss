.storeList {
    background: #f6f6f6;
    box-sizing: border-box;
    min-height: 100vh;
    overflow: hidden;
    padding-bottom: 180rpx;
    width: 750rpx
}

.ClockInRecords_top {
    background: url("https://dm-assets.supercarrier8.com/wobei//newVersion/012.png");
    background-size: 100% 100%;
    display: -webkit-flex;
    display: flex;
    height: 76rpx
}

.top_item {
    height: 76rpx;
    line-height: 76rpx;
    margin-left: 20rpx;
    margin-right: 20rpx;
    position: relative
}

.top_item_choose {
    color: #000;
    font-weight: 700
}

.top_item_choose,.top_item_noChoose {
    font-family: Source Han Sans CN;
    font-size: 28rpx;
    text-align: center
}

.top_item_noChoose {
    color: #3c3c3c;
    font-weight: 500
}

.top_item_xian {
    background: #000;
    border-radius: 4rpx;
    bottom: 0;
    height: 8rpx;
    left: 50%;
    position: absolute;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 56rpx
}

.storeList_item {
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 0 6rpx 7rpx 0 rgba(0,0,0,.03);
    box-sizing: border-box;
    margin: 9rpx auto 5rpx;
    min-height: 151rpx;
    padding-bottom: 20rpx;
    padding-left: 26rpx;
    padding-top: 20rpx;
    position: relative;
    width: 724rpx
}

.storeList_item_name {
    font-size: 30rpx;
    font-weight: 700
}

.storeList_item_name,.storeList_item_phone {
    color: #000;
    font-family: Source Han Sans CN;
    width: 600rpx
}

.storeList_item_phone {
    font-size: 20rpx;
    font-weight: 500
}

.storeList_item_icon {
    height: 70rpx;
    position: absolute;
    right: 38rpx;
    top: 38rpx;
    width: 70rpx
}

.ClockInRecords_text {
    background: #f6f6f6;
    bottom: 0;
    box-sizing: border-box;
    color: silver;
    font-family: Arial;
    font-size: 18rpx;
    font-weight: 400;
    height: 80rpx;
    left: 0;
    padding-top: 20rpx;
    position: fixed;
    text-align: center;
    width: 100%
}
