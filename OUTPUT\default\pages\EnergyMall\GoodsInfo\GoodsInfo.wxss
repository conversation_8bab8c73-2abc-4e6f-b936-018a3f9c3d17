.GoodsInfo {
    background: #f6f6f6;
    min-height: 100vh;
    width: 100%
}

.GoodsInfo_top {
    background: url("https://dm-assets.supercarrier8.com/wobei/newVersion/005.png");
    background-size: 100% 100%;
    overflow: hidden;
    width: 750rpx
}

.GoodsInfo_top_bot {
    -webkit-align-items: center;
    align-items: center;
    box-sizing: border-box;
    display: -webkit-flex;
    display: flex;
    height: 98rpx;
    margin: 0 auto;
    padding-left: 168rpx;
    width: 709rpx
}

.GoodsInfo_top_bot_text {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: #393939;
    display: -webkit-box;
    font-family: Source Han Sans CN;
    font-size: 32rpx;
    font-weight: 700;
    overflow: hidden;
    text-overflow: ellipsis
}

.GoodsInfo_top_img {
    margin: 20rpx auto 0;
    position: relative;
    width: 709rpx
}

.GoodsInfo_top_img_i {
    border-radius: 15rpx;
    height: 709rpx;
    overflow: hidden;
    width: 709rpx
}

.EnergyMall_list_item_radio {
    background: url("https://dm-assets.supercarrier8.com/wobei/newVersion/015.png");
    background-size: 100% 100%;
    bottom: -80rpx;
    height: 161rpx;
    left: 0;
    position: absolute;
    width: 159rpx
}

.EnergyMall_list_item_radio_num {
    color: #010101;
    font-family: Agenda;
    font-size: 49rpx;
    font-weight: 900;
    margin-top: 24rpx;
    text-align: center
}

.EnergyMall_list_item_radio_text {
    color: #010101;
    font-family: Source Han Sans CN;
    font-size: 24rpx;
    font-weight: 400;
    text-align: center
}

.GoodsInfo_info {
    background: #fff;
    box-sizing: border-box;
    height: 170rpx;
    padding-top: 32rpx;
    width: 750rpx
}

.GoodsInfo_info_title {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    box-sizing: border-box;
    color: #393939;
    display: -webkit-box;
    font-family: Source Han Sans CN;
    font-size: 26rpx;
    font-weight: 500;
    overflow: hidden;
    padding-left: 34rpx;
    padding-right: 38rpx;
    text-overflow: ellipsis
}

.GoodsInfo_info_bot {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    padding: 0 30rpx
}

.GoodsInfo_info_bot_num {
    color: #d80e34;
    font-family: Source Han Sans CN;
    font-size: 30rpx;
    font-weight: 700
}

.GoodsInfo_info_bot_kc {
    color: #b4b4b4;
    font-family: Source Han Sans CN;
    font-size: 20rpx;
    font-weight: 400
}

.GoodsInfo_title {
    color: #343434;
    font-family: Source Han Sans CN;
    font-size: 32rpx;
    font-weight: 500;
    height: 78rpx;
    line-height: 78rpx;
    text-align: center
}

.GoodsInfo_imgs {
    background: #fff;
    width: 100%
}

.GoodsInfo_bot {
    bottom: 0;
    left: 0;
    position: fixed
}

.GoodsInfo_button {
    background: #000;
    color: #fff;
    font-family: Source Han Sans CN;
    font-size: 38rpx;
    font-weight: 700;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    width: 750rpx
}
