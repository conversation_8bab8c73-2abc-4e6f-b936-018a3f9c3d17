var e, n = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  a = (e = require("../../../87624F60549B04BFE10427674BE30D65.js")) && e.__esModule ? e : {
    default: e
  },
  r = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  o = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  u = require("../../../83F188C3549B04BFE597E0C403C30D65.js");
var i = getApp();
Page({
  data: {
    img: i.globalData.img,
    userInfo: {}
  },
  gotoSetName: function() {
    wx.navigateTo({
      url: "/pages/PersonalCenter/SetName/SetName"
    })
  },
  getHeadImg: function() {
    var e = this;
    return t(n().mark((function i() {
      var s;
      return n().wrap((function(i) {
        for (;;) switch (i.prev = i.next) {
          case 0:
            return i.next = 2, (0, r.uploadImg)(!0);
          case 2:
            s = i.sent, console.log("headimgInfo", s), (0, r.loadingOpen)(), (0, o.updateHeadImage)({
              headImage: s
            }).then(function() {
              var o = t(n().mark((function t(o) {
                return n().wrap((function(n) {
                  for (;;) switch (n.prev = n.next) {
                    case 0:
                      return (0, r.loadingClose)(), n.next = 3, (0, u.getUser)();
                    case 3:
                      e.setData({
                        userInfo: a.default.data.userInfo
                      });
                    case 4:
                    case "end":
                      return n.stop()
                  }
                }), t)
              })));
              return function(e) {
                return o.apply(this, arguments)
              }
            }());
          case 6:
          case "end":
            return i.stop()
        }
      }), i)
    })))()
  },
  onLoad: function(e) {},
  onReady: function() {},
  onShow: function() {
    this.setData({
      userInfo: a.default.data.userInfo
    })
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {}
});