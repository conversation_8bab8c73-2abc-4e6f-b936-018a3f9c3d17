var e = require("../../../6D59C885549B04BF0B3FA082E5940D65.js");
Component({
  properties: {
    isUrl: {
      type: Boolean,
      value: !0
    },
    src: {
      type: String,
      value: ""
    },
    mode: {
      type: String,
      value: "aspectFit"
    },
    lazyLoad: {
      type: Boolean,
      value: !0
    },
    showMenuByLongpress: {
      type: Boolean,
      value: !1
    },
    multiple: {
      type: Number,
      value: 1
    }
  },
  data: {
    imgUrl: e.imgUrl,
    imgVersion: e.imgVersion,
    imgInfo: {}
  },
  methods: {
    handLoad: function(e) {
      var t = e.detail,
        i = t.width,
        a = t.height;
      this.setData({
        "imgInfo.width": i / Number(this.data.multiple),
        "imgInfo.height": a / Number(this.data.multiple)
      })
    },
    onClick: function() {}
  }
});