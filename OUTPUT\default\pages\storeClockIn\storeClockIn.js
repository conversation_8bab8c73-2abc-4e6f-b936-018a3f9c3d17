var t, e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  a = require("../../@babel/runtime/helpers/asyncToGenerator"),
  n = require("../../A4000F75549B04BFC2666772D6B30D65.js"),
  r = require("../../1AA24145549B04BF7CC42942C3240D65.js"),
  o = (t = require("../../87624F60549B04BFE10427674BE30D65.js")) && t.__esModule ? t : {
    default: t
  },
  s = require("../../6F218526549B04BF0947ED2133340D65.js");
var i = getApp();
Page({
  data: {
    img: i.globalData.img,
    isClockState: !0,
    propState: !1,
    propNum: 23,
    latitude: "",
    longitude: "",
    storeId: "",
    storeInfoList: [],
    storeName: "-",
    storeAddress: "-",
    storeLatitude: "",
    storeLongitude: "",
    addressInfo: "",
    clockMsg: "",
    indicatorDots: !1,
    autoplay: !0,
    interval: 3e3,
    duration: 500,
    circular: !0,
    bannerImgs: [],
    currentIndex: 0
  },
  onNext: function() {
    this.data.currentIndex < this.data.bannerImgs.length - 1 ? this.setData({
      currentIndex: this.data.currentIndex += 1
    }) : this.setData({
      currentIndex: 0
    })
  },
  onPrev: function() {
    0 == this.data.currentIndex ? this.setData({
      currentIndex: this.data.bannerImgs.length - 1
    }) : this.setData({
      currentIndex: this.data.currentIndex -= 1
    })
  },
  gotoWebUrl: function(t) {
    var e = t.currentTarget.dataset.item;
    e.linkUrl && (console.log(e), -1 != e.linkUrl.indexOf("http") ? wx.navigateTo({
      url: "/pages/WebUrl/WebUrl?url=" + e.linkUrl
    }) : -1 != e.linkUrl.indexOf("#小程序://") ? wx.navigateToMiniProgram({
      shortLink: e.linkUrl,
      envVersion: "release"
    }) : wx.navigateTo({
      url: e.linkUrl
    }))
  },
  gotoList: function() {
    wx.navigateTo({
      url: "/pages/storeClockIn/ClockInRecords/ClockInRecords"
    })
  },
  gotoLookStore: function() {
    wx.navigateTo({
      url: "/pages/storeClockIn/storeList/storeList?latitude=" + this.data.latitude + "&longitude=" + this.data.longitude + "&districtCode=" + this.data.districtCode
    })
  },
  gotoRefresh: function() {
    console.log("刷新当前")
  },
  onLoad: function(t) {},
  onReady: function() {},
  closeProp: function() {
    this.setData({
      propState: !1
    })
  },
  gotoclockIn: function() {
    var t = this;
    o.default.data.userInfo.mobile ? "-" != this.data.storeName || "-" != this.data.storeAddress ? ((0, s.loadingOpen)(), (0, n.client)({
      address: this.data.addressInfo,
      latitude: this.data.latitude,
      longitude: this.data.longitude,
      orgId: this.data.storeId,
      storeLatitude: this.data.storeLatitude,
      storeLongitude: this.data.storeLongitude,
      storeName: this.data.storeName,
      telephone: o.default.data.userInfo.mobile,
      userId: o.default.data.registerUserInfo.id,
      userName: o.default.data.userInfo.memberName
    }).then((function(e) {
      (0, s.loadingClose)(), console.log("接口返回去数据", e), 200 != e.code ? t.setData({
        propState: !0,
        propNum: 23,
        clockMsg: e.message
      }) : t.setData({
        propState: !0,
        propNum: 22,
        isClockState: !1
      })
    }))) : this.setData({
      propState: !0,
      propNum: 23,
      clockMsg: "无法获取门店信息，请前往附近门店打卡！"
    }) : this.setData({
      propState: !0,
      propNum: 6
    })
  },
  onShow: function() {
    this.refresh()
  },
  refresh: function() {
    var t = this;
    return a(e().mark((function a() {
      var n, i, d, u, l, c, g;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.next = 2, t.configQuery();
          case 2:
            return n = e.sent, i = n.data.banner ? JSON.parse(n.data.banner) : [], t.setData({
              remark: n.data.remark,
              energyNumber: n.data.integral,
              bannerImgs: i.length > 0 ? i : [{
                imgUrl: "http://dm-assets.supercarrier8.com/wobei/store/storeBgNews0704.png"
              }]
            }), e.next = 7, (0, s.getUserGPS)(!0, "请授权地理位置信息，否则无法匹配门店进行打卡！");
          case 7:
            return d = e.sent, console.log("gpsInfo", d), e.next = 11, (0, r.getGPSAddress)(d);
          case 11:
            return u = e.sent, t.setData({
              latitude: o.default.data.userInfoGPS.latitude,
              longitude: o.default.data.userInfoGPS.longitude,
              addressInfo: u
            }), e.next = 15, t.getConvertGps();
          case 15:
            return l = e.sent, t.setData({
              districtCode: l.data.result.addressComponent.adcode
            }), console.log("addressCodeInfo", l), console.log("addressInfo", u), e.next = 21, t.getStore();
          case 21:
            if (c = e.sent, console.log("storeInfoList", c), 0 == c.data.length) {
              e.next = 29;
              break
            }
            return t.setData({
              storeName: c.data[0].name,
              storeAddress: c.data[0].address,
              storeId: c.data[0].id,
              storeLatitude: c.data[0].f1,
              storeLongitude: c.data[0].f0
            }), e.next = 27, t.seachStore();
          case 27:
            g = e.sent, t.setData({
              isClockState: g.data
            });
          case 29:
          case "end":
            return e.stop()
        }
      }), a)
    })))()
  },
  getConvertGps: function() {
    var t = this;
    return new Promise((function(e, a) {
      (0, n.convertGps)({
        latitude: t.data.latitude,
        longitude: t.data.longitude
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  configQuery: function() {
    return new Promise((function(t, e) {
      (0, n.configQuery)({}).then((function(e) {
        t(e)
      }))
    }))
  },
  seachStore: function() {
    var t = this;
    return new Promise((function(e, a) {
      (0, n.canClock)({
        orgId: t.data.storeId,
        userId: o.default.data.registerUserInfo.id
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  getStore: function() {
    var t = this;
    return new Promise((function(e, a) {
      (0, n.storeList)({
        districtCode: t.data.districtCode,
        latitude: t.data.latitude,
        longitude: t.data.longitude
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {}
});