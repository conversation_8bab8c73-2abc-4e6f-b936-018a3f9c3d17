<view class="ClockInRecords">
    <view class="ClockInRecords_top">
        <view bindtap="changeTopTab" class="top_item" data-item="{{item}}" wx:for="{{topList}}" wx:key="index">
            <view class="top_item_choose" wx:if="{{topIndex==item.index}}">{{item.name}}</view>
            <view class="top_item_noChoose" wx:else>{{item.name}}</view>
            <view class="top_item_xian" wx:if="{{topIndex==item.index}}"></view>
        </view>
    </view>
    <view class="ClockInRecords_title">
        <view class="ClockInRecords_title_box">
            <view class="ClockInRecords_title_time">打卡时间</view>
            <view class="ClockInRecords_title_store">打卡门店</view>
            <view class="ClockInRecords_title_price">能量奖励</view>
        </view>
    </view>
    <block wx:if="{{list.length!=0}}">
        <view class="ClockInRecords_title_item" wx:for="{{list}}" wx:key="index">
            <view class="item_time">{{item.clockTime}}</view>
            <view class="item_store">{{item.storeName}}</view>
            <view class="item_price" style="color:#F0D027">+{{item.integral}}</view>
        </view>
    </block>
    <view class="noListData" wx:else>暂无数据</view>
    <footer class="footer"></footer>
</view>
