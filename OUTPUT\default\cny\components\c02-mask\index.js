var e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  n = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  t = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  r = require("../../../71D07D80549B04BF17B615870C540D65.js");
wx.getAccountInfoSync().miniProgram.envVersion;
Component({
  properties: {},
  data: {
    imgUrl: t.imgUrl,
    imgVersion: "v" + Math.random().toString(36).substr(2),
    open: !1,
    isOnline: !1
  },
  lifetimes: {
    ready: function() {
      this.isOnline()
    }
  },
  methods: {
    isOnline: function() {
      var t = this;
      return n(e().mark((function n() {
        var i, a, s;
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return e.prev = 0, e.next = 3, (0, r.homeOnline)();
            case 3:
              i = e.sent, a = i.code, s = i.data, 200 === a && t.setData({
                isOnline: s.online
              }), e.next = 13;
              break;
            case 9:
              e.prev = 9, e.t0 = e.catch(0), console.log("homeOnline error: ", e.t0), t.setData({
                isOnline: !1
              });
            case 13:
            case "end":
              return e.stop()
          }
        }), n, null, [
          [0, 9]
        ])
      })))()
    },
    handleOpenMask: function() {
      this.setData({
        open: !0
      })
    },
    handleOpenKv: function(e) {
      var n = e.currentTarget.dataset.index;
      1 === Number(n) && wx.navigateTo({
        url: "/cny/pages/home/<USER>"
      }), this.handleMaskClose()
    },
    handleMaskClose: function() {
      this.setData({
        isOnline: !1
      })
    }
  }
});