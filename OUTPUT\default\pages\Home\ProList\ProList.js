var t = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  a = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  e = getApp();
Page({
  data: {
    img: e.globalData.img,
    pageIndex: 1,
    pageSize: 5,
    totalPage: 0,
    list: []
  },
  gotoWebUrl: function(t) {
    var a = t.currentTarget.dataset.item;
    console.log(a), (a.linkUrl || "").indexOf("#小程序:") > -1 ? wx.navigateToMiniProgram({
      shortLink: a.linkUrl
    }) : wx.navigateTo({
      url: "/pages/WebUrl/WebUrl?url=" + a.linkUrl
    })
  },
  onLoad: function(t) {
    this.getProduct()
  },
  getProduct: function() {
    var e = this;
    (0, a.loadingOpen)(), (0, t.getProductList)({
      sort: "-rowUpdateDate",
      flag: 1,
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize
    }).then((function(t) {
      (0, a.loadingClose)(), 200 == t.code ? e.setData({
        pageIndex: e.data.pageIndex += 1,
        totalPage: Math.ceil(t.data.total / e.data.pageSize),
        list: e.data.list.concat(t.data.list)
      }) : (0, a.toastModel)(t.message)
    }))
  },
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onShareAppMessage: function() {
    return {
      title: ""
    }
  },
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {
    this.data.pageIndex <= this.data.totalPage ? this.getProduct() : (0, a.toastModel)("暂无更多数据了~")
  }
});