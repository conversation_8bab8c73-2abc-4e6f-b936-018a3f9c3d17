<view class="tabbar_box">
    <view bindtap="gotoUrl" class="tabbar_box_item" data-index="{{index}}" wx:for="{{IconList}}" wx:key="index">
        <view class="tabbar_box_item_icon--{{item.path}}">
            <image src="{{!item.state?item.iconPath:item.selectedIconPath}}"></image>
        </view>
        <view class="tabbar_box_item_text" style="color:{{item.state?'#F8C934':'#000000'}}">{{item.name}}</view>
    </view>
</view>
