var i = require("6D59C885549B04BF0B3FA082E5940D65.js");
module.exports = {
  data: [{
    name: "宇宙的猜想 x 史努比",
    appid: "wx5178b71574779907",
    link: "#小程序://CS宇航局/mLyLeXSPFAp3ahB",
    price: "268~698",
    img: i.imgUrl + "75/collection/i2.jpg?v=" + i.imgVersion,
    shortLink: !0
  }, {
    name: "卡游 史努比卡牌",
    appid: "wxf85977e258306a7a",
    link: "#小程序://抽卡机/PqBmQSoZDECCbct",
    price: "2~39",
    img: i.imgUrl + "75/collection/i6.jpg?v=" + i.imgVersion,
    shortLink: !0
  }, {
    name: "猫空 史努比单肩包",
    appid: "wxd09ff65260fd525c",
    link: "#小程序://猫的天空之城微商城/V3RezlvqGnir4Fx",
    price: "89",
    img: i.imgUrl + "75/collection/i7.jpg?v=" + i.imgVersion,
    shortLink: !0
  }, {
    name: "猫空 史努比明信片套装",
    appid: "wxd09ff65260fd525c",
    link: "#小程序://猫的天空之城微商城/qE4uwPbtCOb7QqK",
    price: "32",
    img: i.imgUrl + "75/collection/i8.jpg?v=" + i.imgVersion,
    shortLink: !0
  }, {
    name: "杂物社 保温杯、陶瓷餐盘、冰箱贴",
    appid: "wxd7a0e27393123a5d",
    link: "#小程序://九木杂物社/VyesabW8FfdayNC",
    price: "39.9~89.9",
    img: i.imgUrl + "75/collection/i3.jpg?v=" + i.imgVersion,
    shortLink: !0
  }, {
    name: "杂物社 随写软抄本、书签透卡包",
    appid: "wxd7a0e27393123a5d",
    link: "#小程序://九木杂物社/lgfMMKOMKal0D8J",
    price: "9.9~18",
    img: i.imgUrl + "75/collection/i5.jpg?v=" + i.imgVersion,
    shortLink: !0
  }, {
    name: "杂物社 双面背单肩包、牛仔报童包",
    appid: "wxd7a0e27393123a5d",
    link: "#小程序://九木杂物社/snIylo9zfQoYf3H",
    price: "99.9~159",
    img: i.imgUrl + "75/collection/i4.jpg?v=" + i.imgVersion,
    shortLink: !0
  }, {
    name: "史努比和朋友们 拼装模型",
    appid: "wx754f3c9a66228d26",
    link: "#小程序://有漫社/COMtq2ZyCFya5sa",
    price: "128",
    img: i.imgUrl + "75/collection/i9.jpg?v=" + i.imgVersion,
    shortLink: !0
  }, {
    name: "33 PERCENT LUCK x 史努比",
    appid: "wxfbc01a7c3d19ad5b",
    link: "#小程序://33PERCENTLUCKOFFICIAL/1pG1d3ndppUvlWc",
    price: "99~599",
    img: i.imgUrl + "75/collection/i1.jpg?v=" + i.imgVersion,
    shortLink: !0
  }, {
    name: "谢瑞麟珠宝 x 史努比",
    appid: "wx05d5a3aec023949b",
    link: "#小程序://TSL谢瑞麟网上旗舰店/K0n5SD2dnJiIO4F",
    price: "2890~69999",
    img: i.imgUrl + "75/collection/i10.jpg?v=" + i.imgVersion,
    shortLink: !0
  }]
};