Page({
  data: {
    latitude: "",
    longitude: ""
  },
  showOpen: function() {
    wx.openLocation({
      latitude: this.data.latitude,
      longitude: this.data.longitude,
      scale: 18
    })
  },
  onLoad: function() {
    var t = this;
    wx.getLocation({
      type: "wgs84",
      altitude: !0,
      success: function(i) {
        console.log(i), t.setData({
          latitude: i.latitude,
          longitude: i.longitude
        })
      },
      fail: function(t) {}
    })
  }
});