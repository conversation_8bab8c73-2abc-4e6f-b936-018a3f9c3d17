Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var e = require("cny/miniprogram_npm/mobx-miniprogram/index.js"),
  t = (0, e.observable)({
    name: "<PERSON>",
    aMonitor: "",
    updateNameAction: (0, e.action)((function(e) {
      this.name = e
    })),
    updateAmonitor: (0, e.action)((function(e) {
      this.aMonitor = e
    }))
  });
exports.default = t;