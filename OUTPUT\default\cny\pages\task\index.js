var e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  n = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  r = require("../../../71D07D80549B04BF17B615870C540D65.js"),
  a = require("../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  t = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  o = require("../../../A5622344549B04BFC3044B435F450D65.js");
Page({
  data: {
    statusHeaderBarHeight: t.statusHeaderBarHeight,
    imgUrl: t.imgUrl,
    imgVersion: t.imgVersion,
    screenInfo: (0, a.getScreenInfo)(),
    currentJf: 0,
    UserShareInfo: [],
    userInfo: {},
    UserExchangeInfo: [],
    proClickState: !1
  },
  getUserInfoFn: function() {
    var t = this;
    return n(e().mark((function n() {
      var o, s, c;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.prev = 0, e.next = 3, (0, r.getUserInfo)();
          case 3:
            o = e.sent, s = o.code, c = o.data, 200 === s && ((0, a.setStorageSync)("userInfo", c.userInfo), (0, a.setStorageSync)("openid", c.userInfo.openId), (0, a.setStorageSync)("currentJf", c.currentJf), t.setData({
              currentJf: c.currentJf,
              userInfo: c.userInfo
            })), e.next = 12;
            break;
          case 9:
            e.prev = 9, e.t0 = e.catch(0), console.log("task userInfo error: ", e.t0);
          case 12:
          case "end":
            return e.stop()
        }
      }), n, null, [
        [0, 9]
      ])
    })))()
  },
  checkUserShareInfoFn: function() {
    var a = this;
    return n(e().mark((function n() {
      var t, o, s;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.prev = 0, e.next = 3, (0, r.checkUserShareInfo)();
          case 3:
            t = e.sent, o = t.code, s = t.data, 200 == o && a.setData({
              UserShareInfo: s.UserShareInfo
            }), e.next = 12;
            break;
          case 9:
            e.prev = 9, e.t0 = e.catch(0), console.log("checkUserShareInfo error: ", e.t0);
          case 12:
          case "end":
            return e.stop()
        }
      }), n, null, [
        [0, 9]
      ])
    })))()
  },
  getUserExchangeInfo: function() {
    var a = this;
    return n(e().mark((function n() {
      var t, o, s, c;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.prev = 0, e.next = 3, (0, r.getUserExchangeInfo)();
          case 3:
            t = e.sent, o = t.code, s = t.data, 200 == o && a.setData({
              UserExchangeInfo: null !== (c = s.UserExchangeInfo) && void 0 !== c ? c : [],
              proClickState: !0
            }), e.next = 12;
            break;
          case 9:
            e.prev = 9, e.t0 = e.catch(0), console.log("getUserExchangeInfo error: ", e.t0);
          case 12:
          case "end":
            return e.stop()
        }
      }), n, null, [
        [0, 9]
      ])
    })))()
  },
  handleExchange: function() {
    var a = this;
    return n(e().mark((function n() {
      var t, s;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.prev = 0, e.next = 3, (0, r.exchangeDrawNub)({});
          case 3:
            t = e.sent, s = t.code, t.data, 200 == s && (wx.showToast({
              icon: "none",
              title: "兑换成功"
            }), a.getUserInfoFn(), a.getUserExchangeInfo()), e.next = 12;
            break;
          case 9:
            e.prev = 9, e.t0 = e.catch(0), console.log("exchangeDrawNub error: ", e.t0);
          case 12:
            o.pageClickEvent({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "任务页面",
              page_name: "任务页面",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/task/index",
              button_name: "任务02-立即兑换"
            });
          case 13:
          case "end":
            return e.stop()
        }
      }), n, null, [
        [0, 9]
      ])
    })))()
  },
  handleGoTask: function() {
    wx.navigateTo({
      url: "/pages/TaskCenter/TaskCenter"
    }), o.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "任务页面",
      page_name: "任务页面",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/task/index",
      button_name: "任务03-立即前往"
    })
  },
  handlStartDraw: function() {
    wx.navigateTo({
      url: "/cny/pages/drawLots/index"
    }), o.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "任务页面",
      page_name: "任务页面",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/task/index",
      button_name: "任务04-立即抽奖"
    })
  },
  onLoad: function(r) {
    return n(e().mark((function n() {
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            wx.hideShareMenu({
              menus: ["shareAppMessage"]
            }), o.pageView({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "任务页面",
              page_name: "任务页面",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/task/index"
            });
          case 2:
          case "end":
            return e.stop()
        }
      }), n)
    })))()
  },
  onReady: function() {},
  onShow: function() {
    var r = this;
    return n(e().mark((function n() {
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.next = 2, r.getUserInfoFn();
          case 2:
            return e.next = 4, r.checkUserShareInfoFn();
          case 4:
            return e.next = 6, r.getUserExchangeInfo();
          case 6:
          case "end":
            return e.stop()
        }
      }), n)
    })))()
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  handleAddDrawLogs: function() {
    var a = this;
    return n(e().mark((function n() {
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.next = 2, (0, r.userShare)();
          case 2:
            return e.next = 4, a.checkUserShareInfoFn();
          case 4:
            o.pageClickEvent({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "任务页面",
              page_name: "任务页面",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/task/index",
              button_name: "任务01-立即分享"
            });
          case 5:
          case "end":
            return e.stop()
        }
      }), n)
    })))()
  },
  onShareAppMessage: function() {
    return n(e().mark((function n() {
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.abrupt("return", t.shareOptions);
          case 1:
          case "end":
            return e.stop()
        }
      }), n)
    })))()
  }
});