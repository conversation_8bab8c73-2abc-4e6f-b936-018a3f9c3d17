Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
try {
  global.Array = Array
} catch (o) {
  console.log("Array not support in MINA, skip")
}
try {
  global.Buffer = Buffer
} catch (o) {
  console.log("Buffer not support in MINA, skip")
}
try {
  global.DataView = DataView
} catch (o) {
  console.log("DataView not support in MINA, skip")
}
try {
  global.Date = Date
} catch (o) {
  console.log("Date not support in MINA, skip")
}
try {
  global.Error = Error
} catch (o) {
  console.log("Error not support in MINA, skip")
}
try {
  global.Float32Array = Float32Array
} catch (o) {
  console.log("Float32Array not support in MINA, skip")
}
try {
  global.Float64Array = Float64Array
} catch (o) {
  console.log("Float64Array not support in MINA, skip")
}
try {
  global.Function = Function
} catch (o) {
  console.log("Function not support in MINA, skip")
}
try {
  global.Int8Array = Int8Array
} catch (o) {
  console.log("Int8Array not support in MINA, skip")
}
try {
  global.Int16Array = Int16Array
} catch (o) {
  console.log("Int16Array not support in MINA, skip")
}
try {
  global.Int32Array = Int32Array
} catch (o) {
  console.log("Int32Array not support in MINA, skip")
}
try {
  global.Map = Map
} catch (o) {
  console.log("Map not support in MINA, skip")
}
try {
  global.Math = Math
} catch (o) {
  console.log("Math not support in MINA, skip")
}
try {
  global.Object = Object
} catch (o) {
  console.log("Object not support in MINA, skip")
}
try {
  global.Promise = Promise
} catch (o) {
  console.log("Promise not support in MINA, skip")
}
try {
  global.RegExp = RegExp
} catch (o) {
  console.log("RegExp not support in MINA, skip")
}
try {
  global.Set = Set
} catch (o) {
  console.log("Set not support in MINA, skip")
}
try {
  global.String = String
} catch (o) {
  console.log("String not support in MINA, skip")
}
try {
  global.Symbol = Symbol
} catch (o) {
  console.log("Symbol not support in MINA, skip")
}
try {
  global.TypeError = TypeError
} catch (o) {
  console.log("TypeError not support in MINA, skip")
}
try {
  global.Uint8Array = Uint8Array
} catch (o) {
  console.log("Uint8Array not support in MINA, skip")
}
try {
  global.Uint8ClampedArray = Uint8ClampedArray
} catch (o) {
  console.log("Uint8ClampedArray not support in MINA, skip")
}
try {
  global.Uint16Array = Uint16Array
} catch (o) {
  console.log("Uint16Array not support in MINA, skip")
}
try {
  global.Uint32Array = Uint32Array
} catch (o) {
  console.log("Uint32Array not support in MINA, skip")
}
try {
  global.WeakMap = WeakMap
} catch (o) {
  console.log("WeakMap not support in MINA, skip")
}
try {
  global._ = _
} catch (o) {
  console.log("_ not support in MINA, skip")
}
try {
  global.clearTimeout = clearTimeout
} catch (o) {
  console.log("clearTimeout not support in MINA, skip")
}
try {
  global.isFinite = isFinite
} catch (o) {
  console.log("isFinite not support in MINA, skip")
}
try {
  global.parseInt = parseInt
} catch (o) {
  console.log("parseInt not support in MINA, skip")
}
try {
  global.setTimeout = setTimeout
} catch (o) {
  console.log("setTimeout not support in MINA, skip")
}
exports.default = {};