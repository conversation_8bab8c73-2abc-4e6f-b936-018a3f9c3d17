var t = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  e = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  a = getApp();
Page({
  data: {
    img: a.globalData.img,
    pageIndex: 1,
    pageSize: 5,
    totalPage: 0,
    list: []
  },
  gotoWebUrl: function(t) {
    var e = t.currentTarget.dataset.item;
    console.log(e), (e.linkUrl || "").indexOf("#小程序:") > -1 ? wx.navigateToMiniProgram({
      shortLink: e.linkUrl
    }) : wx.navigateTo({
      url: "/pages/WebUrl/WebUrl?url=" + e.linkUrl
    })
  },
  onLoad: function(t) {
    this.getVideo()
  },
  getVideo: function() {
    var a = this;
    (0, e.loadingOpen)(), (0, t.getVideoList)({
      sort: "-rowUpdateDate",
      flag: 1,
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize
    }).then((function(t) {
      (0, e.loadingClose)(), 200 == t.code ? a.setData({
        pageIndex: a.data.pageIndex += 1,
        totalPage: Math.ceil(t.data.total / a.data.pageSize),
        list: a.data.list.concat(t.data.list)
      }) : (0, e.toastModel)(t.message)
    }))
  },
  onReady: function() {},
  onShareAppMessage: function() {
    return {
      title: ""
    }
  },
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {
    this.data.pageIndex <= this.data.totalPage ? this.getVideo() : (0, e.toastModel)("暂无更多数据了~")
  }
});