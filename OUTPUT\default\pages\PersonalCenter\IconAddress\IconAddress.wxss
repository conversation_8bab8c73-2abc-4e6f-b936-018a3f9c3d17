.IconAddress {
    background: #f6f6f6;
    box-sizing: border-box;
    height: 100vh;
    padding-bottom: 180rpx;
    width: 100%
}

.map {
    height: 300px;
    width: 100%
}

.IconAddress_input {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    height: 94rpx;
    -webkit-justify-content: center;
    justify-content: center;
    width: 100%
}

.placeholder {
    color: #d4d4d4;
    font-family: Source <PERSON>N;
    font-size: 26rpx;
    font-weight: 400
}

.IconAddress_input_icon {
    height: 26rpx;
    margin-right: 10rpx;
    width: 20rpx
}

.IconAddress_input_b {
    -webkit-align-items: center;
    align-items: center;
    background: #fff;
    border: 2rpx solid #cfcfcf;
    border-radius: 33rpx;
    box-sizing: border-box;
    display: -webkit-flex;
    display: flex;
    height: 66rpx;
    padding: 0 160rpx 0 20rpx;
    position: relative;
    width: 704rpx
}

.IconAddress_input_b_seach {
    background: #000;
    border-radius: 26rpx;
    color: #fff;
    font-family: Source <PERSON> Sans CN;
    font-size: 26rpx;
    font-weight: 500;
    height: 51rpx;
    line-height: 51rpx;
    position: absolute;
    right: 10rpx;
    text-align: center;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 123rpx;
    z-index: 9999
}

.IconAddress_box {
    background: #fff;
    height: calc(100vh - 854rpx);
    overflow: hidden;
    overflow-y: auto
}

.IconAddress_box_item {
    border-bottom: 2rpx solid #ececec;
    box-sizing: border-box;
    margin: 0 auto;
    padding: 20rpx 0;
    width: 704rpx
}

.IconAddress_box_item_title {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 30rpx;
    font-weight: 500;
    font-weight: 700
}

.IconAddress_box_item_subtitle {
    color: #464648;
    font-family: Source Han Sans CN;
    font-size: 26rpx;
    font-weight: 500
}
