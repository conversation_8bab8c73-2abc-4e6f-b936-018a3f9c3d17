Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var e = wx.getAccountInfoSync().miniProgram.envVersion,
  t = wx.getSystemInfoSync(),
  s = {
    develop: "https://api-fat.yesno.com.cn",
    trial: "https://api-fat.yesno.com.cn",
    release: "https://restapi.supercarrier8.com",
    test_release: ""
  },
  r = s[e].indexOf("tx") > -1,
  a = {
    env: e,
    activityId: "release" == e || r ? "909" : "263016",
    shopActivityId: "release" == e || r ? "671" : "262828",
    enterpriseNo: "release" == e || r ? "************" : "************",
    ApiURL: s[e],
    isIpx: t.safeArea.top > 20
  };
exports.default = a;