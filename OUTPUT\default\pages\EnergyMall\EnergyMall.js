var t = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  e = require("../../@babel/runtime/helpers/asyncToGenerator"),
  a = require("../../A4000F75549B04BFC2666772D6B30D65.js"),
  i = require("../../6F218526549B04BF0947ED2133340D65.js"),
  n = getApp();
Page({
  data: {
    img: n.globalData.img,
    tabList: [],
    energyList: [],
    giftList: [],
    searchKey: "",
    groupId: "",
    userId: "",
    scoreStart: "",
    scoreEnd: "",
    pageIndex: 1,
    pageSize: 10,
    totalPage: 0,
    tabActive: 0,
    energyActive: 0
  },
  changeTab: function(t) {
    var e = t.currentTarget.dataset.index,
      a = t.currentTarget.dataset.item;
    console.log("item", a), this.setData({
      tabActive: e,
      groupId: a.id,
      energyActive: 0,
      scoreStart: "",
      scoreEnd: "",
      pageIndex: 1,
      giftList: []
    }), this.getGift()
  },
  changeEnergy: function(t) {
    var e = t.currentTarget.dataset.index,
      a = t.currentTarget.dataset.item;
    console.log("item", a), this.setData({
      energyActive: e,
      scoreStart: a.f1,
      scoreEnd: a.f2,
      pageIndex: 1,
      giftList: []
    }), this.getGift()
  },
  gotoGoodsInfo: function(t) {
    var e = t.currentTarget.dataset.item;
    wx.navigateTo({
      url: "/pages/EnergyMall/GoodsInfo/GoodsInfo?id=" + e.id
    })
  },
  getGift: function() {
    var t = this;
    return new Promise((function(e, i) {
      (0, a.getGiftList)({
        searchKey: t.data.searchKey,
        groupId: t.data.groupId,
        userId: t.data.userId,
        scoreStart: t.data.scoreStart,
        scoreEnd: t.data.scoreEnd,
        activityId: n.globalData.shopActivityId,
        targetPeople: 0,
        pageIndex: t.data.pageIndex,
        pageSize: t.data.pageSize
      }).then((function(a) {
        t.setData({
          pageIndex: t.data.pageIndex += 1,
          giftList: t.data.giftList.concat(a.data.list),
          totalPage: Math.ceil(a.data.total / t.data.pageSize)
        }), e(a)
      }))
    }))
  },
  getGroup: function() {
    var t = this;
    return new Promise((function(e, i) {
      (0, a.getGroupList)({
        targetPeople: 0
      }).then((function(a) {
        200 == a.code && (a.data.unshift({
          groupName: "全部",
          id: ""
        }), t.setData({
          tabList: a.data
        }), console.log("tabList", t.data.tabList)), e(a)
      }))
    }))
  },
  getIntegralClassification: function() {
    var t = this;
    return new Promise((function(e, i) {
      (0, a.dictionaryItems)({
        code: "jfselect"
      }).then((function(a) {
        a.data.forEach((function(t) {
          t.lookValue = t.f1 + "-" + t.f2
        })), a.data.unshift({
          configCode: "jfselect",
          f1: "",
          f2: "",
          f3: "",
          f4: "",
          f5: "",
          f6: "",
          f7: "",
          f8: "",
          f9: "",
          f10: "",
          id: "",
          itemDescription: "全部",
          itemName: "",
          itemValue: "",
          lookValue: "全部"
        }), t.setData({
          energyList: a.data
        }), e(a)
      }))
    }))
  },
  seachList: function() {
    var a = this;
    return e(t().mark((function e() {
      return t().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return a.setData({
              pageIndex: 1,
              giftList: [],
              groupId: "",
              scoreStart: "",
              scoreEnd: "",
              tabActive: 0,
              energyActive: 0
            }), (0, i.loadingOpen)(), t.next = 4, a.getGift();
          case 4:
            (0, i.loadingClose)();
          case 5:
          case "end":
            return t.stop()
        }
      }), e)
    })))()
  },
  onLoad: function(t) {},
  onReady: function() {},
  onShow: function() {
    this.setData({
      pageIndex: 1,
      pageSize: 10,
      giftList: [],
      groupId: "",
      scoreStart: "",
      scoreEnd: "",
      tabActive: 0,
      energyActive: 0
    }), (0, i.loadingOpen)(), Promise.all([this.getGift(), this.getGroup(), this.getIntegralClassification()]).then((function(t) {
      (0, i.loadingClose)(), console.log("请求完成", t)
    }))
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {
    console.log("his.data.pageIndex", this.data.pageIndex), console.log("his.data.totalPage", this.data.totalPage), this.data.pageIndex <= this.data.totalPage ? this.getGift() : (0, i.toastModel)("暂无更多数据了~")
  }
});