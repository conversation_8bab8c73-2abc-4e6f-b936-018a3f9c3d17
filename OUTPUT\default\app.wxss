wx-image {
    display: block;
    height: 100%;
    width: 100%
}

.imageHead {
    border-radius: 50%
}

wx-textarea {
    -webkit-appearance: none;
    appearance: none;
    font-size: 24rpx;
    outline: none;
    resize: none
}

wx-input,wx-textarea {
    border: none;
    height: 100%;
    width: 100%
}

wx-input {
    background: none;
    display: block;
    margin: 0;
    padding: 0
}

.tabbarProps {
    background: #fff;
    padding-bottom: env(safe-area-inset-bottom)
}

.tabbarProp,.tabbarProps {
    bottom: 0;
    position: fixed;
    width: 100%;
    z-index: 999
}

.tabbarProp_bot {
    background: #fff
}

.footHeight,.tabbarProp_bot {
    padding-bottom: env(safe-area-inset-bottom)
}

.footHeight {
    height: 110rpx
}

.footer {
    bottom: 0
}

.footer,.footerMall {
    left: 0;
    position: fixed;
    width: 100%
}

.footerMall {
    bottom: 100rpx
}

.noListData {
    color: silver;
    font-family: Source Han Sans CN;
    font-size: 32rpx;
    font-weight: 500;
    height: 800rpx;
    line-height: 800rpx;
    text-align: center
}

.pageNoList {
    overflow: hidden;
    width: 100%
}

.pageNoList_img {
    height: 313rpx;
    margin: 300rpx auto 0;
    width: 248rpx
}

.pageNoList_text {
    font-size: 32rpx;
    margin-top: 10px;
    text-align: center
}

.GoodsInfo_imgsBot {
    height: 802rpx;
    width: 750rpx
}
