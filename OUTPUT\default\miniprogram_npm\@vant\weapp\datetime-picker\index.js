var e = function() {
    return (e = Object.assign || function(e) {
      for (var t, n = 1, a = arguments.length; n < a; n++)
        for (var r in t = arguments[n]) Object.prototype.hasOwnProperty.call(t, r) && (e[r] = t[r]);
      return e
    }).apply(this, arguments)
  },
  t = function(e, t, n) {
    if (n || 2 === arguments.length)
      for (var a, r = 0, u = t.length; r < u; r++) !a && r in t || (a || (a = Array.prototype.slice.call(t, 0, r)), a[r] = t[r]);
    return e.concat(a || Array.prototype.slice.call(t))
  };
Object.defineProperty(exports, "__esModule", {
  value: !0
});
var n = require("../common/component"),
  a = require("../common/validator"),
  r = require("../picker/shared"),
  u = (new Date).getFullYear();

function i(e, t, n) {
  return Math.min(Math.max(e, t), n)
}

function o(e) {
  return "00".concat(e).slice(-2)
}

function s(e) {
  for (void 0 === e && (e = "1"); isNaN(parseInt(e, 10));) e = e.slice(1);
  return parseInt(e, 10)
}

function l(e, t) {
  return 32 - new Date(e, t - 1, 32).getDate()
}
var c = function(e, t) {
  return t
};
(0, n.VantComponent)({
  classes: ["active-class", "toolbar-class", "column-class"],
  props: e(e({}, r.pickerProps), {
    value: {
      type: null,
      observer: "updateValue"
    },
    filter: null,
    type: {
      type: String,
      value: "datetime",
      observer: "updateValue"
    },
    showToolbar: {
      type: Boolean,
      value: !0
    },
    formatter: {
      type: null,
      value: c
    },
    minDate: {
      type: Number,
      value: new Date(u - 10, 0, 1).getTime(),
      observer: "updateValue"
    },
    maxDate: {
      type: Number,
      value: new Date(u + 10, 11, 31).getTime(),
      observer: "updateValue"
    },
    minHour: {
      type: Number,
      value: 0,
      observer: "updateValue"
    },
    maxHour: {
      type: Number,
      value: 23,
      observer: "updateValue"
    },
    minMinute: {
      type: Number,
      value: 0,
      observer: "updateValue"
    },
    maxMinute: {
      type: Number,
      value: 59,
      observer: "updateValue"
    }
  }),
  data: {
    innerValue: Date.now(),
    columns: []
  },
  methods: {
    updateValue: function() {
      var e = this,
        t = this.data,
        n = this.correctValue(t.value),
        a = n === t.innerValue;
      this.updateColumnValue(n).then((function() {
        a || e.$emit("input", n)
      }))
    },
    getPicker: function() {
      if (null == this.picker) {
        this.picker = this.selectComponent(".van-datetime-picker");
        var e = this.picker,
          n = e.setColumnValues;
        e.setColumnValues = function() {
          for (var a = [], r = 0; r < arguments.length; r++) a[r] = arguments[r];
          return n.apply(e, t(t([], a, !0), [!1], !1))
        }
      }
      return this.picker
    },
    updateColumns: function() {
      var e = this.data.formatter,
        t = void 0 === e ? c : e,
        n = this.getOriginColumns().map((function(e) {
          return {
            values: e.values.map((function(n) {
              return t(e.type, n)
            }))
          }
        }));
      return this.set({
        columns: n
      })
    },
    getOriginColumns: function() {
      var e = this.data.filter;
      return this.getRanges().map((function(t) {
        var n = t.type,
          a = t.range,
          r = function(e, t) {
            for (var n = -1, a = Array(e < 0 ? 0 : e); ++n < e;) a[n] = t(n);
            return a
          }(a[1] - a[0] + 1, (function(e) {
            var t = a[0] + e;
            return "year" === n ? "".concat(t) : o(t)
          }));
        return e && (r = e(n, r)), {
          type: n,
          values: r
        }
      }))
    },
    getRanges: function() {
      var e = this.data;
      if ("time" === e.type) return [{
        type: "hour",
        range: [e.minHour, e.maxHour]
      }, {
        type: "minute",
        range: [e.minMinute, e.maxMinute]
      }];
      var t = this.getBoundary("max", e.innerValue),
        n = t.maxYear,
        a = t.maxDate,
        r = t.maxMonth,
        u = t.maxHour,
        i = t.maxMinute,
        o = this.getBoundary("min", e.innerValue),
        s = o.minYear,
        l = o.minDate,
        c = [{
          type: "year",
          range: [s, n]
        }, {
          type: "month",
          range: [o.minMonth, r]
        }, {
          type: "day",
          range: [l, a]
        }, {
          type: "hour",
          range: [o.minHour, u]
        }, {
          type: "minute",
          range: [o.minMinute, i]
        }];
      return "date" === e.type && c.splice(3, 2), "year-month" === e.type && c.splice(2, 3), c
    },
    correctValue: function(e) {
      var t, n = this.data,
        r = "time" !== n.type;
      if (r && (t = e, !(0, a.isDef)(t) || isNaN(new Date(t).getTime()))) e = n.minDate;
      else if (!r && !e) {
        var u = n.minHour;
        e = "".concat(o(u), ":00")
      }
      if (!r) {
        var s = e.split(":"),
          l = s[0],
          c = s[1];
        return l = o(i(l, n.minHour, n.maxHour)), c = o(i(c, n.minMinute, n.maxMinute)), "".concat(l, ":").concat(c)
      }
      return e = Math.max(e, n.minDate), e = Math.min(e, n.maxDate)
    },
    getBoundary: function(e, t) {
      var n, a = new Date(t),
        r = new Date(this.data["".concat(e, "Date")]),
        u = r.getFullYear(),
        i = 1,
        o = 1,
        s = 0,
        c = 0;
      return "max" === e && (i = 12, o = l(a.getFullYear(), a.getMonth() + 1), s = 23, c = 59), a.getFullYear() === u && (i = r.getMonth() + 1, a.getMonth() + 1 === i && (o = r.getDate(), a.getDate() === o && (s = r.getHours(), a.getHours() === s && (c = r.getMinutes())))), (n = {})["".concat(e, "Year")] = u, n["".concat(e, "Month")] = i, n["".concat(e, "Date")] = o, n["".concat(e, "Hour")] = s, n["".concat(e, "Minute")] = c, n
    },
    onCancel: function() {
      this.$emit("cancel")
    },
    onConfirm: function() {
      this.$emit("confirm", this.data.innerValue)
    },
    onChange: function() {
      var e, t = this,
        n = this.data,
        a = this.getPicker(),
        r = this.getOriginColumns();
      if ("time" === n.type) {
        var u = a.getIndexes();
        e = "".concat(+r[0].values[u[0]], ":").concat(+r[1].values[u[1]])
      } else {
        var i = (u = a.getIndexes()).map((function(e, t) {
            return r[t].values[e]
          })),
          o = s(i[0]),
          c = s(i[1]),
          m = l(o, c),
          p = s(i[2]);
        "year-month" === n.type && (p = 1), p = p > m ? m : p;
        var h = 0,
          v = 0;
        "datetime" === n.type && (h = s(i[3]), v = s(i[4])), e = new Date(o, c - 1, p, h, v)
      }
      e = this.correctValue(e), this.updateColumnValue(e).then((function() {
        t.$emit("input", e), t.$emit("change", a)
      }))
    },
    updateColumnValue: function(e) {
      var t = this,
        n = [],
        a = this.data.type,
        r = this.data.formatter || c,
        u = this.getPicker();
      if ("time" === a) {
        var i = e.split(":");
        n = [r("hour", i[0]), r("minute", i[1])]
      } else {
        var s = new Date(e);
        n = [r("year", "".concat(s.getFullYear())), r("month", o(s.getMonth() + 1))], "date" === a && n.push(r("day", o(s.getDate()))), "datetime" === a && n.push(r("day", o(s.getDate())), r("hour", o(s.getHours())), r("minute", o(s.getMinutes())))
      }
      return this.set({
        innerValue: e
      }).then((function() {
        return t.updateColumns()
      })).then((function() {
        return u.setValues(n)
      }))
    }
  },
  created: function() {
    var e = this,
      t = this.correctValue(this.data.value);
    this.updateColumnValue(t).then((function() {
      e.$emit("input", t)
    }))
  }
});