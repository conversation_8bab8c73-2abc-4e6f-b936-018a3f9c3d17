var t = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  e = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  a = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  i = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  n = getApp();
Page({
  data: {
    img: n.globalData.img,
    tabList: [{
      name: "全部",
      state: 1
    }, {
      name: "待发货",
      state: 3
    }, {
      name: "待收货",
      state: 4
    }, {
      name: "已完成",
      state: 5
    }],
    activeState: 1,
    list: [],
    pageIndex: 1,
    pageSize: 6,
    totalPage: 0
  },
  resettingList: function() {
    this.setData({
      pageIndex: 1,
      list: []
    }), this.getOrder()
  },
  suerOrderGoods: function(t) {
    var e = this,
      a = t.currentTarget.dataset.item;
    (0, i.confirmReceipt)({
      id: a.id
    }).then((function(t) {
      e.resettingList()
    }))
  },
  delOrder: function(t) {
    var e = this,
      a = t.currentTarget.dataset.item;
    (0, i.deleteOrder)({
      id: a.id
    }).then((function(t) {
      e.resettingList()
    }))
  },
  changeTab: function(t) {
    var e = t.currentTarget.dataset.item;
    console.log(e), this.setData({
      activeState: e.state
    }), this.resettingList()
  },
  gotoOrderInfo: function(t) {
    var e = t.currentTarget.dataset.item;
    console.log(e), wx.navigateTo({
      url: "/pages/PersonalCenter/OrderInfo/OrderInfo?id=" + e.id
    })
  },
  receiveRed: function(n) {
    return e(t().mark((function e() {
      var r, s, o;
      return t().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return console.log(n), r = n.currentTarget.dataset.item, (0, a.loadingOpen)(), t.next = 5, (0, i.getVirtualGiftCardList)({
              id: r.id
            });
          case 5:
            return s = t.sent, t.next = 8, (0, i.getVirtualGiftCard)({
              id: s.data[0].id
            });
          case 8:
            o = t.sent, (0, a.loadingClose)(), wx.showRedPackage({
              url: "https://support.weixin.qq.com/cgi-bin/mmsupport-bin/showredpacket?receiveuri=".concat(o.data, "&check_type=1#wechat_redirect")
            }), console.log("当前数据", r);
          case 12:
          case "end":
            return t.stop()
        }
      }), e)
    })))()
  },
  getOrder: function() {
    var t = this;
    (0, a.loadingOpen)(), (0, i.getOrderList)({
      type: this.data.activeState,
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize
    }).then((function(e) {
      (0, a.loadingClose)(), e.data.list.forEach((function(t) {
        t.orderDetail.forEach((function(t) {
          -1 != t.giftName.indexOf("红包封面") ? t.redState = !0 : t.redState = !1
        }))
      })), t.setData({
        pageIndex: t.data.pageIndex += 1,
        list: t.data.list.concat(e.data.list),
        totalPage: Math.ceil(e.data.total / t.data.pageSize)
      }), console.log("this.data.list", t.data.list)
    }))
  },
  onLoad: function(t) {
    this.resettingList()
  },
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {
    console.log("this.data.pageIndex<this.data.totalPage", this.data.pageIndex, this.data.totalPage), this.data.pageIndex <= this.data.totalPage ? this.getOrder() : (0, a.toastModel)("暂无更多数据了~")
  }
});