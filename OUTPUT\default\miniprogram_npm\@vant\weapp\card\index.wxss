@import "..\common\index.wxss";

.van-card {
    background-color: var(--card-background-color,#fafafa);
    box-sizing: border-box;
    color: var(--card-text-color,#323233);
    font-size: var(--card-font-size,12px);
    padding: var(--card-padding,8px 16px);
    position: relative
}

.van-card__header {
    display: -webkit-flex;
    display: flex
}

.van-card__header--center {
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: center;
    justify-content: center
}

.van-card__thumb {
    -webkit-flex: none;
    flex: none;
    height: var(--card-thumb-size,88px);
    margin-right: var(--padding-xs,8px);
    position: relative;
    width: var(--card-thumb-size,88px)
}

.van-card__thumb:empty {
    display: none
}

.van-card__img {
    border-radius: 8px;
    height: 100%;
    width: 100%
}

.van-card__content {
    display: -webkit-flex;
    display: flex;
    -webkit-flex: 1;
    flex: 1;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    min-height: var(--card-thumb-size,88px);
    min-width: 0;
    position: relative
}

.van-card__content--center {
    -webkit-justify-content: center;
    justify-content: center
}

.van-card__desc,.van-card__title {
    word-wrap: break-word
}

.van-card__title {
    font-weight: 700;
    line-height: var(--card-title-line-height,16px)
}

.van-card__desc {
    color: var(--card-desc-color,#646566);
    line-height: var(--card-desc-line-height,20px)
}

.van-card__bottom {
    line-height: 20px
}

.van-card__price {
    color: var(--card-price-color,#ee0a24);
    display: inline-block;
    font-size: var(--card-price-font-size,12px);
    font-weight: 700
}

.van-card__price-integer {
    font-size: var(--card-price-integer-font-size,16px)
}

.van-card__price-decimal,.van-card__price-integer {
    font-family: var(--card-price-font-family,Avenir-Heavy,PingFang SC,Helvetica Neue,Arial,sans-serif)
}

.van-card__origin-price {
    color: var(--card-origin-price-color,#646566);
    display: inline-block;
    font-size: var(--card-origin-price-font-size,10px);
    margin-left: 5px;
    text-decoration: line-through
}

.van-card__num {
    float: right
}

.van-card__tag {
    left: 0;
    position: absolute!important;
    top: 2px
}

.van-card__footer {
    -webkit-flex: none;
    flex: none;
    text-align: right;
    width: 100%
}
