var t, a = require("../../6F218526549B04BF0947ED2133340D65.js"),
  e = require("../../A4000F75549B04BFC2666772D6B30D65.js");
(t = require("../../87624F60549B04BFE10427674BE30D65.js")) && t.__esModule;
var i = getApp();
Page({
  data: {
    img: i.globalData.img,
    tabIndex: 0,
    tabList: [{
      value: "申请进行中",
      index: 0
    }, {
      value: "往期申请",
      index: 1
    }],
    list: [],
    total: 0,
    pageIndex: 1,
    pageSize: 10
  },
  gotoInfo: function(t) {
    var a = t.currentTarget.dataset.item;
    wx.navigateTo({
      url: "/pages/hssws/Info/Info?activityNo=" + a.activityNo
    })
  },
  changeTab: function(t) {
    console.log(t.currentTarget.dataset.item);
    var a = t.currentTarget.dataset.item;
    this.setData({
      tabIndex: a.index,
      list: [],
      pageIndex: 1
    }), this.getList()
  },
  getPhoneNumber: function(t) {
    console.log("e", t)
  },
  onLoad: function(t) {},
  onReady: function() {},
  getList: function() {
    var t = this;
    0 == this.data.tabIndex ? ((0, a.loadingOpen)(), (0, e.underWayList)({
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize
    }).then((function(e) {
      (0, a.loadingClose)(), 200 == e.code ? t.setData({
        pageIndex: t.data.pageIndex += 1,
        list: t.data.list.concat(e.data.list),
        total: Math.ceil(e.data.total / t.data.pageSize)
      }) : (0, a.toastModel)(e.message)
    }))) : ((0, a.loadingOpen)(), (0, e.appliedList)({
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize
    }).then((function(e) {
      (0, a.loadingClose)(), 200 == e.code ? t.setData({
        pageIndex: t.data.pageIndex += 1,
        list: t.data.list.concat(e.data.list),
        total: Math.ceil(e.data.total / t.data.pageSize)
      }) : (0, a.toastModel)(e.message)
    })))
  },
  onShow: function() {
    this.setData({
      list: [],
      pageIndex: 1
    }), this.getList()
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {
    console.log("this.data.total", this.data.total), this.data.pageIndex <= this.data.total ? this.getList() : (0, a.toastModel)("暂无更多数据了~")
  },
  onShareAppMessage: function() {}
});