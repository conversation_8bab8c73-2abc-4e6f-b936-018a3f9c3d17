var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/typeof"),
  a = require("../../@babel/runtime/helpers/asyncToGenerator"),
  n = require("../../A4000F75549B04BFC2666772D6B30D65.js"),
  r = require("../../6F218526549B04BF0947ED2133340D65.js"),
  o = require("../../8F86AFB2549B04BFE9E0C7B593D30D65.js"),
  i = s(require("../../87624F60549B04BFE10427674BE30D65.js")),
  u = s(require("../../B6135D02549B04BFD0753505DD930D65.js"));

function s(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
var c = getApp();
Page({
  data: {
    img: c.globalData.img,
    list: [],
    daylist: [{
      name: "<PERSON><PERSON>"
    }, {
      name: "TUE"
    }, {
      name: "WED"
    }, {
      name: "THU"
    }, {
      name: "FRI"
    }, {
      name: "SAT"
    }, {
      name: "SUN"
    }],
    monthList: [{
      num: 1,
      name: "Jan"
    }, {
      num: 2,
      name: "Feb"
    }, {
      num: 3,
      name: "Mar"
    }, {
      num: 4,
      name: "Apr"
    }, {
      num: 5,
      name: "May"
    }, {
      num: 6,
      name: "Jun"
    }, {
      num: 7,
      name: "Jul"
    }, {
      num: 8,
      name: "Aug"
    }, {
      num: 9,
      name: "Sep"
    }, {
      num: 10,
      name: "Oct"
    }, {
      num: 11,
      name: "Nov"
    }, {
      num: 12,
      name: "Dec"
    }],
    curDate: "",
    calendarList: [],
    curMonthNumber: "",
    curYear: "",
    curMonth: "",
    curYearMonth: "",
    nextOrUpper: !1,
    isHaveUpper: !0,
    isHaveNext: !1,
    mouthNumber: 0,
    IncDecYearNum: 0,
    IncDecNum: 0,
    signNum: 0,
    propState: !1,
    propNum: 13,
    cardVersion: "",
    totalMonthSignCount: "",
    continuousSignDays: "",
    residueNumber: 0,
    ruleText: "",
    rewardValue: 0,
    repairDate: null,
    continuousRewardValue: 0
  },
  signCur: function(e) {
    var t = e.currentTarget.dataset.item,
      a = e.currentTarget.dataset.index;
    0 != t.num && (console.log("item", t), console.log("index", a), this.data.calendarList.forEach((function(e) {
      e.isChooseState = !1
    })), this.data.calendarList[a].isChooseState = !0, this.setData({
      calendarList: this.data.calendarList,
      repairDate: t
    }))
  },
  getList: function() {
    var e = this;
    return new Promise((function(t, a) {
      (0, n.getUserSignInfo)({
        signMonth: e.data.curYearMonth
      }).then((function(a) {
        e.setData({
          cardVersion: String(a.data.signCardVersion),
          totalMonthSignCount: Array.from(new Set(a.data.signDateList)).length,
          residueNumber: a.data.totalSignCard - a.data.usedSignCard,
          continuousSignDays: a.data.continuousSignDays
        }), t(a)
      }))
    }))
  },
  getCalendar: function() {
    var r = arguments,
      o = this;
    return a(e().mark((function a() {
      var s, c, l, d, p, m, h, g, D, f, N, S, w, I, M, b, v, x, C, U, V;
      return e().wrap((function(a) {
        for (;;) switch (a.prev = a.next) {
          case 0:
            return s = r.length > 0 && void 0 !== r[0] ? r[0] : 0, c = r.length > 1 && void 0 !== r[1] ? r[1] : 0, console.log("年数量", s, "月数量", c), l = new Date, console.log("curTime", l), d = l.getFullYear() + s, p = null, 0 != s ? p = 12 : (p = l.getMonth() + 1 + c, console.log("curMonth", p)), m = l.getDate(), console.log("curYear", d), console.log("curMonth", p), console.log("curMonth", p, t(p)), h = p >= 10 ? p : "0" + p, o.setData({
              curYearMonth: d + "-" + h,
              mouthNumber: p,
              curYear: d,
              curMonthNumber: p,
              curMonth: o.data.monthList.find((function(e) {
                return p == e.num
              })).name
            }), console.log("curYearMonth", o.data.curYearMonth), console.log("curDate", m), g = new Date(d, p - 1, 1), console.log("curFirstDate", g), D = null, D = 0 != g.getDay() ? g.getDay() - 1 : 6, console.log("firstDay", D), g.getFullYear() + "-" + (g.getMonth() + 1) + "-" + g.getDate(), f = new Date(d, p, 0).getDate(), N = [], a.next = 26, (0, n.getRewordSign)({
              enterpriseNo: u.default.enterpriseNo,
              userId: i.default.data.registerUserInfo.id
            });
          case 26:
            return (S = a.sent) && 200 == S.code && (N = (S.data || []).map((function(e) {
              return e.signDate
            }))), a.next = 30, o.getList();
          case 30:
            for (w = a.sent, console.log("signData", w), I = Array.from(new Set(w.data.signDateList)), M = [], b = 0; b < D; b++) M[b] = {
              num: 0
            };
            v = e().mark((function t() {
              var a, n, r, o, i;
              return e().wrap((function(e) {
                for (;;) switch (e.prev = e.next) {
                  case 0:
                    a = d + "-" + (p >= 10 ? p : "0" + p) + "-" + (x >= 10 ? x : "0" + x), n = "", r = "0", x < m ? n = "before" : x == m ? n = "day" : x > m && (n = "after"), o = I.filter((function(e) {
                      return e == a
                    })), 0 != N.filter((function(e) {
                      return e == a
                    })).length ? r = "2" : 1 == o.length && (r = "1"), i = {
                      date: a,
                      num: x,
                      type: n,
                      sign: r
                    }, M.push(i);
                  case 11:
                  case "end":
                    return e.stop()
                }
              }), t)
            })), x = 1;
          case 37:
            if (!(x <= f)) {
              a.next = 42;
              break
            }
            return a.delegateYield(v(), "t0", 39);
          case 39:
            x++, a.next = 37;
            break;
          case 42:
            if (C = 42 - M.length, M.length < 42)
              for (U = 0; U < C; U++) V = {
                num: 0
              }, M.push(V);
            console.log("bigarr", M), M.forEach((function(e) {
              e.isChooseState = !1
            })), o.setData({
              curDate: m,
              calendarList: M
            }), o.data.nextOrUpper ? o.setData({
              isHaveUpper: !1,
              isHaveNext: !0
            }) : o.setData({
              isHaveUpper: !0,
              isHaveNext: !1
            });
          case 48:
          case "end":
            return a.stop()
        }
      }), a)
    })))()
  },
  closeProp: function() {
    this.setData({
      propState: !1
    })
  },
  showRule: function() {
    this.setData({
      propState: !0,
      propNum: 10
    })
  },
  gotoRepair: function() {
    var e = this;
    if (i.default.data.userInfo.mobile)
      if (0 != this.data.residueNumber)
        if (this.data.repairDate) {
          var t = Date.parse(this.data.currentTime.replace(/-/g, "/")),
            a = Date.parse(this.data.repairDate.date.replace(/-/g, "/"));
          console.log(t, a, t - a);
          1 == this.data.repairDate.sign ? (0, r.toastModel)("选择日期已经签到，无需补签") : t - a < 0 || t - a > 2592e6 ? (0, r.toastModel)("补签卡只能补签30天内日期，请选择正确的补签日期") : t - a == 0 ? (0, r.toastModel)("当前日期不支持补签，请选择签到") : (0, r.showModel)("", "请确定是否对".concat(this.data.repairDate.date, "使用补签卡进行补签")).then((function(t) {
            t && ((0, r.loadingOpen)(), (0, n.fixSignUp)({
              cardVersion: e.data.cardVersion,
              signDate: e.data.repairDate.date
            }).then((function(t) {
              (0, r.loadingClose)(), 200 == t.code && e.setData({
                propState: !0,
                propNum: 12,
                repairDate: null
              })
            })))
          }))
        } else(0, r.toastModel)("请选择需要补签的日期");
    else this.setData({
      propState: !0,
      propNum: 11
    });
    else this.setData({
      propState: !0,
      propNum: 6
    })
  },
  gotoSignIn: function() {
    var e = this;
    if (i.default.data.userInfo.mobile) {
      if (this.data.repairDate)
        if (Date.parse(this.data.currentTime.replace(/-/g, "/")) - Date.parse(this.data.repairDate.date.replace(/-/g, "/")) != 0) return void(0, r.toastModel)("签到只能签到当前日期，不能签到其它日期");
      (0, n.justSign)({}).then((function(t) {
        0 == t.data ? ((0, r.loadingClose)(), e.setData({
          propState: !0,
          propNum: 112
        })) : ((0, r.loadingOpen)(), (0, n.signCallback)({
          enterpriseNo: u.default.enterpriseNo,
          userId: i.default.data.registerUserInfo.id
        }).then((function(t) {
          if ((0, r.loadingClose)(), t && 200 == t.code && t.data && t.data.length > 0) {
            var a = t.data.find((function(e) {
                return "1" == e.rewardType && "0" != e.rewardValue
              })) || {},
              n = t.data.find((function(e) {
                return "2" == e.rewardType && "0" != e.rewardValue
              })) || {},
              o = t.data.find((function(e) {
                return "3" == e.rewardType && "0" != e.rewardValue
              })) || {};
            console.log("signInData11111111111", a, n, o), n && e.setData({
              propState: !0,
              propNum: 13,
              signNum: n.rewardValue,
              nextOrUpper: !1,
              IncDecNum: 0,
              continuousRewardValue: o.rewardValue || 0
            })
          } else e.setData({
            propState: !0,
            propNum: 13,
            signNum: "1",
            nextOrUpper: !1,
            IncDecNum: 0
          })
        })))
      }))
    } else this.setData({
      propState: !0,
      propNum: 6
    })
  },
  confirmProp: function() {
    var e = this;
    if (console.log("111111111111111111111", this.data.propNum, this.data.continuousRewardValue), 13 !== this.data.propNum && 12 !== this.data.propNum || !this.data.continuousRewardValue) this.setData({
      propState: !1,
      continuousRewardValue: 0
    });
    else {
      this.setData({
        propState: !1
      });
      var t = setTimeout((function() {
        clearTimeout(t), e.setData({
          propState: !0,
          propNum: 32,
          signNum: e.data.continuousRewardValue,
          nextOrUpper: !1,
          IncDecNum: 0,
          continuousRewardValue: 0
        })
      }), 100)
    }
    this.getCalendar(this.data.IncDecYearNum, this.data.IncDecNum)
  },
  gotoPreviousMouth: function() {
    this.setData({
      nextOrUpper: !0,
      IncDecNum: this.data.IncDecNum -= 1
    }), console.log("mouthNumber", this.data.mouthNumber), console.log("IncDecNum", -1 * this.data.IncDecNum), this.data.mouthNumber <= -1 * this.data.IncDecNum ? (this.setData({
      IncDecYearNum: this.data.IncDecYearNum -= 1,
      IncDecNum: 12
    }), this.getCalendar(this.data.IncDecYearNum, this.data.IncDecNum)) : this.getCalendar(this.data.IncDecYearNum, this.data.IncDecNum)
  },
  gotoNextMouth: function() {
    console.log("下一个月"), console.log("curMonthNumber", this.data.curMonthNumber), this.setData({
      nextOrUpper: !1,
      IncDecNum: this.data.IncDecNum += 1
    }), console.log("IncDecNum", this.data.IncDecNum), this.getCalendar(0, this.data.IncDecNum)
  },
  onLoad: function(e) {},
  onReady: function() {
    this.setData({
      propState: !0,
      propNum: 31
    })
  },
  onShow: function() {
    var e = new Date,
      t = e.getFullYear(),
      a = e.getMonth() + 1,
      n = e.getDate();
    this.setData({
      currentTime: t + "-" + a + "-" + n,
      nextOrUpper: !1,
      isHaveUpper: !0,
      IncDecNum: 0
    }), this.getCalendar(), this.getRule(), 1 == (0, o.get)("wb_signProp") && (0, o.set)("wb_signPropState", 1)
  },
  getRule: function() {
    var e = this;
    (0, r.loadingOpen)(), (0, n.getSignConfig)({}).then((function(t) {
      (0, r.loadingClose)(), e.setData({
        rewardValue: t.data.rewardValue || 0,
        ruleText: t.data.signRule
      })
    }))
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {}
});