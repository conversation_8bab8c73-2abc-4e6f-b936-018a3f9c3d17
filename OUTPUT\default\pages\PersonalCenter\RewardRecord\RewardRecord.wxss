.RR {
    background: #f7f7f7;
    box-sizing: border-box;
    min-height: 100vh;
    overflow: hidden;
    padding-bottom: 40rpx;
    width: 100%
}

.RR_item {
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 16rpx 4rpx hsla(0,0%,84%,.43);
    margin: 20rpx auto 0;
    min-height: 172rpx;
    position: relative;
    width: 690rpx
}

.RR_item,.RR_item_l {
    display: -webkit-flex;
    display: flex
}

.RR_item_l {
    -webkit-align-items: center;
    align-items: center;
    height: 172rpx;
    -webkit-justify-content: center;
    justify-content: center;
    margin-right: 20rpx;
    width: 192rpx
}

.RR_item_l_img {
    border-radius: 20rpx;
    height: 146rpx;
    overflow: hidden;
    width: 146rpx
}

.RR_item_r {
    box-sizing: border-box;
    height: 172rpx;
    padding-top: 32rpx;
    width: 400rpx
}

.RR_item_r_name {
    color: #000;
    font-family: Source <PERSON>N;
    font-size: 28rpx;
    font-weight: 700;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.RR_item_r_time {
    color: #8e8e8e;
    font-family: Source Han Sans CN;
    font-size: 23rpx;
    font-weight: 400;
    margin-top: 34rpx
}

.RR_item_button {
    background: #000;
    color: #fff
}

.RR_item_button,.RR_item_buttonNew {
    border-radius: 25rpx;
    bottom: 36rpx;
    font-family: Source Han Sans CN;
    font-size: 25rpx;
    font-weight: 500;
    height: 54rpx;
    line-height: 54rpx;
    position: absolute;
    right: 20rpx;
    text-align: center;
    width: 139rpx
}

.RR_item_buttonNew {
    color: #000
}
