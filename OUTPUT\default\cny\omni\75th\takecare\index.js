var n = require("../../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  t = require("../../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  a = require("../../../../C924CA51549B04BFAF42A25667750D65.js");
Page({
  data: {
    statusHeaderBarHeight: n.statusHeaderBarHeight,
    imgUrl: n.imgUrl,
    imgVersion: n.imgVersion,
    screenInfo: (0, t.getScreenInfo)(),
    sData: a.sData,
    sIndex: 0,
    maskFlag: !1
  },
  onLogin: function(n) {
    n.detail.loginStatus && this.initData()
  },
  initData: function() {
    console.log("初始化登录")
  },
  bindSwiperChange: function(n) {
    var t = n.detail.current;
    this.setData({
      sIndex: t
    })
  },
  handlePrev: function() {
    var n = this.data.sIndex;
    --n < 0 && (n = a.sData.length - 1), this.setData({
      sIndex: n
    })
  },
  handleNext: function() {
    var n = this.data,
      t = n.sIndex;
    ++t > n.sData.length - 1 && (t = 0), this.setData({
      sIndex: t
    })
  },
  handleGo: function() {
    var n = this.data,
      t = n.sData,
      a = n.sIndex;
    "公众号" === t[a].type ? wx.openOfficialAccountArticle({
      url: t[a].link
    }) : this.setData({
      maskFlag: !0
    })
  },
  handleClose: function() {
    var n = this.data,
      t = n.sData;
    n.sIndex;
    this.setData({
      maskFlag: !1
    }), wx.navigateToMiniProgram({
      shortLink: t[0].link
    })
  },
  onLoad: function(n) {},
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return n.shareOptions
  }
});