<view class="maskContainer" hoverClass="none" hoverStopPropagation="false" wx:if="{{open}}">
    <view class="maskBox maskImgCenter" hoverClass="none" hoverStopPropagation="false">
        <image binderror="" bindload="" class="maskBg" lazyLoad="false" src="{{imgUrl}}components/rule/bg.png?v={{imgVersion}}"></image>
        <image binderror="" bindload="" catch:tap="handleBackIndex" class="title" lazyLoad="false" src="{{imgUrl}}components/rule/title.png?v={{imgVersion}}"></image>
        <view class="scrollContainer" hoverClass="none" hoverStopPropagation="false">
            <scroll-view scrollY class="scrollBox">
                <image binderror="" bindload="" catch:tap="handleBackIndex" class="content" lazyLoad="false" src="{{imgUrl}}components/rule/content.png?v={{imgVersion}}"></image>
            </scroll-view>
        </view>
        <image binderror="" bindload="" catch:tap="handleMyTask" class="s1" lazyLoad="false" src="{{imgUrl}}components/rule/s1.png?v={{imgVersion}}"></image>
        <image catch:tap="handleClose" class="closeIcon" mode="" src="{{imgUrl}}common/close.png?v={{imgVersion}}"></image>
    </view>
</view>
