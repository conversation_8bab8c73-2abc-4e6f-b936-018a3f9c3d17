var e = function(e, t, i) {
  if (i || 2 === arguments.length)
    for (var o, n = 0, a = t.length; n < a; n++) !o && n in t || (o || (o = Array.prototype.slice.call(t, 0, n)), o[n] = t[n]);
  return e.concat(o || Array.prototype.slice.call(t))
};
Object.defineProperty(exports, "__esModule", {
  value: !0
}), (0, require("../../../common/component").VantComponent)({
  props: {
    title: {
      type: String,
      value: "日期选择"
    },
    subtitle: String,
    showTitle: Boolean,
    showSubtitle: <PERSON><PERSON>an,
    firstDayOfWeek: {
      type: Number,
      observer: "initWeekDay"
    }
  },
  data: {
    weekdays: []
  },
  created: function() {
    this.initWeekDay()
  },
  methods: {
    initWeekDay: function() {
      var t = ["日", "一", "二", "三", "四", "五", "六"],
        i = this.data.firstDayOfWeek || 0;
      this.setData({
        weekdays: e(e([], t.slice(i, 7), !0), t.slice(0, i), !0)
      })
    },
    onClickSubtitle: function(e) {
      this.$emit("click-subtitle", e)
    }
  }
});