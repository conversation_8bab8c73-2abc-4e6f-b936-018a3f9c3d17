var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/asyncToGenerator"),
  a = require("../../A4000F75549B04BFC2666772D6B30D65.js"),
  r = i(require("../../87624F60549B04BFE10427674BE30D65.js")),
  n = i(require("../../B6135D02549B04BFD0753505DD930D65.js")),
  o = require("../../6F218526549B04BF0947ED2133340D65.js");

function i(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
var s = getApp();
Component({
  options: {
    addGlobalClass: !0
  },
  properties: {
    propNum: Number,
    ruleText: String,
    signNum: Number,
    energyNumber: Number,
    exchangeEnergyNumber: Number,
    priceInfo: Object,
    priceMsg: String,
    clockMsg: String,
    giftDetailInfo: Object,
    priceData: Object,
    activityId: String,
    storeName: String,
    priceImgInfo: String,
    webUrlSrc: String,
    newDrawRaffieInfo: Array,
    timeMsg: String,
    allThanks: Boolean
  },
  data: {
    date: (new Date).getTime(),
    img: s.globalData.img,
    columns: [],
    bannerList: [{
      url: "banner1.png",
      chooseUrl: "banner1s.png",
      name: "查理·布朗"
    }, {
      url: "banner2.png",
      chooseUrl: "banner2s.png",
      name: "史努比"
    }, {
      url: "banner3.png",
      chooseUrl: "banner3s.png",
      name: "露西·范佩特"
    }, {
      url: "banner4.png",
      chooseUrl: "banner4s.png",
      name: "莱纳斯·范佩特"
    }, {
      url: "banner5.png",
      chooseUrl: "banner5s.png",
      name: "莎莉·布朗"
    }, {
      url: "banner6.png",
      chooseUrl: "banner6s.png",
      name: "胡士托"
    }],
    bannerActive: 0,
    indexValue: 0,
    currentData: null,
    userName: "",
    userPhone: "",
    provinceProp: !1,
    cityProp: !1,
    areaProp: !1,
    province: "",
    provinceId: "",
    city: "",
    cityId: "",
    area: "",
    areaId: "",
    detailAddress: "",
    carmiValueState: !1,
    carmiValue: "******",
    isLottery10: 0,
    isShowLottery10: !1,
    emailValue: "<EMAIL>",
    hsswsImage: "http://dm-assets.supercarrier8.com/wobei/store/hsswsImage.png"
  },
  methods: {
    gotoStore: function() {
      wx.navigateTo({
        url: "/pages/storeClockIn/storeClockIn"
      })
    },
    getPriceCoupon: function() {
      var e = this;
      return new Promise((function(t, n) {
        (0, a.receiveCouponReward)({
          activityId: e.data.activityId,
          participationId: e.data.priceData.participationId,
          winId: e.data.priceInfo.winId,
          rechargeAccount: r.default.data.userInfo.mobile
        }).then((function(e) {
          t(e)
        }))
      }))
    },
    getCardPw: function() {
      var r = this;
      return t(e().mark((function t() {
        var n;
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              if ((0, o.loadingOpen)(), 3 != r.data.priceInfo.winStatus) {
                e.next = 9;
                break
              }
              return e.next = 4, r.getPriceCoupon();
            case 4:
              if (200 == (n = e.sent).code) {
                e.next = 9;
                break
              }
              return (0, o.loadingClose)(), (0, o.toastModel)(n.message), e.abrupt("return");
            case 9:
              setTimeout((function() {
                (0, a.getCardPassword)({
                  activityId: r.data.activityId,
                  participationId: r.data.priceData.participationId,
                  winId: r.data.priceInfo.winId
                }).then((function(e) {
                  if ((0, o.loadingClose)(), 200 == e.code) r.setData({
                    carmiValueState: !0,
                    carmiValue: e.data.cardPassWord
                  });
                  else {
                    if (r.setData({
                        carmiValueState: !1
                      }), 21210086 == e.code) return void(0, o.toastModel)("卡密正在审核中，请稍后查看");
                    if (21210085 == e.code) return void(0, o.toastModel)("卡密正在发放中，请稍后操作");
                    (0, o.toastModel)(e.message)
                  }
                }))
              }), 2e3);
            case 10:
            case "end":
              return e.stop()
          }
        }), t)
      })))()
    },
    setUserName: function(e) {
      this.setData({
        userName: e.detail.value.replace(/\s+/g, "")
      })
    },
    setUserPhone: function(e) {
      this.setData({
        userPhone: e.detail.value.replace(/\D/g, "")
      })
    },
    setDetailAddress: function(e) {
      this.setData({
        detailAddress: e.detail.value
      })
    },
    gotohb: function() {
      console.log("长按保存")
    },
    getCityList: function(e) {
      var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "";
      return new Promise((function(r, n) {
        (0, a.selectList)({
          level: e,
          regionId: t
        }).then((function(e) {
          r(e)
        }))
      }))
    },
    setColumnList: function(a) {
      var r = arguments,
        n = this;
      return t(e().mark((function t() {
        var o, i, s;
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return o = r.length > 1 && void 0 !== r[1] ? r[1] : "", e.next = 3, n.getCityList(a, o);
            case 3:
              i = e.sent, console.log("columns", i), s = i.data.filter((function(e) {
                return "台湾省" != e.areaName && "香港" != e.areaName && "澳门" != e.areaName && "苏鲁交界" != e.areaName
              })), console.log("dataPro", s), s.forEach((function(e) {
                e.text = e.areaName, e.code = e.areaId
              })), n.setData({
                columns: s
              });
            case 9:
            case "end":
              return e.stop()
          }
        }), t)
      })))()
    },
    showCity: function(a) {
      var r = this;
      return t(e().mark((function t() {
        var n;
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              n = a.currentTarget.dataset.index, console.log(n), r.setData({
                indexValue: n
              }), 0 == n ? r.setColumnList(1, 1e11) : 1 == n ? r.setColumnList(2, r.data.provinceId) : 2 == n && r.setColumnList(3, r.data.cityId), r.setData({
                cityProp: !0
              });
            case 5:
            case "end":
              return e.stop()
          }
        }), t)
      })))()
    },
    onCancel: function() {
      this.setData({
        cityProp: !1
      })
    },
    onConfirm: function(e) {
      var t = e.detail.value;
      0 == this.data.indexValue ? this.setData({
        province: t.areaName,
        provinceId: t.areaId,
        city: "",
        cityId: "",
        area: "",
        areaId: ""
      }) : 1 == this.data.indexValue ? this.setData({
        city: t.areaName,
        cityId: t.areaId,
        area: "",
        areaId: ""
      }) : 2 == this.data.indexValue && this.setData({
        area: t.areaName,
        areaId: t.areaId
      }), console.log("value", t), this.setData({
        cityProp: !1
      })
    },
    closeProp: function() {
      this.triggerEvent("closeProp")
    },
    showGZH: function() {
      this.triggerEvent("showGZH")
    },
    chooseHead: function(e) {
      console.log(e.currentTarget.dataset.index);
      var t = e.currentTarget.dataset.index;
      this.setData({
        bannerActive: t
      })
    },
    gotoSureHead: function() {
      console.log("选择当前头像", this.data.bannerList[this.data.bannerActive]), this.triggerEvent("gotoSureHead", this.data.bannerList[this.data.bannerActive])
    },
    confirmProp: function(e) {
      if (console.log("data", e), 8 == this.data.propNum) {
        var t = e.currentTarget.dataset.index;
        console.log("index", t), this.triggerEvent("confirmProp", t)
      } else this.triggerEvent("confirmProp")
    },
    confirmTips: function() {
      var e = this;
      wx.requestSubscribeMessage({
        tmplIds: ["l4hX6TCSzwebYwz8eIIbT0hVxSkyT-be37sWSA-7RXY"],
        success: function(t) {
          console.log("res1111111111", t), "accept" === t["l4hX6TCSzwebYwz8eIIbT0hVxSkyT-be37sWSA-7RXY"] ? ((0, a.setMessage)({
            openId: r.default.data.openid
          }), e.triggerEvent("confirmProp")) : e.triggerEvent("confirmProp")
        },
        fail: function(t) {
          e.triggerEvent("confirmProp")
        }
      })
    },
    submitUserInfo: function() {
      var e = this.data,
        t = e.userName,
        a = e.userPhone,
        r = e.province,
        n = e.city,
        i = e.area,
        s = e.detailAddress;
      return 0 == t.length ? (0, o.toastModel)("请输入姓名") : 0 != a.length && 11 == a.length && /^0?1[0-9][0-9]\d{8}$/.test(a) ? 0 == r.length || 0 == n.length || 0 == i.length ? (0, o.toastModel)("请选择省市区") : 0 == s.length ? (0, o.toastModel)("请输入详细地址") : void this.triggerEvent("submitUserInfo", {
        userName: t,
        userPhone: a,
        consigneeAddress: r + n + i + s
      }) : (0, o.toastModel)("请输入正确格式的手机号")
    },
    gotoMy: function() {
      wx.switchTab({
        url: "/pages/Home/Home"
      })
    },
    gotoZp: function() {
      wx.navigateTo({
        url: "/pages/drawReward/drawReward"
      }), this.triggerEvent("closeProp")
    },
    gotoEmpower: function() {
      wx.navigateTo({
        url: "/pages/empower/empower"
      })
    },
    gotoMall: function() {
      wx.switchTab({
        url: "/pages/EnergyMall/EnergyMall"
      })
    },
    gotoOrder: function() {
      wx.reLaunch({
        url: "/pages/PersonalCenter/OrderList/OrderList"
      })
    },
    gotoTask: function() {
      wx.navigateTo({
        url: "/pages/TaskCenter/TaskCenter"
      })
    },
    gotoGoods: function() {
      this.triggerEvent("closeProp"), wx.navigateTo({
        url: "/pages/PersonalCenter/RewardRecord/RewardRecord"
      })
    },
    gotoReceive: function(e) {
      var t = e.currentTarget.dataset.value;
      console.log(t), wx.showRedPackage({
        url: "https://support.weixin.qq.com/cgi-bin/mmsupport-bin/showredpacket?receiveuri=".concat(t, "&check_type=1#wechat_redirect")
      })
    },
    gotoCopy: function(e) {
      var t = e.currentTarget.dataset.value;
      wx.setClipboardData({
        data: t,
        success: function(e) {
          wx.showToast({
            title: "复制成功",
            icon: "none",
            mask: "true"
          })
        }
      })
    }
  },
  lifetimes: {
    ready: function() {
      var e = this;
      if (8 == this.data.propNum)(0, a.dictionaryItems)({
        code: "wb_lottery_10",
        enterpriseNo: n.default.enterpriseNo
      }).then((function(t) {
        var a = (t.data || [])[0].itemValue;
        e.setData({
          isLottery10: "1" == a,
          isShowLottery10: !0
        })
      }));
      else if (27 == this.data.propNum) {
        console.log("设置图片时间戳~~~~~~~~~~~~~~~~~~~~");
        var t = Date.now();
        this.setData({
          hsswsImage: this.data.hsswsImage + "?timestamp=" + t
        }), console.log("hsswsImage", this.data.hsswsImage)
      } else 31 == this.data.propNum && (0, a.dictionaryItems)({
        code: "wb_nergy_strategy",
        enterpriseNo: n.default.enterpriseNo
      }).then((function(t) {
        var a = (t.data || []).map((function(e) {
          return {
            value: e.f1,
            label: e.itemName,
            isNum: /^\d+$/.test(e.f1)
          }
        }));
        e.setData({
          energyList: a
        })
      }))
    }
  }
});