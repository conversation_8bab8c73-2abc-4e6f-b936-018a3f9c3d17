<van-button square appParameter="{{appParameter}}" bind:click="onClick" bindcontact="onContact" binderror="onError" bindgetphonenumber="onGetPhoneNumber" bindgetuserinfo="onGetUserInfo" bindlaunchapp="onLaunchApp" bindopensetting="onOpenSetting" businessId="{{businessId}}" customClass="van-goods-action-icon" disabled="{{disabled}}" id="{{id}}" lang="{{lang}}" loading="{{loading}}" openType="{{openType}}" sendMessageImg="{{sendMessageImg}}" sendMessagePath="{{sendMessagePath}}" sendMessageTitle="{{sendMessageTitle}}" sessionFrom="{{sessionFrom}}" showMessageCard="{{showMessageCard}}" size="large">
    <van-icon class="van-goods-action-icon__icon" classPrefix="{{classPrefix}}" color="{{color}}" customClass="icon-class" dot="{{dot}}" info="{{info}}" name="{{icon}}" wx:if="{{icon}}"></van-icon>
    <slot name="icon" wx:else></slot>
    <text class="text-class">{{text}}</text>
</van-button>
