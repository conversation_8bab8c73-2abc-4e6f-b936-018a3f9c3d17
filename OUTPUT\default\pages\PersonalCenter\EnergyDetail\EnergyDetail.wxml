<view class="EnergyDetail">
    <view bindtap="showRule" class="signIn_rule">
        <image mode="" src="{{img}}newVersion/022.png"></image>
    </view>
    <view class="EnergyDetail_top">
        <view class="EnergyDetail_top_energy">
            <view class="EnergyDetail_top_energy_icon">
                <image mode="" src="{{img}}newVersion/021.png"></image>
            </view>
            <view class="EnergyDetail_top_energy_num">{{userInfo.currentJf}}</view>
            <view class="EnergyDetail_top_energy_text">可用能量</view>
        </view>
        <view class="EnergyDetail_top_speck">
            <view class="EnergyDetail_top_speck_lj">累计能量：<text style="font-weight:bold;">{{userInfo.totalJf}}</text>
            </view>
            <view class="EnergyDetail_top_speck_lj" style="margin-left:70rpx;">已消耗：<text style="font-weight:bold;">{{userInfo.usedJf}}</text>
            </view>
        </view>
    </view>
    <view class="OrderList_top">
        <view bindtap="changeTab" class="OrderList_top_item" data-item="{{item}}" wx:for="{{tabList}}" wx:key="index">
            <view style="color:{{activeState==index?'#000000':'#A0ACAB'}}">{{item.name}}</view>
            <view class="OrderList_top_item_x" wx:if="{{activeState==index}}"></view>
        </view>
        <view bindtap="showTime" class="EnergyDetail_date">
            <view style="margin-left:14rpx">{{dateTime}}</view>
            <view class="EnergyDetail_date_jian">
                <image mode="" src="{{img}}whiteXia.png"></image>
            </view>
        </view>
    </view>
    <view class="EnergyDetail_list" wx:if="{{list.length!=0}}">
        <view class="EnergyDetail_list_item" wx:for="{{list}}" wx:key="index">
            <view class="EnergyDetail_list_item_l">
                <view class="EnergyDetail_list_item_l_title">{{item.recordTypeNameNew}}</view>
                <view class="EnergyDetail_list_item_l_time">{{item.rowCreateDate}}</view>
            </view>
            <view class="EnergyDetail_list_item_r" wx:if="{{item.recordType==2}}">-{{item.amount}}</view>
            <view class="EnergyDetail_list_item_r2" wx:elif="{{item.recordType==1}}">+{{item.amount}}</view>
        </view>
    </view>
    <view class="noListData" wx:else>暂无数据</view>
    <van-overlay bind:click="onClickHide" show="{{show}}">
        <van-datetime-picker bind:confirm="suerDate" bind:input="onInput" class="datetime" maxDate="{{maxDate}}" minDate="{{minDate}}" type="year-month" value="{{currentDate}}"></van-datetime-picker>
    </van-overlay>
    <prop bindcloseProp="closeProp" propNum="{{propNum}}" ruleText="{{ruleText}}" wx:if="{{propState}}"></prop>
    <view style="height:140rpx;"></view>
    <footer class="footer"></footer>
</view>
