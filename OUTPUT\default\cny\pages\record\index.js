var e, r = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  n = (e = require("../../../A5622344549B04BFC3044B435F450D65.js")) && e.__esModule ? e : {
    default: e
  },
  o = require("../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  a = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  i = require("../../../71D07D80549B04BF17B615870C540D65.js");
require("../../../03E40C94549B04BF6582649340450D65.js").mount(), Page({
  data: {
    statusHeaderBarHeight: a.statusHeaderBarHeight,
    imgUrl: a.imgUrl,
    imgVersion: a.imgVersion,
    screenInfo: (0, o.getScreenInfo)(),
    recordList: []
  },
  onLoad: function(e) {
    return t(r().mark((function e() {
      return r().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            n.default.pageView({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "上传记录",
              page_name: "上传记录",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/record/index"
            });
          case 1:
          case "end":
            return e.stop()
        }
      }), e)
    })))()
  },
  onShow: function() {
    var e = this;
    return t(r().mark((function t() {
      return r().wrap((function(r) {
        for (;;) switch (r.prev = r.next) {
          case 0:
            return r.next = 2, e.getMyStoryListFn();
          case 2:
          case "end":
            return r.stop()
        }
      }), t)
    })))()
  },
  getMyStoryListFn: function() {
    var e = this;
    return t(r().mark((function t() {
      var n, o, a;
      return r().wrap((function(r) {
        for (;;) switch (r.prev = r.next) {
          case 0:
            return r.prev = 0, r.next = 3, (0, i.getMyStoryList)();
          case 3:
            n = r.sent, o = n.code, a = n.data, 200 === o && e.setData({
              recordList: a
            }), r.next = 12;
            break;
          case 9:
            r.prev = 9, r.t0 = r.catch(0), console.log("getMyStoryList:", r.t0);
          case 12:
          case "end":
            return r.stop()
        }
      }), t, null, [
        [0, 9]
      ])
    })))()
  },
  likeStory: function(e) {
    e.detail.flag && this.getMyStoryListFn()
  },
  onReady: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return a.shareOptions
  }
});