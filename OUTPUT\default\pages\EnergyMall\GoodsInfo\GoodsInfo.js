var t, a = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  o = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  e = (t = require("../../../87624F60549B04BFE10427674BE30D65.js")) && t.__esModule ? t : {
    default: t
  };
getApp();
Page({
  data: {
    id: "",
    goodsInfoMsg: {},
    giftType: null,
    userInfo: {},
    propState: !1,
    propNum: null
  },
  closeProp: function() {
    this.setData({
      propState: !1
    })
  },
  getDetail: function() {
    var t = this;
    (0, o.loadingOpen)(), (0, a.getGiftDetail)({
      id: this.data.id
    }).then((function(a) {
      (0, o.loadingClose)(), 200 == a.code ? (a.data.details = a.data.details ? a.data.details.replace(/\<img/gi, '<img style="width:100%;height:auto ;display:block;"') : "", a.data.details = a.data.details ? a.data.details.replace("px", "rpx") : "", t.setData({
        goodsInfoMsg: a.data,
        giftType: a.data.giftType
      }), 1 == t.data.giftType && wx.setNavigationBarTitle({
        title: "虚拟商品详情"
      }), 3 == t.data.giftType && wx.setNavigationBarTitle({
        title: "实物商品详情"
      })) : (0, o.toastModel)(a.message)
    }))
  },
  gotoSubmit: function() {
    console.log("用户信息", this.data.userInfo), this.data.userInfo.mobile ? this.data.userInfo.currentJf < this.data.goodsInfoMsg.giftExchangeScore ? (0, o.toastModel)("可用能量不足") : this.data.goodsInfoMsg.giftTotalCount - this.data.goodsInfoMsg.giftUseCount <= 0 ? (0, o.toastModel)("商品库存不足") : (e.default.data.goodsInfoId = this.data.goodsInfoMsg.id, wx.navigateTo({
      url: "/pages/EnergyMall/SubmitOrder/SubmitOrder?id=" + e.default.data.goodsInfoId
    })) : this.setData({
      propState: !0,
      propNum: 6
    })
  },
  onLoad: function(t) {
    this.setData({
      id: t.id,
      userInfo: e.default.data.userInfo
    }), this.getDetail()
  },
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {}
});