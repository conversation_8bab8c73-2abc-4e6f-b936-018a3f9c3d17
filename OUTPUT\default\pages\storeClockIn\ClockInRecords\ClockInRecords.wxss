.ClockInRecords {
    box-sizing: border-box;
    min-height: 100vh;
    padding-bottom: 100rpx;
    width: 100%
}

.ClockInRecords_top {
    background: url("http://dm-assets.supercarrier8.com/wobei/newVersion/012.png");
    background-size: 100% 100%;
    display: -webkit-flex;
    display: flex;
    height: 76rpx
}

.top_item {
    height: 76rpx;
    line-height: 76rpx;
    margin-left: 20rpx;
    position: relative;
    width: 100rpx
}

.top_item_choose {
    color: #000;
    font-weight: 700
}

.top_item_choose,.top_item_noChoose {
    font-family: Source Han Sans CN;
    font-size: 28rpx;
    text-align: center
}

.top_item_noChoose {
    color: #3c3c3c;
    font-weight: 500
}

.top_item_xian {
    background: #000;
    border-radius: 4rpx;
    bottom: 0;
    height: 8rpx;
    left: 50%;
    position: absolute;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 56rpx
}

.ClockInRecords_title {
    background: #fff6de;
    color: #2b2b2b;
    display: -webkit-flex;
    display: flex;
    font-family: Source Han Sans CN;
    font-size: 24rpx;
    font-weight: 500;
    height: 64rpx;
    line-height: 64rpx;
    width: 750rpx
}

.ClockInRecords_title_box {
    display: -webkit-flex;
    display: flex;
    height: 64rpx;
    margin: 0 auto;
    width: 714rpx
}

.ClockInRecords_title_store,.ClockInRecords_title_time {
    height: 64rpx;
    text-align: center;
    width: 40%
}

.ClockInRecords_title_price {
    height: 64rpx;
    text-align: center;
    width: 20%
}

.ClockInRecords_title_item {
    border-bottom: 1rpx solid #dfdfdf;
    color: #000;
    display: -webkit-flex;
    display: flex;
    font-family: Source Han Sans CN;
    font-size: 24rpx;
    font-weight: 500;
    margin: 0 auto;
    min-height: 64rpx;
    width: 714rpx
}

.item_store,.item_time {
    width: 40%
}

.item_price,.item_store,.item_time {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    min-height: 64rpx;
    text-align: center
}

.item_price {
    width: 20%
}

.ClockInRecords_nolist {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 48rpx;
    font-weight: 500;
    height: 400rpx;
    line-height: 400rpx;
    text-align: center
}

.ClockInRecords_text {
    background: #fff;
    bottom: 0;
    box-sizing: border-box;
    color: silver;
    font-family: Arial;
    font-size: 18rpx;
    font-weight: 400;
    height: 80rpx;
    left: 0;
    padding-top: 20rpx;
    position: fixed;
    text-align: center;
    width: 100%
}
