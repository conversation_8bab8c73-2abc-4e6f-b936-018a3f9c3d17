<view class="container">
    <view class="container_box">
        <view class="container_box_items">
            <view class="container_box_item" wx:for="{{10}}" wx:key="index">{{item}}</view>
        </view>
    </view>
    <swiper autoplay="{{autoplay}}" class="swiper" duration="{{duration}}" indicatorDots="{{indicatorDots}}" interval="{{interval}}">
        <swiper-item class="swiperItem" wx:for="{{background}}" wx:key="*this">
            <view class="swiper-item {{item}}">{{item}}</view>
        </swiper-item>
    </swiper>
    <div class="ul">
        <div class="li">第一个哈哈</div>
        <div class="li">第一个哈哈</div>
        <div class="li">第一个哈哈</div>
        <div class="li">第一个哈哈</div>
        <div class="li">第一个哈哈</div>
        <div class="li">第一个哈哈</div>
        <div class="li">第一个哈哈</div>
        <div class="li">第一个哈哈</div>
        <div class="li">第一个哈哈</div>
        <div class="li">第一个哈哈</div>
        <div class="li">第一个哈哈</div>
        <div class="li">第一个哈哈</div>
    </div>
</view>
