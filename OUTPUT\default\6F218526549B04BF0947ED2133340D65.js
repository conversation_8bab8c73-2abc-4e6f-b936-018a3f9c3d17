Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.uploadImg = exports.uploadFile = exports.toastModel = exports.showModel = exports.setPageTitle = exports.scanCode = exports.loadingOpen = exports.loadingClose = exports.getUserPhone = exports.getUserInfo = exports.getUserGPS = exports.getCode = exports.callPhone = void 0;
var e, t = require("./@babel/runtime/helpers/regeneratorRuntime.js"),
  n = require("./@babel/runtime/helpers/asyncToGenerator.js"),
  o = (require("A4000F75549B04BFC2666772D6B30D65.js"), (e = require("B6135D02549B04BFD0753505DD930D65.js")) && e.__esModule ? e : {
    default: e
  });
exports.loadingOpen = function() {
  wx.showLoading({
    mask: !0
  })
};
exports.loadingClose = function() {
  wx.hideLoading()
};
var r = function(e) {
  wx.showToast({
    icon: "none",
    title: e,
    mask: !0
  })
};
exports.toastModel = r;
var s = function() {
  var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "",
    t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "",
    n = !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2];
  return new Promise((function(o, r) {
    wx.showModal({
      title: e,
      content: t,
      showCancel: n,
      success: function(e) {
        e.confirm ? o(!0) : e.cancel && o(!1)
      }
    })
  }))
};
exports.showModel = s;
exports.getCode = function() {
  return new Promise((function(e, t) {
    wx.login({
      success: function(t) {
        e(t.code)
      }
    })
  }))
};
exports.scanCode = function() {
  return new Promise((function(e, t) {
    wx.scanCode({
      success: function(t) {
        e(t)
      }
    })
  }))
};
exports.uploadImg = function() {
  var e = arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
  return new Promise((function(o, s) {
    var a;
    wx.chooseMedia({
      count: 1,
      mediaType: ["image"],
      sourceType: e ? ["album", "camera"] : ["camera"],
      camera: "back",
      success: (a = n(t().mark((function e(n) {
        var s;
        return t().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              if (console.log("图片信息", n), !(n.tempFiles[0].size > 20971520)) {
                e.next = 4;
                break
              }
              return r("上传图片不得大于20M"), e.abrupt("return");
            case 4:
              return e.next = 6, i(n.tempFiles[0].tempFilePath);
            case 6:
              s = e.sent, o(s);
            case 8:
            case "end":
              return e.stop()
          }
        }), e)
      }))), function(e) {
        return a.apply(this, arguments)
      })
    })
  }))
};
var i = function(e) {
  return new Promise((function(t, n) {
    wx.uploadFile({
      url: o.default.ApiURL + "/cncop/hive-oss/public/v1/oss/upload-get-url",
      filePath: e,
      name: "file",
      formData: {
        folder: "sup8-mp-module-pepsi"
      },
      success: function(e) {
        t(JSON.parse(e.data).data)
      }
    })
  }))
};
exports.uploadFile = i;
var a = function() {
  var e = n(t().mark((function e() {
    return t().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.next = 2, s("提示", "是否允许“xxx”获取您的微信信息？");
        case 2:
          if (e.sent) {
            e.next = 5;
            break
          }
          return e.abrupt("return");
        case 5:
          return e.abrupt("return", new Promise((function(e, t) {
            wx.getUserProfile({
              lang: "zh_CN",
              desc: "用于完善会员资料",
              success: function(t) {
                e(t)
              }
            })
          })));
        case 6:
        case "end":
          return e.stop()
      }
    }), e)
  })));
  return function() {
    return e.apply(this, arguments)
  }
}();
exports.getUserInfo = a;
exports.getUserPhone = function(e) {
  return new Promise((function(t, n) {
    "getPhoneNumber:ok" == e.detail.errMsg ? t({
      encryptedData: e.detail.encryptedData,
      iv: e.detail.iv,
      session_key: ""
    }) : s("", "为了更好的提供服务 本次活动需要验证您的手机号 请点击允许")
  }))
};
var c = function(e) {
    var o;
    wx.getSetting({
      success: (o = n(t().mark((function n(o) {
        var r;
        return t().wrap((function(t) {
          for (;;) switch (t.prev = t.next) {
            case 0:
              if (o.authSetting["scope.userLocation"] || void 0 === o.authSetting["scope.userLocation"]) {
                t.next = 8;
                break
              }
              return t.next = 3, s("提示", e, !0);
            case 3:
              r = t.sent, console.log("isTrue", r), r ? wx.openSetting({
                success: function() {
                  u()
                }
              }) : wx.navigateBack(), t.next = 9;
              break;
            case 8:
              u();
            case 9:
            case "end":
              return t.stop()
          }
        }), n)
      }))), function(e) {
        return o.apply(this, arguments)
      })
    })
  },
  u = function() {
    var e = !(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0],
      t = arguments.length > 1 ? arguments[1] : void 0;
    return new Promise((function(n, o) {
      wx.getSystemInfo({
        success: function(o) {
          console.log("用户手机是否打开GPS授权", o.locationEnabled), o.locationEnabled ? wx.getLocation({
            type: "gcj02",
            altitude: !0,
            success: function(e) {
              n(e)
            },
            fail: function(o) {
              e ? c(t) : n(o)
            }
          }) : s("提示", "您手机定位功能没有开启，请在系统设置中打开定位服务")
        }
      })
    }))
  };
exports.getUserGPS = u;
exports.callPhone = function(e) {
  wx.makePhoneCall({
    phoneNumber: e
  }).catch((function(e) {
    console.log("取消拨打", e)
  }))
};
exports.setPageTitle = function(e) {
  wx.setNavigationBarTitle({
    title: e
  })
};