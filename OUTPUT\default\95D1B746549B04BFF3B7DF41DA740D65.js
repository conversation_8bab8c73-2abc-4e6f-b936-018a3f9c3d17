var e = require("./@babel/runtime/helpers/regeneratorRuntime.js"),
  n = require("./@babel/runtime/helpers/asyncToGenerator.js"),
  o = require("6D59C885549B04BF0B3FA082E5940D65.js"),
  t = function() {
    var o = n(e().mark((function n() {
      var o;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.prev = 0, e.next = 3, new Promise((function(e, n) {
              wx.login({
                success: function(o) {
                  o.code ? e(o.code) : n(new Error("登录失败！" + o.errMsg))
                },
                fail: function(e) {
                  n(new Error("wx.login失败：" + e.errMsg))
                }
              })
            }));
          case 3:
            return o = e.sent, e.abrupt("return", o);
          case 7:
            return e.prev = 7, e.t0 = e.catch(0), console.log("getJsCode error: ", e.t0), e.abrupt("return", null);
          case 11:
          case "end":
            return e.stop()
        }
      }), n, null, [
        [0, 7]
      ])
    })));
    return function() {
      return o.apply(this, arguments)
    }
  }();
module.exports = {
  getJsCode: t,
  getAccessToken: function() {
    return wx.getStorageSync("access_token") || ""
  },
  setAccessToken: function(e) {
    return wx.setStorageSync("access_token", e)
  },
  getStorageSync: function(e) {
    return JSON.parse(wx.getStorageSync(e))
  },
  setStorageSync: function(e, n) {
    wx.setStorageSync(e, JSON.stringify(n))
  },
  isTokenExpired: function() {
    var e = wx.getStorageSync("access_token") ? wx.getStorageSync("token_expire") : null,
      n = Date.now();
    return !e || n - e > 72e5
  },
  getScreenInfo: function() {
    var e = wx.getSystemInfoSync(),
      n = e.windowWidth / e.windowHeight;
    return console.log("screenProp: ", n), n < .55 ? "screen189" : n > .5 ? "screen159" : n > .48 && n <= .5 ? "screen169" : ""
  },
  preloadFonts: function() {
    wx.loadFontFace({
      global: !0,
      family: "AaHouDiHei",
      source: "url(".concat(o.fontsUrl, "2025/snoppy/assets/fonts/AaHouDiHei-2.ttf)")
    }), wx.loadFontFace({
      global: !0,
      family: "SourceHanSansCN-Normal",
      source: "url(".concat(o.fontsUrl, "2025/snoppy/assets/fonts/SourceHanSansCN-Normal.otf)")
    }), wx.loadFontFace({
      global: !0,
      family: "SourceHanSansCN-Medium",
      source: "url(".concat(o.fontsUrl, "2025/snoppy/assets/fonts/SourceHanSansCN-Medium.otf)")
    }), wx.loadFontFace({
      global: !0,
      family: "SourceHanSerifCN-Bold",
      source: "url(".concat(o.fontsUrl, "2025/snoppy/assets/fonts/SourceHanSansCN-Bold.otf)")
    }), wx.loadFontFace({
      global: !0,
      family: "SourceHanSerifCN-Light",
      source: "url(".concat(o.fontsUrl, "2025/snoppy/assets/fonts/SourceHanSerifCN-Light.otf)")
    }), wx.loadFontFace({
      global: !0,
      family: "SourceHanSerifCN-Regular",
      source: "url(".concat(o.fontsUrl, "2025/snoppy/assets/fonts/SourceHanSansCN-Regular.otf)")
    }), wx.loadFontFace({
      global: !0,
      family: "LogoSCUnboundedSans-Regular",
      source: "url(".concat(o.fontsUrl, "2025/snoppy/assets/fonts/LogoSCUnboundedSans-Regular-2.ttf)")
    })
  },
  checkVersionUpdate: function() {
    if (wx.canIUse("getUpdateManager")) {
      var e = wx.getUpdateManager();
      e.onCheckForUpdate((function(n) {
        n.hasUpdate && (e.onUpdateReady((function() {
          wx.showModal({
            title: "更新提示",
            content: "新版本已经准备好，是否重启应用？",
            success: function(n) {
              n.confirm && e.applyUpdate()
            }
          })
        })), e.onUpdateFailed((function() {
          wx.showModal({
            title: "已经有新版本了哟~",
            content: "新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~"
          })
        })))
      }))
    } else wx.showModal({
      title: "提示",
      content: "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。"
    })
  },
  setMonitor: function(e) {
    return wx.setStorageSync("monitor", JSON.stringify(e))
  },
  getMonitor: function() {
    return JSON.parse(wx.getStorageSync("monitor") || "")
  },
  autoSize: function(e, n) {
    var o = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0,
      t = new Array,
      r = e[0] / e[1];
    return t[0] = n[0], t[1] = Math.round(t[0] / r), o ? t[1] < n[1] && (t[1] = n[1], t[0] = Math.round(t[1] * r)) : t[1] > n[1] && (t[1] = n[1], t[0] = Math.round(t[1] * r)), t
  }
};