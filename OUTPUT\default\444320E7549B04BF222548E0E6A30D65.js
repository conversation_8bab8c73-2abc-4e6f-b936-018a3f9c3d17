Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.request = void 0, require("./@babel/runtime/helpers/Arrayincludes.js");
var e = require("./@babel/runtime/helpers/objectSpread2.js"),
  t = o(require("B6135D02549B04BFD0753505DD930D65.js"));
require("F02D42C2549B04BF964B2AC524F30D65.js");
var a = o(require("1DF9C763549B04BF7B9FAF6415140D65.js")),
  r = o(require("87624F60549B04BFE10427674BE30D65.js")),
  d = require("6F218526549B04BF0947ED2133340D65.js");

function o(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
getApp();
exports.request = function(o) {
  var u = e({
    enterpriseNo: t.default.enterpriseNo
  }, o.data);
  return new Promise((function(s, n) {
    var i, l, c, h, f;
    "DELETE" == o.method ? wx.request({
      url: "".concat(t.default.ApiURL) + "/" + o.url + "?id=" + o.data.id,
      method: o.method,
      header: {
        "X-Request-Lang": "zh-CN",
        Authorization: r.default.data.token ? r.default.data.token : ""
      },
      success: function(e) {
        200 == e.data.code ? s(e.data) : [20324010, 20324011, 20324012, 20324013].includes(e.data.code) ? (r.default.data.token = "", (0, d.showModel)("", "登录失效，请重新登录", !1).then((function(e) {
          wx.reLaunch({
            url: "/pages/Home/Home"
          })
        }))) : (0, d.toastModel)(e.data.message.replace(/积分/g, "能量"))
      },
      fail: function(e) {
        n(e)
      }
    }) : wx.request({
      url: o.isUrl ? o.url : "".concat(t.default.ApiURL) + "/" + o.url,
      method: o.method || "post",
      data: o.enterpriseNo ? o.data : u,
      header: e(e({
        "X-Request-Lang": "zh-CN",
        Authorization: r.default.data.token ? r.default.data.token : ""
      }, (i = {
        method: o.method,
        url: o.url,
        params: o.enterpriseNo ? o.data : u,
        data: o.enterpriseNo ? o.data : u
      }, l = i.method, c = i.url, h = i.params, f = i.data, a.default.getSignByReq({
        method: l,
        url: "/".concat(c),
        params: h,
        data: f,
        headers: {}
      }).headers)), o.headers),
      success: function(e) {
        200 == e.data.code ? s(e.data) : [20324010, 20324011, 20324012, 20324013].includes(e.data.code) ? (r.default.data.token = "", (0, d.showModel)("", "登录失效，请重新登录", !1).then((function(e) {
          wx.reLaunch({
            url: "/pages/Home/Home"
          })
        }))) : o.separateMsg ? s(e.data) : (0, d.toastModel)(e.data.message.replace(/积分/g, "能量"))
      },
      fail: function(e) {
        (0, d.loadingClose)(), (0, d.toastModel)("网络异常，请稍后再试")
      }
    })
  }))
};