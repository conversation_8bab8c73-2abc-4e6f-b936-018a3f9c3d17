<view class="GoodsInfo">
    <view class="GoodsInfo_top">
        <view class="GoodsInfo_top_img">
            <view class="GoodsInfo_top_img_i">
                <image mode="" src="{{goodsInfoMsg.giftImg}}"></image>
            </view>
            <view class="EnergyMall_list_item_radio">
                <view class="EnergyMall_list_item_radio_num">{{goodsInfoMsg.giftExchangeScore}}</view>
                <view class="EnergyMall_list_item_radio_text">申请能量</view>
            </view>
        </view>
        <view class="GoodsInfo_top_bot">
            <view class="GoodsInfo_top_bot_text">{{goodsInfoMsg.giftName}}</view>
        </view>
    </view>
    <view class="GoodsInfo_title">商品详情</view>
    <view class="GoodsInfo_imgs">
        <rich-text class="cnt" nodes="{{goodsInfoMsg.details}}"></rich-text>
    </view>
    <view class="GoodsInfo_imgsBot">
        <image mode="" src="https://dm-assets.supercarrier8.com/wobei/sws/infoImg.png"></image>
    </view>
    <view style="height:90rpx;background:#fff;"></view>
    <view class="GoodsInfo_bot">
        <view bindtap="gotoSubmit" class="GoodsInfo_button">立即兑换</view>
    </view>
    <prop bindcloseProp="closeProp" propNum="{{propNum}}" wx:if="{{propState}}"></prop>
</view>
