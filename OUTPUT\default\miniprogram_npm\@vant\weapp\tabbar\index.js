Object.defineProperty(exports, "__esModule", {
  value: !0
});
var e = require("../common/component"),
  t = require("../common/relation"),
  r = require("../common/utils");
(0, e.VantComponent)({
  relation: (0, t.useChildren)("tabbar-item", (function() {
    this.updateChildren()
  })),
  props: {
    active: {
      type: null,
      observer: "updateChildren"
    },
    activeColor: {
      type: String,
      observer: "updateChildren"
    },
    inactiveColor: {
      type: String,
      observer: "updateChildren"
    },
    fixed: {
      type: Boolean,
      value: !0,
      observer: "setHeight"
    },
    placeholder: {
      type: <PERSON><PERSON><PERSON>,
      observer: "setHeight"
    },
    border: {
      type: <PERSON><PERSON>an,
      value: !0
    },
    zIndex: {
      type: Number,
      value: 1
    },
    safeAreaInsetBottom: {
      type: <PERSON>olean,
      value: !0
    }
  },
  data: {
    height: 50
  },
  methods: {
    updateChildren: function() {
      var e = this.children;
      Array.isArray(e) && e.length && e.forEach((function(e) {
        return e.updateFromParent()
      }))
    },
    setHeight: function() {
      var e = this;
      this.data.fixed && this.data.placeholder && wx.nextTick((function() {
        (0, r.getRect)(e, ".van-tabbar").then((function(t) {
          e.setData({
            height: t.height
          })
        }))
      }))
    }
  }
});