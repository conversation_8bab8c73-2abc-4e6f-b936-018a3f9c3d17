var e = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  a = require("../../../A5622344549B04BFC3044B435F450D65.js");
Component({
  properties: {},
  data: {
    imgUrl: e.imgUrl,
    imgVersion: e.imgVersion,
    open: !1
  },
  methods: {
    openMask: function() {
      this.setData({
        open: !0
      }), a.pagePopupViews({
        page_type: "抽签页面",
        page_name: "抽签页面",
        popup_name: "很遗憾",
        page_path: "cny/pages/drawLots/index"
      })
    },
    closeMask: function() {
      this.setData({
        open: !1
      })
    },
    handleRegret: function() {
      this.closeMask(), wx.navigateTo({
        url: "/cny/pages/task/index"
      }), a.pagePopupClickEvents({
        page_type: "抽签页面",
        page_name: "抽签页面",
        popup_name: "很抱歉",
        page_path: "cny/pages/drawLots/index",
        button_name: "确定"
      })
    }
  }
});