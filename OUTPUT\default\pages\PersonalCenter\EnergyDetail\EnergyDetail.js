var t, e = require("../../../9F0F7777549B04BFF9691F70EED30D65.js"),
  a = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  n = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  i = (t = require("../../../87624F60549B04BFE10427674BE30D65.js")) && t.__esModule ? t : {
    default: t
  },
  s = require("../../../8F86AFB2549B04BFE9E0C7B593D30D65.js");
var o = getApp();
Page({
  data: {
    img: o.globalData.img,
    currentDate: (new Date).getTime(),
    minDate: new Date("2023-01").getTime(),
    maxDate: (new Date).getTime(),
    tabList: [{
      name: "全部",
      state: ""
    }, {
      name: "已获取",
      state: 1
    }, {
      name: "已消耗",
      state: 2
    }],
    activeState: "",
    list: [],
    show: !1,
    dateTime: "",
    pageIndex: 1,
    pageSize: 10,
    totalPage: 0,
    userInfo: null,
    startDate: "",
    endDate: "",
    ruleText: ""
  },
  closeProp: function() {
    this.setData({
      propState: !1
    })
  },
  getList: function() {
    var t = this;
    (0, a.loadingOpen)(), (0, n.JfRecordPage)({
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      UserID: i.default.data.userInfo.id,
      recordType: this.data.activeState,
      startDate: this.data.startDate,
      endDate: this.data.endDate
    }).then((function(e) {
      (0, a.loadingClose)(), e.data.list.forEach((function(t) {
        t.recordTypeNameNew = t.recordTypeName.replace("积分", "能量")
      })), t.setData({
        pageIndex: t.data.pageIndex += 1,
        list: t.data.list.concat(e.data.list),
        totalPage: Math.ceil(e.data.total / t.data.pageSize)
      })
    }))
  },
  showRule: function() {
    this.setData({
      propState: !0,
      propNum: 10
    })
  },
  changeTab: function(t) {
    var e = t.currentTarget.dataset.item;
    console.log(e), this.setData({
      activeState: e.state
    }), this.resettingList()
  },
  resettingList: function() {
    this.setData({
      pageIndex: 1,
      list: []
    }), this.getList()
  },
  onInput: function(t) {
    this.setData({
      currentDate: t.detail
    }), console.log("currentDate", this.data.currentDate)
  },
  showTime: function() {
    this.setData({
      show: !0
    })
  },
  onClickHide: function() {
    this.setData({
      show: !1
    })
  },
  suerDate: function() {
    this.setData({
      dateTime: (0, e.transformTime)(this.data.currentDate)
    }), this.getMonthStartEnd(this.data.dateTime), this.resettingList()
  },
  getMonthStartEnd: function(t) {
    var e = t.slice(0, 4),
      a = t.slice(5, t.length - 1);
    console.log("nyYear", e), console.log("nyMonth", a);
    var n = new Date(e, a - 1),
      i = new Date(new Date(e, a).valueOf() - 864e5);

    function s(t) {
      return t.getFullYear() + "-" + (t.getMonth() + 1) + "-" + t.getDate()
    }
    this.setData({
      startDate: s(n),
      endDate: s(i)
    })
  },
  onLoad: function(t) {
    this.setData({
      dateTime: (0, e.transformTime)(this.data.currentDate)
    }), this.getMonthStartEnd(this.data.dateTime)
  },
  getRule: function() {
    this.setData({
      ruleText: '\n            <p>1、能量值自获取之日起有效期为1年。</p>\n            <p style="padding-left:18px">如：2023年8月1日获取的能量值将在2024年7月31日当天失效。</p>\n            <p>2、能量值有效期届满后，系统自动清除，不接受增补。</p>'
    })
  },
  onReady: function() {},
  onShow: function() {
    this.setData({
      userInfo: i.default.data.userInfo
    }), console.log("userInfo", this.data.userInfo), this.resettingList(), this.getRule(), 1 == (0, s.get)("wb_mxProp") && ((0, s.set)("wb_mxPropState", 1), this.showRule())
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {
    this.data.pageIndex <= this.data.totalPage ? this.getList() : (0, a.toastModel)("暂无更多数据了~")
  }
});