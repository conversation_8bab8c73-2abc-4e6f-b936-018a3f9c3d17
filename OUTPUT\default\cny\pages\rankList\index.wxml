<view class="container fadeIn" hoverClass="none" hoverStopPropagation="false">
    <custom-header :isBack="{{true}}" title="PEANUTS"></custom-header>
    <view class="scroll-view {{screenInfo}}" hoverClass="none" hoverStopPropagation="false" style="top:{{statusHeaderBarHeight}}px">
        <image binderror="" bindload="" class="bg" lazyLoad="true" src="{{imgUrl}}75/record/bg.jpg?v={{imgVersion}}"></image>
        <view class="menuBox">
            <image binderror="" bindload="" class="menuImg" lazyLoad="true" src="{{imgUrl}}75/rank/{{isMenu==='hots'?'hots':'news'}}.png?v={{imgVersion}}"></image>
            <view catch:tap="handleSelectMenu" class="menuBtn menuBtn1" data-ismenu="hots"></view>
            <view catch:tap="handleSelectMenu" class="menuBtn menuBtn2" data-ismenu="news"></view>
        </view>
        <view class="contentBox">
            <c-75-upload-list bind:bindscrolltolower="bindscrolltolower" dataList="{{rankList}}" id="c-75-upload-list" type="rank"></c-75-upload-list>
        </view>
        <image binderror="" bindload="" catch:tap="handleGoCollection" class="submit" lazyLoad="true" src="{{imgUrl}}75/rank/submit.png?v={{imgVersion}}"></image>
    </view>
</view>
