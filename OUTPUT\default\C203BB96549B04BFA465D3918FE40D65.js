Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.penCache = exports.default = exports.clearPenCache = void 0;
var t = require("./@babel/runtime/helpers/toConsumableArray.js"),
  e = require("./@babel/runtime/helpers/createForOfIteratorHelper.js"),
  s = require("./@babel/runtime/helpers/classCallCheck.js"),
  i = require("./@babel/runtime/helpers/createClass.js"),
  o = require("1DCFDDB6549B04BF7BA9B5B13DF40D65.js"),
  r = require("41547380549B04BF27321B87E3E40D65.js");
require("DD091151549B04BFBB6F795656150D65.js");
var c = {
  viewRect: {},
  textLines: {}
};
exports.penCache = c;
exports.clearPenCache = function(t) {
  t ? (c.viewRect[t] = null, c.textLines[t] = null) : (c.viewRect = {}, c.textLines = {})
};
var h = function() {
  function h(t, e) {
    s(this, h), this.ctx = t, this.data = e
  }
  return i(h, [{
    key: "paint",
    value: function(t) {
      this.style = {
        width: this.data.width.toPx(),
        height: this.data.height.toPx()
      }, this._background();
      var s, i = e(this.data.views);
      try {
        for (i.s(); !(s = i.n()).done;) {
          var o = s.value;
          this._drawAbsolute(o)
        }
      } catch (t) {
        i.e(t)
      } finally {
        i.f()
      }
      this.ctx.draw(!1, (function() {
        t && t()
      }))
    }
  }, {
    key: "_background",
    value: function() {
      this.ctx.save();
      var t = this.style,
        e = t.width,
        s = t.height,
        i = this.data.background;
      this.ctx.translate(e / 2, s / 2), this._doClip(this.data.borderRadius, e, s), i ? i.startsWith("#") || i.startsWith("rgba") || "transparent" === i.toLowerCase() ? (this.ctx.fillStyle = i, this.ctx.fillRect(-e / 2, -s / 2, e, s)) : r.api.isGradient(i) ? (r.api.doGradient(i, e, s, this.ctx), this.ctx.fillRect(-e / 2, -s / 2, e, s)) : this.ctx.drawImage(i, -e / 2, -s / 2, e, s) : (this.ctx.fillStyle = "transparent", this.ctx.fillRect(-e / 2, -s / 2, e, s)), this.ctx.restore()
    }
  }, {
    key: "_drawAbsolute",
    value: function(e) {
      if (e && e.type) switch (e.css && e.css.length && (e.css = Object.assign.apply(Object, t(e.css))), e.type) {
        case "image":
          this._drawAbsImage(e);
          break;
        case "text":
          this._fillAbsText(e);
          break;
        case "inlineText":
          this._fillAbsInlineText(e);
          break;
        case "rect":
          this._drawAbsRect(e);
          break;
        case "qrcode":
          this._drawQRCode(e)
      }
    }
  }, {
    key: "_border",
    value: function(t) {
      var e = t.borderRadius,
        s = void 0 === e ? 0 : e,
        i = t.width,
        o = t.height,
        r = t.borderWidth,
        c = void 0 === r ? 0 : r,
        h = t.borderStyle,
        a = void 0 === h ? "solid" : h,
        l = 0,
        n = 0,
        x = 0,
        d = 0,
        f = Math.min(i, o);
      if (s) {
        var g = s.split(/\s+/);
        4 === g.length ? (l = Math.min(g[0].toPx(!1, f), i / 2, o / 2), n = Math.min(g[1].toPx(!1, f), i / 2, o / 2), x = Math.min(g[2].toPx(!1, f), i / 2, o / 2), d = Math.min(g[3].toPx(!1, f), i / 2, o / 2)) : l = n = x = d = Math.min(s && s.toPx(!1, f), i / 2, o / 2)
      }
      var u = c && c.toPx(!1, f);
      this.ctx.lineWidth = u, "dashed" === a ? this.ctx.setLineDash([4 * u / 3, 4 * u / 3]) : "dotted" === a && this.ctx.setLineDash([u, u]);
      var v = "solid" !== a;
      this.ctx.beginPath(), v && 0 === l && this.ctx.moveTo(-i / 2 - u, -o / 2 - u / 2), 0 !== l && this.ctx.arc(-i / 2 + l, -o / 2 + l, l + u / 2, 1 * Math.PI, 1.5 * Math.PI), this.ctx.lineTo(0 === n ? v ? i / 2 : i / 2 + u / 2 : i / 2 - n, -o / 2 - u / 2), v && 0 === n && this.ctx.moveTo(i / 2 + u / 2, -o / 2 - u), 0 !== n && this.ctx.arc(i / 2 - n, -o / 2 + n, n + u / 2, 1.5 * Math.PI, 2 * Math.PI), this.ctx.lineTo(i / 2 + u / 2, 0 === x ? v ? o / 2 : o / 2 + u / 2 : o / 2 - x), v && 0 === x && this.ctx.moveTo(i / 2 + u, o / 2 + u / 2), 0 !== x && this.ctx.arc(i / 2 - x, o / 2 - x, x + u / 2, 0, .5 * Math.PI), this.ctx.lineTo(0 === d ? v ? -i / 2 : -i / 2 - u / 2 : -i / 2 + d, o / 2 + u / 2), v && 0 === d && this.ctx.moveTo(-i / 2 - u / 2, o / 2 + u), 0 !== d && this.ctx.arc(-i / 2 + d, o / 2 - d, d + u / 2, .5 * Math.PI, 1 * Math.PI), this.ctx.lineTo(-i / 2 - u / 2, 0 === l ? v ? -o / 2 : -o / 2 - u / 2 : -o / 2 + l), v && 0 === l && this.ctx.moveTo(-i / 2 - u, -o / 2 - u / 2), v || this.ctx.closePath()
    }
  }, {
    key: "_doClip",
    value: function(t, e, s, i) {
      t && e && s && (this.ctx.globalAlpha = 0, this.ctx.fillStyle = "white", this._border({
        borderRadius: t,
        width: e,
        height: s,
        borderStyle: i
      }), this.ctx.fill(), getApp().systemInfo && getApp().systemInfo.version <= "6.6.6" && "ios" === getApp().systemInfo.platform || this.ctx.clip(), this.ctx.globalAlpha = 1)
    }
  }, {
    key: "_doBorder",
    value: function(t, e, s) {
      if (t.css) {
        var i = t.css,
          o = i.borderRadius,
          r = i.borderWidth,
          c = i.borderColor,
          h = i.borderStyle;
        r && (this.ctx.save(), this._preProcess(t, !0), this.ctx.strokeStyle = c || "black", this._border({
          borderRadius: o,
          width: e,
          height: s,
          borderWidth: r,
          borderStyle: h
        }), this.ctx.stroke(), this.ctx.restore())
      }
    }
  }, {
    key: "_preProcess",
    value: function(t, e) {
      var s, i, o, r, h = 0,
        a = this._doPaddings(t);
      switch (t.type) {
        case "inlineText":
          for (var l = 0, n = 0, x = 0, d = t.textList || [], f = 0; f < d.length; f++) {
            var g = d[f],
              u = g.css.fontWeight || "400",
              v = g.css.textStyle || "normal";
            g.css.fontSize || (g.css.fontSize = "20rpx"), this.ctx.font = "".concat(v, " ").concat(u, " ").concat(g.css.fontSize.toPx(), 'px "').concat(g.css.fontFamily || "sans-serif", '"'), n += this.ctx.measureText(g.text).width;
            var b = g.css.lineHeight ? g.css.lineHeight.toPx() : g.css.fontSize.toPx();
            x = Math.max(x, b)
          }
          h = t.css.width ? t.css.width.toPx(!1, this.style.width) - a[1] - a[3] : n, s = x * (l += Math.ceil(n / h)), i = {
            lines: l,
            lineHeight: x
          };
          break;
        case "text":
          for (var p = String(t.text).split("\n"), y = 0; y < p.length; ++y) "" === p[y] && (p[y] = " ");
          var w = t.css.fontWeight || "400",
            P = t.css.textStyle || "normal";
          t.css.fontSize || (t.css.fontSize = "20rpx"), this.ctx.font = "".concat(P, " ").concat(w, " ").concat(t.css.fontSize.toPx(), 'px "').concat(t.css.fontFamily || "sans-serif", '"');
          for (var m = 0, k = [], T = 0; T < p.length; ++T) {
            var S = this.ctx.measureText(p[T]).width,
              _ = t.css.fontSize.toPx() + a[1] + a[3],
              A = t.css.width ? t.css.width.toPx(!1, this.style.width) - a[1] - a[3] : S;
            A < _ && (A = _);
            var R = Math.ceil(S / A);
            h = A > h ? A : h, m += R, k[T] = R
          }
          m = t.css.maxLines < m ? t.css.maxLines : m;
          var W = t.css.lineHeight ? t.css.lineHeight.toPx() : t.css.fontSize.toPx();
          s = W * m, i = {
            lines: m,
            lineHeight: W,
            textArray: p,
            linesArray: k
          };
          break;
        case "image":
          var I = getApp().systemInfo.pixelRatio ? getApp().systemInfo.pixelRatio : 2;
          t.css && (t.css.width || (t.css.width = "auto"), t.css.height || (t.css.height = "auto")), !t.css || "auto" === t.css.width && "auto" === t.css.height ? (h = Math.round(t.sWidth / I), s = Math.round(t.sHeight / I)) : "auto" === t.css.width ? (s = t.css.height.toPx(!1, this.style.height), h = t.sWidth / t.sHeight * s) : "auto" === t.css.height ? (h = t.css.width.toPx(!1, this.style.width), s = t.sHeight / t.sWidth * h) : (h = t.css.width.toPx(!1, this.style.width), s = t.css.height.toPx(!1, this.style.height));
          break;
        default:
          if (!t.css.width || !t.css.height) return void console.error("You should set width and height");
          h = t.css.width.toPx(!1, this.style.width), s = t.css.height.toPx(!1, this.style.height)
      }
      if (t.css && t.css.right)
        if ("string" == typeof t.css.right) o = this.style.width - t.css.right.toPx(!0, this.style.width);
        else {
          var M = t.css.right;
          o = this.style.width - M[0].toPx(!0, this.style.width) - c.viewRect[M[1]].width * (M[2] || 1)
        }
      else if (t.css && t.css.left)
        if ("string" == typeof t.css.left) o = t.css.left.toPx(!0, this.style.width);
        else {
          var z = t.css.left;
          o = z[0].toPx(!0, this.style.width) + c.viewRect[z[1]].width * (z[2] || 1)
        }
      else o = 0;
      if (t.css && t.css.bottom) r = this.style.height - s - t.css.bottom.toPx(!0, this.style.height);
      else if (t.css && t.css.top)
        if ("string" == typeof t.css.top) r = t.css.top.toPx(!0, this.style.height);
        else {
          var C = t.css.top;
          r = C[0].toPx(!0, this.style.height) + c.viewRect[C[1]].height * (C[2] || 1)
        }
      else r = 0;
      var H = t.css && t.css.rotate ? this._getAngle(t.css.rotate) : 0,
        L = t.css && t.css.align ? t.css.align : t.css && t.css.right ? "right" : "left",
        D = t.css && t.css.verticalAlign ? t.css.verticalAlign : "top",
        B = 0;
      switch (L) {
        case "center":
          B = o;
          break;
        case "right":
          B = o - h / 2;
          break;
        default:
          B = o + h / 2
      }
      var q = 0;
      switch (D) {
        case "center":
          q = r;
          break;
        case "bottom":
          q = r - s / 2;
          break;
        default:
          q = r + s / 2
      }
      this.ctx.translate(B, q);
      var j = o;
      "center" === L ? j = o - h / 2 : "right" === L && (j = o - h);
      var G = r;
      return "center" === D ? G = r - s / 2 : "bottom" === D && (G = r - s), t.rect ? (t.rect.left = j, t.rect.top = G, t.rect.right = j + h, t.rect.bottom = G + s, t.rect.x = t.css && t.css.right ? o - h : o, t.rect.y = r) : t.rect = {
        left: j,
        top: G,
        right: j + h,
        bottom: G + s,
        x: t.css && t.css.right ? o - h : o,
        y: r
      }, t.rect.left = t.rect.left - a[3], t.rect.top = t.rect.top - a[0], t.rect.right = t.rect.right + a[1], t.rect.bottom = t.rect.bottom + a[2], "text" === t.type && (t.rect.minWidth = t.css.fontSize.toPx() + a[1] + a[3]), this.ctx.rotate(H), !e && t.css && t.css.borderRadius && "rect" !== t.type && this._doClip(t.css.borderRadius, h, s, t.css.borderStyle), this._doShadow(t), t.id && (c.viewRect[t.id] = {
        width: h,
        height: s,
        left: t.rect.left,
        top: t.rect.top,
        right: t.rect.right,
        bottom: t.rect.bottom
      }), {
        width: h,
        height: s,
        x: o,
        y: r,
        extra: i
      }
    }
  }, {
    key: "_doPaddings",
    value: function(t) {
      var e = (t.css ? t.css : {}).padding,
        s = [0, 0, 0, 0];
      if (e) {
        var i = e.split(/\s+/);
        if (1 === i.length) {
          var o = i[0].toPx();
          s = [o, o, o, o]
        }
        if (2 === i.length) {
          var r = i[0].toPx(),
            c = i[1].toPx();
          s = [r, c, r, c]
        }
        if (3 === i.length) {
          var h = i[0].toPx(),
            a = i[1].toPx();
          s = [h, a, i[2].toPx(), a]
        }
        if (4 === i.length) s = [i[0].toPx(), i[1].toPx(), i[2].toPx(), i[3].toPx()]
      }
      return s
    }
  }, {
    key: "_doBackground",
    value: function(t) {
      this.ctx.save();
      var e = this._preProcess(t, !0),
        s = e.width,
        i = e.height,
        o = t.css.background,
        c = this._doPaddings(t),
        h = s + c[1] + c[3],
        a = i + c[0] + c[2];
      this._doClip(t.css.borderRadius, h, a, t.css.borderStyle), r.api.isGradient(o) ? r.api.doGradient(o, h, a, this.ctx) : this.ctx.fillStyle = o, this.ctx.fillRect(-h / 2, -a / 2, h, a), this.ctx.restore()
    }
  }, {
    key: "_drawQRCode",
    value: function(t) {
      this.ctx.save();
      var e = this._preProcess(t),
        s = e.width,
        i = e.height;
      o.api.draw(t.content, this.ctx, -s / 2, -i / 2, s, i, t.css.background, t.css.color), this.ctx.restore(), this._doBorder(t, s, i)
    }
  }, {
    key: "_drawAbsImage",
    value: function(t) {
      if (t.url) {
        this.ctx.save();
        var e = this._preProcess(t),
          s = e.width,
          i = e.height,
          o = t.sWidth,
          r = t.sHeight,
          c = 0,
          h = 0,
          a = s / i;
        a >= t.sWidth / t.sHeight ? (r = o / a, h = Math.round((t.sHeight - r) / 2)) : (o = r * a, c = Math.round((t.sWidth - o) / 2)), t.css && "scaleToFill" === t.css.mode ? this.ctx.drawImage(t.url, -s / 2, -i / 2, s, i) : (this.ctx.drawImage(t.url, c, h, o, r, -s / 2, -i / 2, s, i), t.rect.startX = c / t.sWidth, t.rect.startY = h / t.sHeight, t.rect.endX = (c + o) / t.sWidth, t.rect.endY = (h + r) / t.sHeight), this.ctx.restore(), this._doBorder(t, s, i)
      }
    }
  }, {
    key: "_fillAbsInlineText",
    value: function(t) {
      if (t.textList) {
        t.css.background && this._doBackground(t), this.ctx.save();
        for (var e, s, i, o = this._preProcess(t, t.css.background && t.css.borderRadius), r = o.width, c = o.height, h = o.extra, a = (h.lines, h.lineHeight), l = -r / 2, n = 0, x = l, d = r, f = 0; f < t.textList.length; f++)
          for (var g = t.textList[f], u = 0, v = 0, b = g.text.length, p = this.ctx.measureText(g.text).width, y = Math.ceil(p / b); v < b;) {
            for (;
              (v - u + 1) * y < d && v < b;) v++;
            var w = g.text.substr(u, v - u),
              P = -c / 2 + g.css.fontSize.toPx() + n * a;
            this.ctx.font = (e = g.css, s = void 0, i = void 0, s = e.fontWeight || "400", i = e.textStyle || "normal", e.fontSize || (e.fontSize = "20rpx"), "".concat(i, " ").concat(s, " ").concat(e.fontSize.toPx(), 'px "').concat(e.fontFamily || "sans-serif", '"')), this.ctx.fillStyle = g.css.color || "black", this.ctx.textAlign = "left", "stroke" === g.css.textStyle ? this.ctx.strokeText(w, x, P) : this.ctx.fillText(w, x, P);
            var m = this.ctx.measureText(w).width,
              k = g.css.fontSize.toPx();
            g.css.textDecoration && (this.ctx.lineWidth = k / 13, this.ctx.beginPath(), /\bunderline\b/.test(g.css.textDecoration) && (this.ctx.moveTo(x, P), this.ctx.lineTo(x + m, P), {
              moveTo: [x, P],
              lineTo: [x + m, P]
            }), /\boverline\b/.test(g.css.textDecoration) && (this.ctx.moveTo(x, P - k), this.ctx.lineTo(x + m, P - k), {
              moveTo: [x, P - k],
              lineTo: [x + m, P - k]
            }), /\bline-through\b/.test(g.css.textDecoration) && (this.ctx.moveTo(x, P - k / 3), this.ctx.lineTo(x + m, P - k / 3), {
              moveTo: [x, P - k / 3],
              lineTo: [x + m, P - k / 3]
            }), this.ctx.closePath(), this.ctx.strokeStyle = g.css.color, this.ctx.stroke()), u = v, x += m, ((d -= m) <= 0 || d < y) && (d = r, x = l, n++)
          }
        this.ctx.restore(), this._doBorder(t, r, c)
      }
    }
  }, {
    key: "_fillAbsText",
    value: function(s) {
      if (s.text) {
        s.css.background && this._doBackground(s), this.ctx.save();
        var i = this._preProcess(s, s.css.background && s.css.borderRadius),
          o = i.width,
          r = i.height,
          h = i.extra;
        if (this.ctx.fillStyle = s.css.color || "black", s.id && c.textLines[s.id]) {
          this.ctx.textAlign = s.css.textAlign ? s.css.textAlign : "left";
          var a, l = e(c.textLines[s.id]);
          try {
            for (l.s(); !(a = l.n()).done;) {
              var n = a.value,
                x = n.measuredWith,
                d = n.text,
                f = n.x,
                g = n.y,
                u = n.textDecoration;
              if ("stroke" === s.css.textStyle ? this.ctx.strokeText(d, f, g, x) : this.ctx.fillText(d, f, g, x), u) {
                var v, b, p = s.css.fontSize.toPx();
                this.ctx.lineWidth = p / 13, this.ctx.beginPath(), (v = this.ctx).moveTo.apply(v, t(u.moveTo)), (b = this.ctx).lineTo.apply(b, t(u.lineTo)), this.ctx.closePath(), this.ctx.strokeStyle = s.css.color, this.ctx.stroke()
              }
            }
          } catch (t) {
            l.e(t)
          } finally {
            l.f()
          }
        } else {
          var y = h.lines,
            w = h.lineHeight,
            P = h.textArray,
            m = h.linesArray;
          if (s.id) {
            for (var k = 0, T = 0; T < P.length; ++T) {
              var S = this.ctx.measureText(P[T]).width;
              k = S > k ? S : k
            }
            c.viewRect[s.id].width = o ? k < o ? k : o : k
          }
          for (var _ = 0, A = 0; A < P.length; ++A)
            for (var R = Math.ceil(P[A].length / m[A]), W = 0, I = 0, M = 0; M < m[A] && !(_ >= y); ++M) {
              I = R;
              for (var z = P[A].substr(W, I), C = this.ctx.measureText(z).width; W + I <= P[A].length && (o - C > s.css.fontSize.toPx() || C - o > s.css.fontSize.toPx());) {
                if (C < o) z = P[A].substr(W, ++I);
                else {
                  if (z.length <= 1) break;
                  z = P[A].substr(W, --I)
                }
                C = this.ctx.measureText(z).width
              }
              if (W += z.length, _ === y - 1 && (A < P.length - 1 || W < P[A].length)) {
                for (; this.ctx.measureText("".concat(z, "...")).width > o && !(z.length <= 1);) z = z.substring(0, z.length - 1);
                z += "...", C = this.ctx.measureText(z).width
              }
              this.ctx.textAlign = s.css.textAlign ? s.css.textAlign : "left";
              var H = void 0,
                L = void 0;
              switch (s.css.textAlign) {
                case "center":
                  L = (H = 0) - C / 2;
                  break;
                case "right":
                  L = (H = o / 2) - C;
                  break;
                default:
                  L = H = -o / 2
              }
              var D = -r / 2 + (0 === _ ? s.css.fontSize.toPx() : s.css.fontSize.toPx() + _ * w);
              _++, "stroke" === s.css.textStyle ? this.ctx.strokeText(z, H, D, C) : this.ctx.fillText(z, H, D, C);
              var B = s.css.fontSize.toPx(),
                q = void 0;
              s.css.textDecoration && (this.ctx.lineWidth = B / 13, this.ctx.beginPath(), /\bunderline\b/.test(s.css.textDecoration) && (this.ctx.moveTo(L, D), this.ctx.lineTo(L + C, D), q = {
                moveTo: [L, D],
                lineTo: [L + C, D]
              }), /\boverline\b/.test(s.css.textDecoration) && (this.ctx.moveTo(L, D - B), this.ctx.lineTo(L + C, D - B), q = {
                moveTo: [L, D - B],
                lineTo: [L + C, D - B]
              }), /\bline-through\b/.test(s.css.textDecoration) && (this.ctx.moveTo(L, D - B / 3), this.ctx.lineTo(L + C, D - B / 3), q = {
                moveTo: [L, D - B / 3],
                lineTo: [L + C, D - B / 3]
              }), this.ctx.closePath(), this.ctx.strokeStyle = s.css.color, this.ctx.stroke()), s.id && (c.textLines[s.id] ? c.textLines[s.id].push({
                text: z,
                x: H,
                y: D,
                measuredWith: C,
                textDecoration: q
              }) : c.textLines[s.id] = [{
                text: z,
                x: H,
                y: D,
                measuredWith: C,
                textDecoration: q
              }])
            }
        }
        this.ctx.restore(), this._doBorder(s, o, r)
      }
    }
  }, {
    key: "_drawAbsRect",
    value: function(t) {
      this.ctx.save();
      var e = this._preProcess(t),
        s = e.width,
        i = e.height;
      r.api.isGradient(t.css.color) ? r.api.doGradient(t.css.color, s, i, this.ctx) : this.ctx.fillStyle = t.css.color;
      var o = t.css,
        c = o.borderRadius,
        h = o.borderStyle,
        a = o.borderWidth;
      this._border({
        borderRadius: c,
        width: s,
        height: i,
        borderWidth: a,
        borderStyle: h
      }), this.ctx.fill(), this.ctx.restore(), this._doBorder(t, s, i)
    }
  }, {
    key: "_doShadow",
    value: function(t) {
      if (t.css && t.css.shadow) {
        var e = t.css.shadow.replace(/,\s+/g, ",").split(/\s+/);
        e.length > 4 ? console.error("shadow don't spread option") : (this.ctx.shadowOffsetX = parseInt(e[0], 10), this.ctx.shadowOffsetY = parseInt(e[1], 10), this.ctx.shadowBlur = parseInt(e[2], 10), this.ctx.shadowColor = e[3])
      }
    }
  }, {
    key: "_getAngle",
    value: function(t) {
      return Number(t) * Math.PI / 180
    }
  }]), h
}();
exports.default = h;