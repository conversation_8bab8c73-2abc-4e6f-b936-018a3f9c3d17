Object.defineProperty(exports, "__esModule", {
  value: !0
});
var e = require("../common/component"),
  t = require("../common/relation");
(0, e.VantComponent)({
  relation: (0, t.useChildren)("sidebar-item", (function() {
    this.setActive(this.data.activeKey)
  })),
  props: {
    activeKey: {
      type: Number,
      value: 0,
      observer: "setActive"
    }
  },
  beforeCreate: function() {
    this.currentActive = -1
  },
  methods: {
    setActive: function(e) {
      var t = this.children,
        r = this.currentActive;
      if (!t.length) return Promise.resolve();
      this.currentActive = e;
      var i = [];
      return r !== e && t[r] && i.push(t[r].setActive(!1)), t[e] && i.push(t[e].setActive(!0)), Promise.all(i)
    }
  }
});