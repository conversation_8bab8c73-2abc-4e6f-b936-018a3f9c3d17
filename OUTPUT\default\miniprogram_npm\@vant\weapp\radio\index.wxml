<view class="{{utils.bem( 'radio',[direction] )}} custom-class">
    <view bindtap="onClickLabel" class="{{utils.bem( 'radio__label',[ labelPosition,{disabled:disabled||parentDisabled} ] )}} label-class" wx:if="{{labelPosition==='left'}}">
        <slot></slot>
    </view>
    <view bindtap="onChange" class="van-radio__icon-wrap" style="font-size:{{utils.addUnit(iconSize)}}">
        <slot name="icon" wx:if="{{useIconSlot}}"></slot>
        <van-icon class="{{utils.bem( 'radio__icon',[ shape,{disabled:disabled||parentDisabled,checked:value===name} ] )}}" customClass="icon-class" customStyle="{{computed.iconCustomStyle( {iconSize:iconSize} )}}" name="success" style="{{computed.iconStyle( {iconSize:iconSize,checkedColor:checkedColor,disabled:disabled,parentDisabled:parentDisabled,value:value,name:name} )}}" wx:else></van-icon>
    </view>
    <view bindtap="onClickLabel" class="label-class {{utils.bem( 'radio__label',[ labelPosition,{disabled:disabled||parentDisabled} ] )}}" wx:if="{{labelPosition==='right'}}">
        <slot></slot>
    </view>
</view>

<wxs module="utils" src="..\wxs\utils.wxs"/>
<wxs module="computed" src="index.wxs"/>