<view class="container fadeIn" hoverClass="none" hoverStopPropagation="false">
    <custom-header :isBack="{{true}}" title="PEANUTS"></custom-header>
    <view class="scroll-view {{screenInfo}}" hoverClass="none" hoverStopPropagation="false" style="top:{{statusHeaderBarHeight}}px">
        <image binderror="" bindload="" class="bg" lazyLoad="true" src="{{imgUrl}}redPacket/bg.jpg?v={{imgVersion}}"></image>
        <image binderror="" bindload="" class="lantern" lazyLoad="true" src="{{imgUrl}}redPacket/lantern.png?v={{imgVersion}}"></image>
        <image binderror="" bindload="" class="star" lazyLoad="true" src="{{imgUrl}}redPacket/star.png?v={{imgVersion}}"></image>
        <view class="titleBox" hoverClass="none" hoverStopPropagation="false">
            <image binderror="" bindload="" class="title" lazyLoad="true" src="{{imgUrl}}redPacket/t1.png?v={{imgVersion}}" wx:if="{{RedCoverList.length==0}}"></image>
            <image binderror="" bindload="" class="title" lazyLoad="true" src="{{imgUrl}}redPacket/t2.png?v={{imgVersion}}" wx:if="{{RedCoverList.length>0&&RedCoverList.length<3}}"></image>
            <image binderror="" bindload="" class="title" lazyLoad="true" src="{{imgUrl}}redPacket/t3.png?v={{imgVersion}}" wx:if="{{RedCoverList.length==3}}"></image>
        </view>
        <view class="swiperBox" hoverClass="none" hoverStopPropagation="false">
            <swiper bindanimationfinish="" bindchange="handleChangeSwiper" class="sBox" current="{{redCurrentIndex}}" currentItemId="" displayMultipleItems="1" duration="1000" nextMargin="160rpx" previousMargin="160rpx" skipHiddenItemLayout="false">
                <swiper-item wx:for="{{redPacketList}}" wx:key="index">
                    <view class="redPacketBox fadeIn {{item.active?'redPacketBoxHover':''}}" hoverClass="none" hoverStopPropagation="false">
                        <view class="redBox" hoverClass="none" hoverStopPropagation="false">
                            <image binderror="" bindload="" class="redPacketImg" lazyLoad="false" src="{{item.img}}"></image>
                            <view catch:tap="handleGetRedPack" class="getRedPack" data-info="{{item}}"></view>
                            <image binderror="" bindload="" class="lock" lazyLoad="false" src="{{imgUrl}}redPacket/lock.png?v={{imgVersion}}" wx:if="{{item.isLock}}"></image>
                            <image binderror="" bindload="" class="end" lazyLoad="false" src="{{imgUrl}}redPacket/end.png?v={{imgVersion}}" wx:if="{{item.isEnd&&item.isLock}}"></image>
                        </view>
                    </view>
                </swiper-item>
            </swiper>
        </view>
        <image binderror="" bindload="" catch:tap="handleMyTask" class="s1" lazyLoad="true" src="{{imgUrl}}redPacket/s1.png?v={{imgVersion}}"></image>
        <image binderror="" bindload="" catch:tap="handleOpenCounponRecord" class="couponIcon" lazyLoad="true" src="{{imgUrl}}redPacket/couponIcon.png?v={{imgVersion}}"></image>
        <couponRecord id="couponRecord"></couponRecord>
    </view>
    <copyright></copyright>
</view>
