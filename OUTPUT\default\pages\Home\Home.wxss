.home {
    background: url("https://dm-assets.supercarrier8.com/wobei//newVersion/003.png");
    background-size: 100% 100%;
    min-height: 100vh;
    width: 100%
}

.home_fb {
    height: 106rpx;
    position: fixed;
    right: 10rpx;
    top: 830rpx;
    width: 106rpx
}

.storeIcon {
    bottom: 220rpx;
    height: 127rpx;
    position: fixed;
    right: 20rpx;
    width: 112rpx
}

.home_gg {
    background: #fff;
    height: 100vh;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 999999
}

.home_swiperBox {
    position: relative
}

.home_swiper {
    height: 360rpx;
    margin: 0 auto;
    overflow: hidden;
    width: 100%
}

.home_swiper_left {
    left: 14rpx
}

.home_swiper_left,.home_swiper_right {
    height: 42rpx;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 42rpx;
    z-index: 999
}

.home_swiper_right {
    right: 14rpx
}

.swiperItem {
    width: 100%
}

.swiper-item,.swiperItem {
    height: 360rpx;
    margin: 0 auto;
    overflow: hidden
}

.home_banner {
    overflow: hidden;
    width: 100%
}

.home_banner_title {
    height: 35rpx;
    margin: 48rpx auto;
    width: 145rpx
}

.home_banner_items::-webkit-scrollbar {
    display: none;
    height: 0;
    width: 0
}

.home_banner_items {
    box-sizing: border-box;
    margin-bottom: 56rpx;
    overflow-x: scroll;
    padding-left: 33rpx;
    white-space: nowrap
}

.home_banner_item {
    display: inline-block;
    margin-right: 42rpx
}

.home_banner_item_img {
    border-radius: 50%;
    height: 126rpx;
    overflow: hidden;
    width: 126rpx
}

.home_banner_item_text {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 20rpx;
    font-weight: 500;
    margin-top: 10rpx;
    text-align: center
}

.home_GZH {
    -webkit-align-items: center;
    align-items: center;
    background: #f5ca01;
    border-radius: 15rpx;
    margin: 14rpx auto 25rpx
}

.home_GZH,.home_newB {
    display: -webkit-flex;
    display: flex;
    height: 124rpx;
    width: 730rpx
}

.home_newB {
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin: 0 auto
}

.home_newB_gzh,.home_newB_jf {
    height: 124rpx;
    width: 360rpx
}

.home_GZH_text {
    font-size: 26rpx;
    font-weight: 700;
    margin-left: 45rpx
}

.home_GZH_text,.home_GZH_text2 {
    color: #000;
    font-family: Source Han Sans CN
}

.home_GZH_text2 {
    font-size: 20rpx;
    font-weight: 400;
    margin-left: 37rpx
}

.home_GZH_Icon {
    border-radius: 50%;
    height: 77rpx;
    margin-left: 220rpx;
    width: 77rpx
}

.home_vip {
    margin: 0 auto;
    width: 730rpx
}

.home_vip_title {
    height: 35rpx;
    margin: 48rpx auto;
    width: 145rpx
}

.home_vip_items {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    width: 730rpx
}

.home_vip_itemXY {
    background: #faced4;
    border-radius: 15rpx;
    height: 400rpx;
    width: 360rpx
}

.home_vip_item {
    height: 195rpx
}

.home_vip_item,.home_vip_items2 {
    border-radius: 15rpx;
    margin-bottom: 10rpx;
    width: 360rpx
}

.home_vip_items2 {
    height: 400rpx
}

.home_vip_sws {
    background: #f0dcf6;
    border-radius: 15rpx;
    height: 195rpx;
    margin: 0 auto;
    width: 730rpx
}
