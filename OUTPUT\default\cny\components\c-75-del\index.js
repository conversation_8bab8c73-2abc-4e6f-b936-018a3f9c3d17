var e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  a = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  n = require("../../../71D07D80549B04BF17B615870C540D65.js"),
  r = require("../../../A5622344549B04BFC3044B435F450D65.js");
Component({
  properties: {},
  data: {
    imgUrl: a.imgUrl,
    imgVersion: a.imgVersion,
    open: !1,
    storyId: ""
  },
  methods: {
    openMask: function(e) {
      this.setData({
        open: !0,
        storyId: e
      }), r.pagePopupViews({
        page_type: "故事详情-我的",
        page_name: "故事详情-我的",
        popup_name: "确认删除内容",
        page_path: "cny/pages/details/index"
      })
    },
    closeMask: function() {
      this.setData({
        open: !1
      }), r.pagePopupClickEvents({
        page_type: "故事详情-我的",
        page_name: "故事详情-我的",
        popup_name: "确认删除内容",
        page_path: "cny/pages/details/index",
        button_name: "弹窗-取消"
      })
    },
    handleClose: function() {
      this.closeMask()
    },
    handleCancel: function() {
      this.closeMask()
    },
    handleConfirm: function() {
      var a = this;
      return t(e().mark((function t() {
        var o, p;
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return e.prev = 0, e.next = 3, (0, n.deleteMyStory)({
                storyId: a.data.storyId
              });
            case 3:
              o = e.sent, p = o.code, o.data, 200 === p && (a.closeMask(), a.triggerEvent("confirm", {
                flag: !0
              }), r.pagePopupClickEvents({
                page_type: "故事详情-我的",
                page_name: "故事详情-我的",
                popup_name: "确认删除内容",
                page_path: "cny/pages/details/index",
                button_name: "弹窗-确认"
              })), e.next = 12;
              break;
            case 9:
              e.prev = 9, e.t0 = e.catch(0), console.error("deleteMyStory:", e.t0);
            case 12:
            case "end":
              return e.stop()
          }
        }), t, null, [
          [0, 9]
        ])
      })))()
    }
  }
});