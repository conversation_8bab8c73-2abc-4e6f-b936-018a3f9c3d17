<view class="OrderList">
    <view class="OrderList_top">
        <view bindtap="changeTab" class="OrderList_top_item" data-item="{{item}}" wx:for="{{tabList}}" wx:key="{{item.state}}">
            <view class="OrderList_top_items" style="color:{{activeState==item.state?'#000':'#7D7D7D'}}">{{item.name}}</view>
            <view class="OrderList_top_item_x" wx:if="{{activeState==item.state}}"></view>
        </view>
    </view>
    <block wx:if="{{list.length!=0}}">
        <view bindtap="gotoOrderInfo" class="OrderList_item" data-item="{{item}}" wx:for="{{list}}" wx:key="index">
            <view class="OrderList_item_top">
                <view class="OrderList_item_top_time">{{item.rowCreateDate}}</view>
                <view class="OrderList_item_top_state" wx:if="{{item.orderStatus==36}}">待收货</view>
                <view class="OrderList_item_top_state" wx:elif="{{item.orderStatus==31}}">待发货</view>
                <view class="OrderList_item_top_state" style="color:#000;" wx:elif="{{item.orderStatus==37||item.orderStatus==82}}">已完成</view>
                <view class="OrderList_item_top_state" wx:elif="{{item.orderStatus==1}}">待支付</view>
                <view class="OrderList_item_top_state" wx:elif="{{item.orderStatus==2}}">已支付积分未付款</view>
                <view class="OrderList_item_top_state" wx:elif="{{item.orderStatus==21}}">待审核</view>
                <view class="OrderList_item_top_state" wx:elif="{{item.orderStatus==32}}">发放中</view>
                <view class="OrderList_item_top_state" wx:elif="{{item.orderStatus==33}}">发放成功</view>
                <view class="OrderList_item_top_state" wx:elif="{{item.orderStatus==34}}">发放失败</view>
                <view class="OrderList_item_top_state" wx:elif="{{item.orderStatus==35}}">重发处理中</view>
                <view class="OrderList_item_top_state" wx:elif="{{item.orderStatus==36}}">已发货</view>
                <view class="OrderList_item_top_state" wx:elif="{{item.orderStatus==83}}">用户手动删除</view>
            </view>
            <view class="OrderList_item_bot" wx:for="{{item.orderDetail}}" wx:for-index="childindex" wx:for-item="items" wx:key="childindex">
                <view class="OrderList_item_bot_l">
                    <view class="OrderList_item_bot_l_img">
                        <image mode="" src="{{items.giftImage}}"></image>
                    </view>
                </view>
                <view class="OrderList_item_bot_r">
                    <view class="OrderList_item_bot_r_t">{{items.giftName}}</view>
                </view>
                <view catchtap="receiveRed" class="OrderList_item_bot_button" data-item="{{item}}" wx:if="{{items.redState}}">领取封面</view>
                <view catchtap="suerOrderGoods" class="OrderList_item_bot_button" data-item="{{item}}" wx:if="{{item.orderStatus==36}}">确认收货</view>
            </view>
        </view>
    </block>
    <view class="noListData" wx:else>暂无数据</view>
    <footer class="footer"></footer>
</view>
