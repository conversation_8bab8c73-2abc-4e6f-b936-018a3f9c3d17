<view bind:transitionend="onTransitionEnd" class="custom-class {{classes}} {{utils.bem( 'popup',[ position,{round:round,safe:safeAreaInsetBottom,safeTop:safeAreaInsetTop,safeTabBar:safeAreaTabBar} ] )}}" style="{{computed.popupStyle( {zIndex:zIndex,currentDuration:currentDuration,display:display,customStyle:customStyle} )}}" wx:if="{{inited}}">
    <slot></slot>
    <van-icon bind:tap="onClickCloseIcon" class="close-icon-class van-popup__close-icon van-popup__close-icon--{{closeIconPosition}}" name="{{closeIcon}}" wx:if="{{closeable}}"></van-icon>
</view>
