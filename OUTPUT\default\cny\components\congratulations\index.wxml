<view class="maskContainer" hoverClass="none" hoverStopPropagation="false" wx:if="{{open}}">
    <view class="maskBox maskImgCenter  " hoverClass="none" hoverStopPropagation="false">
        <view class="cardBigBox {{currentState==='back'?'rotateAni':''}}" hoverClass="none" hoverStopPropagation="false">
            <view class="maskCard maskFront" hoverClass="none" hoverStopPropagation="false" wx:if="{{!hideFront}}">
                <image binderror="" bindload="" class="maskBg " lazyLoad="false" src="{{imgUrl}}components/congratulations/bg.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" class="tip1 " lazyLoad="false" src="{{imgUrl}}components/congratulations/tip1.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" class="tip2 " lazyLoad="false" src="{{imgUrl}}components/congratulations/tip2-{{RedCoverType}}.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" class="tip3 tip3-{{RedCoverType}}" lazyLoad="false" src="{{imgUrl}}components/congratulations/tip3-{{RedCoverType}}.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" class="red-cover " lazyLoad="false" src="{{imgUrl}}components/congratulations/red{{RedCoverType}}.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" catch:tap="handleGoGetRedCover" class="s1 " lazyLoad="false" src="{{imgUrl}}components/congratulations/s1.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" class="tip4 " lazyLoad="false" src="{{imgUrl}}components/congratulations/tip4.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" catch:tap="handleClose" class="close " lazyLoad="false" src="{{imgUrl}}components/congratulations/close.png?v={{imgVersion}}"></image>
            </view>
            <view class="maskCard maskBack" hoverClass="none" hoverStopPropagation="false">
                <image binderror="" bindload="" class="maskBg " lazyLoad="false" src="{{imgUrl}}components/coupon/bg.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" class="title " lazyLoad="false" src="{{imgUrl}}components/coupon/title.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" class="tip1 " lazyLoad="false" src="{{imgUrl}}components/coupon/tip1.png?v={{imgVersion}}"></image>
                <view class="cardBox" hoverClass="none" hoverStopPropagation="false">
                    <image binderror="" bindload="" class="letter " lazyLoad="false" src="{{imgUrl}}components/coupon/letter.png?v={{imgVersion}}"></image>
                    <image binderror="" bindload="" class="bigBg " lazyLoad="false" src="{{Coupon.infoPic}}"></image>
                    <view class="brandName SourceHanSerifCN-Bold">{{Coupon.infoTitleOne}}</view>
                    <image binderror="" bindload="" class="couponName " lazyLoad="false" src="{{Coupon.infoTitlePic}}" wx:if="{{Coupon.infoTitlePic}}"></image>
                    <view class="couponName couponName2 SourceHanSerifCN-Regular" wx:else>{{Coupon.infoTitleTwo}}</view>
                </view>
                <image binderror="" bindload="" catch:tap="handleStartUse" class="s1 " lazyLoad="false" src="{{imgUrl}}components/coupon/s1.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" catch:tap="handleGoTask" class="s2 " lazyLoad="false" src="{{imgUrl}}components/coupon/s2.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" class="tip2 " lazyLoad="false" src="{{imgUrl}}components/coupon/tip2.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" catch:tap="handleClose" class="close " lazyLoad="false" src="{{imgUrl}}components/congratulations/close.png?v={{imgVersion}}"></image>
            </view>
        </view>
    </view>
</view>
