<view class="storeClockIn">
    <view class="storeClockIn_bg">
        <view class="home_swiperBox" style="height:1000rpx;">
            <swiper autoplay="{{autoplay}}" circular="{{circular}}" class="home_swiper" current="{{currentIndex}}" duration="{{duration}}" indicatorActiveColor="#D20303" indicatorDots="{{indicatorDots}}" interval="{{interval}}">
                <swiper-item wx:for="{{bannerImgs}}" wx:key="index">
                    <view catchtap="gotoWebUrl" class="swiper-item" data-item="{{item}}">
                        <image mode="widthFix" src="{{item.imgUrl}}"></image>
                    </view>
                </swiper-item>
            </swiper>
            <view bindtap="onPrev" class="home_swiper_left">
                <image mode="" src="{{img}}/newVersion/001.png"></image>
            </view>
            <view bindtap="onNext" class="home_swiper_right">
                <image mode="" src="{{img}}/newVersion/002.png"></image>
            </view>
            <view bindtap="gotoLookStore" class="storeClockIn_jl" style="right:180rpx;">查看门店</view>
            <view bindtap="gotoList" class="storeClockIn_jl">打卡记录</view>
            <view bindtap="gotoclockIn" class="storeClockIn_button" wx:if="{{isClockState}}">
                <image mode="" src="{{img}}store/ljSignIn0704.png"></image>
            </view>
            <view class="storeClockIn_button" wx:else>
                <image mode="" src="{{img}}store/ydk.png"></image>
            </view>
            <view bindtap="refresh" class="storeClockIn_Refresh">
                <image mode="" src="{{img}}store/RefreshNew0627.png"></image>
            </view>
        </view>
        <view class="storeClockIn_item">
            <view class="storeClockIn_item_name">
                <view class="storeClockIn_item_name_icon">
                    <image mode="" src="{{img}}store/nameIcon0627.png"></image>
                </view>
                <view class="storeClockIn_item_name_text">门店名称：{{storeName}}</view>
            </view>
            <view class="storeClockIn_item_name">
                <view class="storeClockIn_item_name_icon">
                    <image mode="" src="{{img}}store/addressIcon0627.png"></image>
                </view>
                <view class="storeClockIn_item_name_text">门店具体地址：{{storeAddress}}</view>
            </view>
        </view>
        <view class="storeClockIn_box">
            <rich-text class="cnt" nodes="{{remark}}"></rich-text>
        </view>
        <view class="storeClockIn_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <prop bindcloseProp="closeProp" clockMsg="{{clockMsg}}" energyNumber="{{energyNumber}}" propNum="{{propNum}}" storeName="{{storeName}}" wx:if="{{propState}}"></prop>
</view>
