var e, t = (e = require("B6135D02549B04BFD0753505DD930D65.js")) && e.__esModule ? e : {
  default: e
};
App({
  onLaunch: function() {
    var e = wx.getStorageSync("logs") || [];
    e.unshift(Date.now()), wx.setStorageSync("logs", e), wx.login({
      success: function(e) {}
    }), wx.onAppRoute((function() {
      var e = getCurrentPages(),
        t = e[e.length - 1];
      console.log("view", t), t && t.onShareAppMessage && (t.onShareAppMessage() || (t.onShareAppMessage = function() {
        return {
          title: "花生帮粉丝俱乐部",
          path: "/pages/Home/Home"
        }
      }))
    }))
  },
  globalData: {
    img: "https://dm-assets.supercarrier8.com/wobei/",
    imgNew: "http://dm-assets.supercarrier8.com/wobei/newImgs/",
    appId: "wx841a8e9e6972a9a6",
    enterpriseNo: t.default.enterpriseNo,
    shopActivityId: t.default.shopActivityId,
    activityId: t.default.activityId
  }
});