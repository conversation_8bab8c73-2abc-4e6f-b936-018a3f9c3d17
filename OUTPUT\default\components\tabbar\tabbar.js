Component({
  properties: {
    actice: {
      type: String,
      value: null,
      observer: function(e, t) {
        this.data.IconList.forEach((function(t, a) {
          e === t.path && (t.state = !0)
        })), this.setData({
          IconList: this.data.IconList
        })
      }
    }
  },
  data: {
    active: "",
    IconList: [{
      iconPath: "/images/homeIconNo.png",
      selectedIconPath: "/images/homeIcon.png",
      path: "home",
      name: "首页",
      state: !1
    }, {
      iconPath: "/images/energyMallIconNo.png",
      selectedIconPath: "/images/energyMallIcon.png",
      path: "mall",
      name: "能量商城",
      state: !1
    }, {
      iconPath: "/images/myIconNo.png",
      selectedIconPath: "/images/myIcon.png",
      path: "my",
      name: "我的",
      state: !1
    }]
  },
  methods: {
    gotoUrl: function(e) {
      var t = e.currentTarget.dataset.index;
      0 === t ? wx.switchTab({
        url: "/pages/Home/Home"
      }) : 1 === t ? wx.switchTab({
        url: "/pages/EnergyMall/EnergyMall"
      }) : 2 === t && wx.switchTab({
        url: "/pages/PersonalCenter/PersonalCenter"
      })
    }
  }
});