<view class="TaskCenter">
    <view bindtap="showRule" class="signIn_rule">
        <image mode="" src="{{img}}newVersion/022.png"></image>
    </view>
    <view class="TaskCenter_top">
        <view class="TaskCenter_top_title">做任务 获取能量值</view>
        <view class="TaskCenter_top_box">
            <view class="TaskCenter_top_box_icon">
                <image mode="" src="{{img}}newVersion/043.png"></image>
            </view>
            <view class="TaskCenter_top_box_text">我的能量值：<text style="font-size:36rpx;">{{userInfo.currentJf}}</text>
            </view>
        </view>
    </view>
    <view class="TaskCenter_item" wx:for="{{taskList}}" wx:key="index">
        <view class="TaskCenter_item_icon">
            <image mode="" src="{{item.taskIco}}"></image>
        </view>
        <view class="TaskCenter_item_text">
            <view class="text_title">{{item.taskName}}</view>
            <view class="text_subtitle">
                <rich-text class="cnt" nodes="{{item.taskDescribe}}"></rich-text>
            </view>
        </view>
        <block wx:if="{{item.taskInfo==0}}">
            <block wx:if="{{item.indexNo=='i_0001'}}">
                <view bindtap="gotoSignIn" class="TaskCenter_item_button" wx:if="{{item.status==0}}">去签到</view>
                <view bindtap="gotoReceivePrice" class="TaskCenter_item_button" data-item="{{item}}" wx:elif="{{item.status==1}}">领取奖励</view>
                <view class="TaskCenter_item_buttons" wx:else>已完成</view>
            </block>
            <block wx:elif="{{item.indexNo=='i_0002'}}">
                <view bindtap="gotoLook" class="TaskCenter_item_button" data-item="{{item}}" wx:if="{{item.status==0}}">去完成</view>
                <view bindtap="gotoReceivePrice" class="TaskCenter_item_button" data-item="{{item}}" wx:elif="{{item.status==1}}">领取奖励</view>
                <view class="TaskCenter_item_buttons" wx:else>已完成</view>
            </block>
            <block wx:elif="{{item.indexNo=='i_0003'}}">
                <view bindtap="gotoLook" class="TaskCenter_item_button" data-item="{{item}}" wx:if="{{item.status==0}}">去观看</view>
                <view bindtap="gotoReceivePrice" class="TaskCenter_item_button" data-item="{{item}}" wx:elif="{{item.status==1}}">领取奖励</view>
                <view class="TaskCenter_item_buttons" wx:else>已完成</view>
            </block>
            <block wx:elif="{{item.indexNo=='i_0004'}}">
                <div bindtap="gotoShare" class="TaskCenter_item_button" data-item="{{item}}" wx:if="{{item.status==0}}">去分享<button class="TaskCenter_item_button_b" openType="share"></button>
                </div>
                <view bindtap="gotoReceivePrice" class="TaskCenter_item_button" data-item="{{item}}" wx:elif="{{item.status==1}}">领取奖励</view>
                <view class="TaskCenter_item_buttons" wx:else>已完成</view>
            </block>
            <block wx:elif="{{item.indexNo=='i_0005'}}">
                <view bindtap="gotoStore" class="TaskCenter_item_button" data-item="{{item}}" wx:if="{{item.status==0}}">去打卡</view>
                <view bindtap="gotoReceivePrice" class="TaskCenter_item_button" data-item="{{item}}" wx:elif="{{item.status==1}}">领取奖励</view>
                <view class="TaskCenter_item_buttons" wx:else>已完成</view>
            </block>
        </block>
        <view class="TaskCenter_item_buttons" wx:else>已完成</view>
    </view>
    <view class="TaskCenter_item" wx:if="{{isInviteState}}">
        <view class="TaskCenter_item_icon">
            <image mode="" src="{{img}}newVersion/icon-invitation.png"></image>
        </view>
        <view class="TaskCenter_item_text">
            <view class="text_title">邀请好友注册</view>
            <view class="text_subtitle">{{configRubric}}</view>
        </view>
        <div bindtap="gotoShareReg" class="TaskCenter_item_button">邀请注册<button class="TaskCenter_item_button_b" openType="share"></button>
        </div>
    </view>
    <view class="TaskCenter_item">
        <view class="TaskCenter_item_icon">
            <image mode="" src="{{img}}newVersion/icon-anti.png"></image>
        </view>
        <view class="TaskCenter_item_text">
            <view class="text_title">扫码查防伪</view>
            <view class="text_subtitle">扫码赢能量</view>
        </view>
        <view bindtap="gotoScan" class="TaskCenter_item_button">去扫码</view>
    </view>
    <prop bindcloseProp="closeProp" class="prop_n" priceMsg="{{priceMsg}}" propNum="{{propNum}}" ruleText="{{ruleText}}" wx:if="{{propState}}"></prop>
    <footer class="footer"></footer>
</view>
