var e, s = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  t = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  d = (e = require("../../../87624F60549B04BFE10427674BE30D65.js")) && e.__esModule ? e : {
    default: e
  };
var a = getApp();
Page({
  data: {
    img: a.globalData.img,
    list: [],
    isSubOrderJoin: !1,
    defaultAddress: {}
  },
  getUserAddressList: function() {
    var e = this;
    (0, t.loadingOpen)(), (0, s.getUserAddressList)({}).then((function(s) {
      (0, t.loadingClose)(), s.data.forEach((function(s) {
        s.id == e.data.defaultAddress.id && (d.default.data.orderChooseAddress = s)
      })), e.setData({
        list: s.data || []
      })
    }))
  },
  chooseOrderAddress: function(e) {
    var s = e.currentTarget.dataset.item;
    console.log("选择当前地址", s), s.id != this.data.defaultAddress.id && (d.default.data.orderChooseAddress = s, console.log("点击时设置地址", d.default.data.orderChooseAddress), d.default.data.orderChooseAddressState = !0, wx.navigateBack())
  },
  delAddress: function(e) {
    var d = this,
      a = e.currentTarget.dataset.item;
    (0, t.showModel)("是否确认删除？").then((function(e) {
      e && (0, s.deleteDefaultAddress)({
        id: a.id
      }).then((function(e) {
        200 == e.code ? ((0, t.toastModel)("删除成功"), d.getUserAddressList()) : (0, t.toastModel)(e.message)
      }))
    }))
  },
  gotoExitAddress: function(e) {
    var s = e.currentTarget.dataset.item;
    wx.navigateTo({
      url: "/pages/PersonalCenter/Address/Address?addressInfo=" + JSON.stringify(s)
    })
  },
  gotoAddress: function() {
    wx.navigateTo({
      url: "/pages/PersonalCenter/Address/Address"
    })
  },
  onLoad: function(e) {
    console.log("options", e), e.defaultAddress ? (console.log("options", JSON.parse(e.defaultAddress)), this.setData({
      isSubOrderJoin: !0
    }), this.setData({
      defaultAddress: JSON.parse(e.defaultAddress)
    })) : this.setData({
      isSubOrderJoin: !1
    })
  },
  onReady: function() {},
  onShow: function() {
    console.log("地址列表"), console.log("defaultAddress", this.data.defaultAddress), this.getUserAddressList()
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {}
});