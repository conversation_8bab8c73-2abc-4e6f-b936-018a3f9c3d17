.TaskCenter {
    background: #fff;
    box-sizing: border-box;
    min-height: 100vh;
    padding-bottom: 140rpx;
    width: 100%
}

.signIn_rule {
    height: 39rpx;
    position: absolute;
    right: 21rpx;
    top: 21rpx;
    width: 39rpx
}

.TaskCenter_top {
    background: url("https://dm-assets.supercarrier8.com/wobei/newVersion/042.png");
    background-size: 100% 100%;
    box-sizing: border-box;
    height: 268rpx;
    margin-bottom: 60rpx;
    padding-top: 50rpx;
    width: 100%
}

.TaskCenter_top_title {
    color: #000;
    font-family: STYuanti;
    font-size: 48rpx;
    font-weight: 700;
    text-align: center
}

.TaskCenter_top_box {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    margin: 20rpx auto 0
}

.TaskCenter_top_box_icon {
    height: 51rpx;
    width: 54rpx
}

.TaskCenter_top_box_text {
    color: #000;
    font-family: Source <PERSON> Sans CN;
    font-size: 32rpx;
    font-weight: 500;
    margin-left: 12rpx
}

.TaskCenter_item {
    -webkit-align-items: center;
    align-items: center;
    background: #fff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 11rpx 1rpx hsla(0,0%,74%,.6);
    display: -webkit-flex;
    display: flex;
    height: 146rpx;
    margin: 0 auto 41rpx;
    width: 693rpx
}

.TaskCenter_item_icon {
    height: 82rpx;
    margin-left: 40rpx;
    width: 82rpx
}

.TaskCenter_item_text {
    margin-left: 27rpx;
    width: 330rpx
}

.text_title {
    color: #000;
    font-family: STYuanti;
    font-size: 25rpx;
    font-weight: 700
}

.text_subtitle {
    color: #4d4d4d;
    font-family: Source Han Sans CN;
    font-size: 23rpx;
    font-weight: 400
}

.TaskCenter_item_button {
    background: #000;
    border-radius: 25rpx;
    color: #fff;
    font-family: Source Han Sans CN;
    font-size: 25rpx;
    font-weight: 500;
    height: 54rpx;
    line-height: 54rpx;
    margin-left: 30rpx;
    position: relative;
    text-align: center;
    width: 158rpx
}

wx-button {
    background-color: inherit;
    margin: 0;
    padding: 0;
    position: static
}

wx-button:after {
    content: none
}

wx-button::after {
    border: none
}

.TaskCenter_item_button_b {
    height: 54rpx!important;
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    width: 158rpx!important
}

.TaskCenter_item_buttons {
    border-radius: 25rpx;
    box-sizing: border-box;
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 25rpx;
    font-weight: 500;
    height: 54rpx;
    line-height: 50rpx;
    margin-left: 30rpx;
    text-align: center;
    width: 158rpx
}

.prop_rule_box_rule {
    font-size: 24rpx;
    line-height: 2.5
}
