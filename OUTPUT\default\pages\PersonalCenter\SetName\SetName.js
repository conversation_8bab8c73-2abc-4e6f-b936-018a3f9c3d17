var e, n = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  a = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  i = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  r = require("../../../83F188C3549B04BFE597E0C403C30D65.js"),
  o = (e = require("../../../87624F60549B04BFE10427674BE30D65.js")) && e.__esModule ? e : {
    default: e
  };
Page({
  data: {
    nickName: ""
  },
  setNickName: function() {
    if ("" == this.data.nickName) return (0, a.toastModel)("请输入昵称");
    console.log(this.data.nickName), (0, a.loadingOpen)(), (0, i.updateName)({
      name: this.data.nickName
    }).then(function() {
      var e = t(n().mark((function e(t) {
        return n().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return (0, a.loadingClose)(), e.next = 3, (0, r.getUser)();
            case 3:
              wx.navigateBack();
            case 4:
            case "end":
              return e.stop()
          }
        }), e)
      })));
      return function(n) {
        return e.apply(this, arguments)
      }
    }())
  },
  onLoad: function(e) {},
  onReady: function() {},
  onShow: function() {
    this.setData({
      nickName: o.default.data.userInfo.memberName
    })
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {}
});