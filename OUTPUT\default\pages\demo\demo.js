var t = "DAkHeza7GMvAuA5FL2p8GAZxC84WoMLh";
Page({
  data: {
    latitude: null,
    longitude: null,
    poiResults: [],
    query: ""
  },
  select: function(t) {
    var o = t.currentTarget.dataset,
      e = o.lat,
      n = o.lng;
    this.convertGPS(e, n)
  },
  convertGPS: function(t, o) {
    wx.request({
      url: "https://api-fat.yesno.com.cn/cncop/area/convert-gps?latitude=".concat(t, "&longitude=").concat(o),
      success: function(t) {
        var o = t.data.data.result;
        console.log(t), console.log("国家", o.addressComponent.country), console.log("省", o.addressComponent.province), console.log("市", o.addressComponent.city), console.log("区", o.addressComponent.district), console.log("乡镇", o.addressComponent.town), console.log("areaid", o.addressComponent.adcode), console.log("地址信息", o.formattedAddress)
      }
    })
  },
  search: function() {
    var o = this;
    wx.request({
      url: "https://api.map.baidu.com/place/v2/search?query=".concat(this.data.query, "&radius=10000&location=").concat(this.data.latitude, ",").concat(this.data.longitude, "&output=json&ak=").concat(t, "&coord_type=gcj02ll"),
      success: function(t) {
        o.setData({
          poiResults: t.data.results
        })
      }
    })
  },
  onLoad: function(o) {
    var e = this,
      n = wx.createMapContext("mapId");
    wx.getLocation({
      type: "gcj02"
    }).then((function(o) {
      console.log(o), wx.request({
        url: "https://api.map.baidu.com/place/v2/search?query=商场&radius=10000&location=".concat(o.latitude, ",").concat(o.longitude, "&output=json&ak=").concat(t, "&coord_type=gcj02ll&scope=2"),
        success: function(t) {
          e.setData({
            poiResults: t.data.results
          })
        }
      }), e.setData({
        latitude: o.latitude,
        longitude: o.longitude
      }), n.addMarkers({
        markers: [{
          latitude: o.latitude,
          longitude: o.longitude
        }],
        clear: !0
      })
    }))
  },
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {}
});