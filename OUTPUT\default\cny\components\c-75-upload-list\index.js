var e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  r = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  a = require("../../../71D07D80549B04BF17B615870C540D65.js"),
  n = require("../../../A5622344549B04BFC3044B435F450D65.js");
Component({
  properties: {
    type: {
      type: String,
      value: "rank"
    },
    dataList: {
      type: Array,
      value: []
    }
  },
  data: {
    imgUrl: r.imgUrl,
    imgVersion: r.imgVersion,
    list: [],
    isLike: !1,
    noMoreData: !1
  },
  methods: {
    bindscrolltolower: function(e) {
      this.triggerEvent("bindscrolltolower", {
        flag: !0
      })
    },
    handleGoDetail: function(e) {
      console.log(e, "===e===");
      var t = e.currentTarget.dataset,
        r = t.id,
        a = t.status,
        i = t.info;
      return "1" === a ? wx.showToast({
        icon: "none",
        title: "该故事正在审核中"
      }) : "3" === a ? wx.showToast({
        icon: "none",
        title: "该故事审核不通过",
        duration: 2e3
      }) : (wx.navigateTo({
        url: "/cny/pages/details/index?isDel=".concat(this.data.type, "&storyId=").concat(r)
      }), void n.pageClickEvent({
        refer_page_type: "",
        refer_page_name: "",
        page_type: "rank" === this.data.type ? "排行榜" : "上传记录",
        page_name: "rank" === this.data.type ? "排行榜" : "上传记录",
        module_name: "",
        module_rank: "1",
        page_path: "rank" === this.data.type ? "cny/pages/rankList/index" : "cny/pages/record/index",
        button_name: i.context
      }))
    },
    handleLike: function(e) {
      var t = e.currentTarget.dataset,
        r = (t.userId, t.storyId);
      wx.navigateTo({
        url: "/cny/pages/details/index?isDel=".concat(this.data.type, "&storyId=").concat(r)
      })
    },
    likeStoryFn: function(r) {
      var n = this;
      return t(e().mark((function t() {
        var i, o;
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return e.prev = 0, e.next = 3, (0, a.likeStory)({
                storyId: r
              });
            case 3:
              i = e.sent, o = i.code, i.data, 200 === o && n.triggerEvent("likeStory", {
                flag: !0
              }), e.next = 12;
              break;
            case 9:
              e.prev = 9, e.t0 = e.catch(0), console.log("likeStory::", e.t0);
            case 12:
            case "end":
              return e.stop()
          }
        }), t, null, [
          [0, 9]
        ])
      })))()
    },
    noMoreDataFn: function(e) {
      this.setData({
        noMoreData: e
      })
    }
  }
});