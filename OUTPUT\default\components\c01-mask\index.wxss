.maskIcon {
    bottom: 500rpx;
    height: 131rpx;
    right: 10rpx;
    width: 131rpx
}

.maskContainer,.maskIcon {
    position: fixed;
    z-index: 99999
}

.maskContainer {
    background: rgba(0,0,0,.6);
    height: 100%;
    left: 0;
    top: 0;
    width: 100%
}

.maskBox {
    -webkit-animation: fadeIn .5s linear alternate;
    animation: fadeIn .5s linear alternate;
    height: auto;
    left: 50%;
    position: absolute;
    top: 52rpx;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 670rpx
}

.maskBox .maskKv {
    height: 1075rpx;
    width: 100%
}

.maskBox .maskBtn {
    bottom: 108rpx;
    height: 70rpx;
    left: 50%;
    position: absolute;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 386rpx
}

.maskBox .maskClose {
    display: block;
    height: 60rpx;
    margin: 10rpx auto 0;
    width: 60rpx
}

@-webkit-keyframes fadeIn {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}