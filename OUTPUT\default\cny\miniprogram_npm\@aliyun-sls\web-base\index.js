require("../../../../@babel/runtime/helpers/Arrayincludes"), require("../../../../@babel/runtime/helpers/Objectentries");
var e, n, t = require("../../../../@babel/runtime/helpers/slicedToArray"),
  r = require("../../../../@babel/runtime/helpers/createForOfIteratorHelper"),
  o = require("../../../../@babel/runtime/helpers/classCallCheck"),
  i = require("../../../../@babel/runtime/helpers/createClass"),
  c = require("../../../../@babel/runtime/helpers/typeof");
module.exports = (e = {}, n = function(n, t) {
  if (!e[n]) return require(t);
  if (!e[n].status) {
    var r = e[n].m;
    r._exports = r._tempexports;
    var o = Object.getOwnPropertyDescriptor(r, "exports");
    o && o.configurable && Object.defineProperty(r, "exports", {
      set: function(e) {
        "object" === c(e) && e !== r._exports && (r._exports.__proto__ = e.__proto__, Object.keys(e).forEach((function(n) {
          r._exports[n] = e[n]
        }))), r._tempexports = e
      },
      get: function() {
        return r._tempexports
      }
    }), e[n].status = 1, e[n].func(e[n].req, r, r.exports)
  }
  return e[n].m.exports
}, function(n, t, r) {
  e[n] = {
    status: 0,
    func: t,
    req: r,
    m: {
      exports: {},
      _tempexports: {}
    }
  }
}(1739784025837, (function(e, n, a) {
  var u = e("@aliyun-sls/web-types");

  function s(e, n, t) {
    Object.defineProperty(e, n, {
      writable: !0,
      enumerable: !0,
      configurable: !0,
      value: t
    })
  }
  var l = function() {
    function e(n) {
      o(this, e), this.onFirstSubscribe = n, this.observers = []
    }
    return i(e, [{
      key: "subscribe",
      value: function(e) {
        var n = this;
        return !this.observers.length && this.onFirstSubscribe && (this.onLastUnsubscribe = this.onFirstSubscribe() || void 0), this.observers.push(e), {
          unsubscribe: function() {
            n.observers = n.observers.filter((function(n) {
              return e !== n
            })), !n.observers.length && n.onLastUnsubscribe && n.onLastUnsubscribe()
          }
        }
      }
    }, {
      key: "notify",
      value: function(e) {
        this.observers.forEach((function(n) {
          return n(e)
        }))
      }
    }]), e
  }();

  function f(e, n, t) {
    try {
      return e.apply(n, t)
    } catch (e) {
      return
    }
  }

  function p() {}
  var d = Object.defineProperty,
    v = Object.defineProperties,
    h = Object.getOwnPropertyDescriptors,
    b = Object.getOwnPropertySymbols,
    m = Object.prototype.hasOwnProperty,
    g = Object.prototype.propertyIsEnumerable,
    y = function(e, n, t) {
      return n in e ? d(e, n, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: t
      }) : e[n] = t
    };

  function O(e) {
    return "number" == typeof e
  }

  function S(e, n) {
    return +e.toFixed(n)
  }

  function k(e) {
    return e ? (parseInt(e, 10) ^ 16 * Math.random() >> parseInt(e, 10) / 4).toString(16) : "".concat(1e7, "-", 1e3, "-", 4e3, "-", 8e3, "-", 1e11).replace(/[018]/g, k)
  }

  function x() {
    for (var e, n, t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 20, r = new Array(t), o = Date.now().toString(36).split(""); t-- > 0;) n = (e = 36 * Math.random() | 0).toString(36), r[t] = e % 3 ? n : n.toUpperCase();
    for (var i = 0; i < 8; i++) r.splice(3 * i + 2, 0, o[i]);
    return r.join("")
  }

  function w(e, n) {
    return function() {
      try {
        return e.apply(void 0, arguments)
      } catch (e) {
        console.error(n, e)
      }
    }
  }

  function j(e) {
    return e.host.startsWith("http://") || e.host.startsWith("https://") ? e.host + "/logstores/" + e.logstore + (e.stsPlugin ? "" : "/track?APIVersion=0.6.0") : "https://" + e.project + "." + e.host + "/logstores/" + e.logstore + (e.stsPlugin ? "" : "/track?APIVersion=0.6.0")
  }
  var I, E = /^(?:([^:\/?#]+):\/\/)?((?:([^\/?#@]*)@)?([^\/?#:]*)(?:\:(\d*))?)?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n)*))?/i;

  function P(e, n) {
    for (var t = arguments.length, r = new Array(t > 2 ? t - 2 : 0), o = 2; o < t; o++) r[o - 2] = arguments[o];
    return !(null == n || !Array.isArray(n)) && n.some((function(n) {
      if ("function" == typeof n) try {
        return n.apply(void 0, [e].concat(r))
      } catch (e) {
        return console.error("user function callback threw an error:", e), !1
      }
      return "string" == typeof n ? e.includes(n) : "[object RegExp]" === Object.prototype.toString.call(n) && n.test(e)
    }))
  }

  function _(e) {
    var n = Date.now() - performance.now();
    return n > L() ? Math.round(n + e) : A(e)
  }

  function T() {
    return Date.now()
  }

  function R() {
    return performance.now()
  }

  function A(e) {
    return Math.round(L() + e)
  }

  function L() {
    return void 0 === I && (I = performance.timing.navigationStart), I
  }

  function N(e, n) {
    var t, r = void 0 === n ? 0 : +n;
    try {
      if (t = function(e) {
          var n = M(e, "stacktrace");
          if (n) {
            for (var t, r = / line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i, o = / line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\((.*)\))? in (.*):\s*$/i, i = n.split("\n"), c = [], a = 0; a < i.length; a += 2) {
              var u = void 0;
              r.exec(i[a]) ? (t = r.exec(i[a]), u = {
                args: [],
                column: void 0,
                func: t[3],
                line: +t[1],
                url: t[2]
              }) : o.exec(i[a]) && (t = o.exec(i[a]), u = {
                args: t[5] ? t[5].split(",") : [],
                column: +t[2],
                func: t[3] || t[4],
                line: +t[1],
                url: t[6]
              }), u && (!u.func && u.line && (u.func = "?"), u.context = [i[a + 1]], c.push(u))
            }
            if (c.length) return {
              stack: c,
              message: M(e, "message"),
              name: M(e, "name")
            }
          }
        }(e)) return t
    } catch (e) {}
    try {
      if (t = function(e) {
          var n = M(e, "stack");
          if (n) {
            for (var t, r, o, i, c = /^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i, a = /^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|capacitor|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i, u = /^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i, s = /(\S+) line (\d+)(?: > eval line \d+)* > eval/i, l = /\((\S*)(?::(\d+))(?::(\d+))\)/, f = n.split("\n"), p = [], d = 0, v = f.length; d < v; d += 1) {
              if (c.exec(f[d])) {
                var h = (o = c.exec(f[d]))[2] && 0 === o[2].indexOf("native");
                t = o[2] && 0 === o[2].indexOf("eval"), r = l.exec(o[2]), t && r && (o[2] = r[1], o[3] = r[2], o[4] = r[3]), i = {
                  args: h ? [o[2]] : [],
                  column: o[4] ? +o[4] : void 0,
                  func: o[1] || "?",
                  line: o[3] ? +o[3] : void 0,
                  url: h ? void 0 : o[2]
                }
              } else if (u.exec(f[d])) o = u.exec(f[d]), i = {
                args: [],
                column: o[4] ? +o[4] : void 0,
                func: o[1] || "?",
                line: +o[3],
                url: o[2]
              };
              else {
                if (!a.exec(f[d])) continue;
                o = a.exec(f[d]), t = o[3] && o[3].indexOf(" > eval") > -1, r = s.exec(o[3]), t && r ? (o[3] = r[1], o[4] = r[2], o[5] = void 0) : 0 !== d || o[5] || void 0 === e.columnNumber || (p[0].column = e.columnNumber + 1), i = {
                  args: o[2] ? o[2].split(",") : [],
                  column: o[5] ? +o[5] : void 0,
                  func: o[1] || "?",
                  line: o[4] ? +o[4] : void 0,
                  url: o[3]
                }
              }!i.func && i.line && (i.func = "?"), p.push(i)
            }
            if (p.length) return {
              stack: p,
              message: M(e, "message"),
              name: M(e, "name")
            }
          }
        }(e)) return t
    } catch (e) {}
    try {
      if (t = function(e) {
          var n = M(e, "message");
          if (n) {
            var t = n.split("\n");
            if (!(t.length < 4)) {
              var r, o, i, c = /^\s*Line (\d+) of linked script ((?:file|https?|blob)\S+)(?:: in function (\S+))?\s*$/i,
                a = /^\s*Line (\d+) of inline#(\d+) script in ((?:file|https?|blob)\S+)(?:: in function (\S+))?\s*$/i,
                u = /^\s*Line (\d+) of function script\s*$/i,
                s = [],
                l = window && window.document && window.document.getElementsByTagName("script"),
                f = [];
              for (var p in l) o = l, i = p, Object.prototype.hasOwnProperty.call(o, i) && !l[p].src && f.push(l[p]);
              for (var d = 2; d < t.length; d += 2) {
                var v = void 0;
                if (c.exec(t[d])) r = c.exec(t[d]), v = {
                  args: [],
                  column: void 0,
                  func: r[3],
                  line: +r[1],
                  url: r[2]
                };
                else if (a.exec(t[d])) r = a.exec(t[d]), v = {
                  args: [],
                  column: void 0,
                  func: r[4],
                  line: +r[1],
                  url: r[3]
                };
                else if (u.exec(t[d])) {
                  r = u.exec(t[d]);
                  var h = window.location.href.replace(/#.*$/, "");
                  v = {
                    url: h,
                    args: [],
                    column: void 0,
                    func: "",
                    line: +r[1]
                  }
                }
                v && (v.func || (v.func = "?"), v.context = [t[d + 1]], s.push(v))
              }
              if (s.length) return {
                stack: s,
                message: t[0],
                name: M(e, "name")
              }
            }
          }
        }(e)) return t
    } catch (e) {}
    try {
      if (t = U(e, r + 1)) return t
    } catch (e) {}
    return {
      message: M(e, "message"),
      name: M(e, "name"),
      stack: []
    }
  }

  function U(e, n) {
    for (var t, r, o = /function\s+([_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*)?\s*\(/i, i = [], c = {}, a = !1, u = U.caller; u && !a; u = u.caller) u !== N && (r = {
      args: [],
      column: void 0,
      func: "?",
      line: void 0,
      url: void 0
    }, t = o.exec(u.toString()), u.name ? r.func = u.name : t && (r.func = t[1]), void 0 === r.func && (r.func = t ? t.input.substring(0, t.input.indexOf("{")) : void 0), c[u.toString()] ? a = !0 : c[u.toString()] = !0, i.push(r));
    n && i.splice(0, n);
    var s = {
      stack: i,
      message: M(e, "message"),
      name: M(e, "name")
    };
    return function(e, n, t) {
      var r = {
        url: n,
        line: t ? +t : void 0
      };
      if (r.url && r.line) {
        e.incomplete = !1;
        var o = e.stack;
        if (o.length > 0 && o[0].url === r.url) {
          if (o[0].line === r.line) return !1;
          if (!o[0].line && o[0].func === r.func) return o[0].line = r.line, o[0].context = r.context, !1
        }
        return o.unshift(r), e.partial = !0, !0
      }
      e.incomplete = !0
    }(s, M(e, "sourceURL") || M(e, "fileName"), M(e, "line") || M(e, "lineNumber")), s
  }

  function M(e, n) {
    if ("object" === c(e) && e && n in e) {
      var t = e[n];
      return "string" == typeof t ? t : void 0
    }
  }

  function H(e, n, t) {
    var r = e[n],
      o = t(r),
      i = function() {
        return o.apply(this, arguments)
      };
    return e[n] = i, {
      stop: function() {
        e[n] === i ? e[n] = r : o = r
      }
    }
  }

  function C(e, n, t) {
    var r = t.before,
      o = t.after;
    return H(e, n, (function(e) {
      return function() {
        var n, t = arguments;
        return r && f(r, this, t), "function" == typeof e && (n = e.apply(this, t)), o && f(o, this, t), n
      }
    }))
  }
  var D = /^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/;

  function F(e) {
    return "".concat(e.name || "Error", ": ").concat(e.message)
  }

  function q(e) {
    return e.filter((function(e) {
      return null == e.url || !e.url.includes("sls-rum.js")
    }))
  }

  function B(e) {
    var n = F(e);
    return e.stack.forEach((function(e) {
      var t = "?" === e.func ? "<anonymous>" : e.func,
        r = e.args && e.args.length > 0 ? "(".concat(e.args.join(", "), ")") : "",
        o = e.line ? ":".concat(e.line) : "",
        i = e.line && e.column ? ":".concat(e.column) : "";
      n += "\n  at ".concat(t).concat(r, " @ ").concat(e.url).concat(o).concat(i)
    })), encodeURIComponent(n)
  }
  var $ = Array(32);

  function W(e) {
    for (var n = 0; n < 2 * e; n++) $[n] = Math.floor(16 * Math.random()) + 48, $[n] >= 58 && ($[n] += 39);
    return String.fromCharCode.apply(null, $.slice(0, 2 * e))
  }

  function X() {
    return W(16)
  }

  function G() {
    return W(8)
  }

  function Y(e, n) {
    return "".concat(e, "-").concat(n, "-1")
  }

  function K(e, n) {
    return "00-".concat(e, "-").concat(n, "-01")
  }

  function V(e, n) {
    return "".concat(e, ":").concat(n, ":0:1")
  }
  var z = {
    b3: {
      name: "b3",
      getter: Y
    },
    traceparent: {
      name: "traceparent",
      getter: K
    },
    uber: {
      name: "uber-trace-id",
      getter: V
    }
  };

  function J(e, n, t) {
    null != e && Object.keys(e).map((function(r) {
      null == t || "" == t ? n[r] = e[r] : n["".concat(t, ".").concat(r)] = e[r]
    }))
  }
  var Z = Object.defineProperty,
    Q = Object.defineProperties,
    ee = Object.getOwnPropertyDescriptors,
    ne = Object.getOwnPropertySymbols,
    te = Object.prototype.hasOwnProperty,
    re = Object.prototype.propertyIsEnumerable,
    oe = function(e, n, t) {
      return n in e ? Z(e, n, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: t
      }) : e[n] = t
    },
    ie = function(e, n) {
      for (var t in n || (n = {})) te.call(n, t) && oe(e, t, n[t]);
      if (ne) {
        var o, i = r(ne(n));
        try {
          for (i.s(); !(o = i.n()).done;) t = o.value, re.call(n, t) && oe(e, t, n[t])
        } catch (e) {
          i.e(e)
        } finally {
          i.f()
        }
      }
      return e
    };

  function ce() {
    return {
      name: u.InternalPlugin.BASE_TRANSFORM,
      run: function() {
        var e = this;
        this.subscribe("*", (function(n, t) {
          var r, o, i, c, a = n.otBase,
            s = n.extra;
          a.service = e.options.service, a.attribute = (i = ie(ie({}, null != (r = e.options.attribute) ? r : {}), a.attribute), c = {
            sid: e.session.getSessionId(),
            pid: e.session.getPageId(),
            uid: e.options.uid
          }, Q(i, ee(c))), e.options.nickname && (a.attribute.nickname = e.options.nickname), J(e.options.custom, a.attribute, "custom"), a.resource = ie(ie({}, null != (o = e.options.resource) ? o : {}), a.resource), J({
            "uem.sdk.version": "0.3.8",
            workspace: e.options.workspace,
            "deployment.environment": e.options.env
          }, a.resource), e.options.namespace && (a.resource["service.namespace"] = e.options.namespace), e.options.version && (a.resource["service.version"] = e.options.version);
          var l = {
            otBase: a,
            extra: s
          };
          u.InternalPlugin.BASE_TRANSFORM, t(l)
        }), u.InternalPluginPriority[u.InternalPlugin.BASE_TRANSFORM])
      }
    }
  }
  var ae = function() {
      function e(n) {
        o(this, e), this.subscribeMap = {}, this.allNotifiers = [], this.allNotifiers = n
      }
      return i(e, [{
        key: "subscribeOne",
        value: function(e, n, t, r) {
          var o, i = null != (o = this.subscribeMap[n]) ? o : [];
          this.subscribeMap[n] = i, i.push({
            name: e,
            priority: r,
            callback: t
          }), i.sort((function(e, n) {
            return e.priority - n.priority
          }))
        }
      }, {
        key: "notify",
        value: function(e, n) {
          var t, r = null != (t = this.subscribeMap[e]) ? t : [];
          ! function e(n, t, r) {
            if (!(t < 0)) {
              var o = function(r) {
                  e(n, t - 1, r)
                },
                i = n[t];
              w((function() {
                i.callback.call(void 0, r, o)
              }), "plugin notify run error")()
            }
          }(r, r.length - 1, n)
        }
      }, {
        key: "subscribe",
        value: function(e, n, t, r) {
          if ("*" === n)
            for (var o = 0; o < this.allNotifiers.length; o++) {
              var i = this.allNotifiers[o];
              e !== i && this.subscribeOne(e, i, t, r)
            } else this.subscribeOne(e, n, t, r)
        }
      }, {
        key: "getSubscribeMap",
        value: function() {
          return this.subscribeMap
        }
      }]), e
    }(),
    ue = Object.defineProperty,
    se = Object.defineProperties,
    le = Object.getOwnPropertyDescriptors,
    fe = Object.getOwnPropertySymbols,
    pe = Object.prototype.hasOwnProperty,
    de = Object.prototype.propertyIsEnumerable,
    ve = function(e, n, t) {
      return n in e ? ue(e, n, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: t
      }) : e[n] = t
    },
    he = ["uid", "nickname", "env", "service", "version", "custom", "namespace"],
    be = function() {
      function e(n) {
        var t, r, i, c;
        o(this, e), this.isInit = !1, this.pendingPlugins = [], this.pluginMap = {}, this.session = (t = X(), r = G(), {
          getSessionId: function() {
            return t
          },
          getPageId: function() {
            return r
          },
          refreshPageId: function() {
            r = G()
          }
        }), this.options = n, this.use({
          name: u.InternalPlugin.LOG_SEND,
          run: function() {}
        }), this.use(ce()), this.options.env = null != (i = n.env) ? i : "prod", this.options.version = null != (c = n.version) ? c : "-"
      }
      return i(e, [{
        key: "initPlugin",
        value: function(e) {
          var n = this;
          if (null != e && "object" === c(e))
            if (null != e.name && "" !== e.name)
              if (this.pluginMap[e.name]) console.error("plugin name: ".concat(e.name, " is conflict"));
              else if ("function" == typeof e.run) {
            var t = function(e, n) {
              return se(e, le(n))
            }(function(e, n) {
              for (var t in n || (n = {})) pe.call(n, t) && ve(e, t, n[t]);
              if (fe) {
                var o, i = r(fe(n));
                try {
                  for (i.s(); !(o = i.n()).done;) t = o.value, de.call(n, t) && ve(e, t, n[t])
                } catch (e) {
                  i.e(e)
                } finally {
                  i.f()
                }
              }
              return e
            }({}, this.context), {
              subscribe: function(t, r, o) {
                n.sub.subscribe(e.name, t, r, o)
              },
              notify: function(t) {
                n.sub.notify(e.name, t)
              }
            });
            w((function() {
              return e.run.call(t)
            }), "plugin ".concat(e.name, " init failed"))()
          } else console.error("plugin.run is not a function");
          else console.error("plugin name is required.");
          else console.error("plugin is not a object")
        }
      }, {
        key: "addLog",
        value: function(e) {
          if (this.isInit) {
            var n = {
              t: u.WebEventType.LOG
            };
            J(e, n, u.WebEventType.LOG);
            var t = {
              start: 1e3 * T(),
              attribute: n,
              resource: {}
            };
            this.sub.notify(u.InternalPlugin.LOG_SEND, {
              otBase: t,
              extra: {}
            })
          } else console.error("log should call after start")
        }
      }, {
        key: "setOptions",
        value: function(e) {
          var n = this;
          this.isInit ? he.forEach((function(t) {
            e[t] !== n.options[t] && null != e[t] && "" != e[t] && (n.options[t] = e[t], "uid" === t && "function" == typeof n.setLocalStorage && n.setLocalStorage(u.SLS_TRACE_UID_KEY, e[t]))
          })) : console.error("setOptions should call after start")
        }
      }, {
        key: "start",
        value: function() {
          if (!this.isInit) {
            this.isInit = !0;
            var e = this.options.uid;
            null != e && "" !== e || ("function" == typeof this.getLocalStorage && (e = this.getLocalStorage(u.SLS_TRACE_UID_KEY)), null != e && "" !== e || (e = x())), this.options.uid = e, "function" == typeof this.setLocalStorage && this.setLocalStorage(u.SLS_TRACE_UID_KEY, e);
            var n = this.pendingPlugins.map((function(e) {
              return null == e ? void 0 : e.name
            })).filter((function(e) {
              return null != e
            }));
            this.sub = new ae(n), this.context = {
              options: this.options,
              session: this.session
            };
            var t, o = r(this.pendingPlugins);
            try {
              for (o.s(); !(t = o.n()).done;) {
                var i = t.value;
                this.initPlugin(i)
              }
            } catch (e) {
              o.e(e)
            } finally {
              o.f()
            }
          }
        }
      }, {
        key: "use",
        value: function(e) {
          this.isInit ? console.error("plugin: ".concat(null == e ? void 0 : e.name, " use should run before start")) : this.pendingPlugins.push(e)
        }
      }]), e
    }(),
    me = Object.defineProperty,
    ge = Object.getOwnPropertySymbols,
    ye = Object.prototype.hasOwnProperty,
    Oe = Object.prototype.propertyIsEnumerable,
    Se = function(e, n, t) {
      return n in e ? me(e, n, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: t
      }) : e[n] = t
    },
    ke = function() {
      function e() {
        o(this, e), this.dep = new Map
      }
      return i(e, [{
        key: "subscribe",
        value: function(e, n) {
          var t = this.dep.get(e);
          t ? this.dep.set(e, t.concat(n)) : this.dep.set(e, [n])
        }
      }, {
        key: "notify",
        value: function(e, n) {
          var t = this.dep.get(e);
          e && t && t.forEach((function(e) {
            try {
              e(n)
            } catch (e) {
              console.error(e)
            }
          }))
        }
      }]), e
    }();
  a.ONE_DAY = 864e5, a.ONE_HOUR = 36e5, a.ONE_KILO_BYTE = 1024, a.ONE_MINUTE = 6e4, a.ONE_SECOND = 1e3, a.ONE_YEAR = 31536e6, a.Observable = l, a.SDK_VERSION = "0.3.8", a.SLSClient = be, a.Subscribe = ke, a.assembleOTAttribute = J, a.buildHttpInfo = function(e, n, t, o) {
    var i = {
      url: e,
      method: n,
      status_code: t
    };
    return null == o ? i : function(e, n) {
      return v(e, h(n))
    }(function(e, n) {
      for (var t in n || (n = {})) m.call(n, t) && y(e, t, n[t]);
      if (b) {
        var o, i = r(b(n));
        try {
          for (i.s(); !(o = i.n()).done;) t = o.value, g.call(n, t) && y(e, t, n[t])
        } catch (e) {
          i.e(e)
        } finally {
          i.f()
        }
      }
      return e
    }({}, i), {
      host: o.host,
      scheme: o.protocol
    })
  }, a.callMonitored = f, a.catchUserErrors = w, a.clocksNow = function() {
    return {
      relative: R(),
      timeStamp: T()
    }
  }, a.clocksOrigin = function() {
    return {
      relative: 0,
      timeStamp: L()
    }
  }, a.computeStackTrace = N, a.createHandlingStack = function() {
    var e, n = new Error;
    return f((function() {
      var t = N(n);
      (null == t ? void 0 : t.stack) && (t.stack = q(t.stack)), e = B(t)
    })), e
  }, a.currentDrift = function() {
    return Math.round(Date.now() - (L() + performance.now()))
  }, a.debugLog = p, a.defineGlobal = function(e, n, t, r) {
    r ? r((function() {
      s(e, n, t)
    })) : s(e, n, t)
  }, a.defineGlobalValue = s, a.elapsed = function(e, n) {
    return n - e
  }, a.formatErrorMessage = F, a.formatUnknownError = function(e, n, t) {
    var r, o = !(arguments.length > 3 && void 0 !== arguments[3]) || arguments[3];
    if (!e || void 0 === e.message && !(n instanceof Error)) return {
      message: "".concat(t, " ").concat(JSON.stringify(n)),
      stacktrace: "",
      type: e && e.name,
      id: k()
    };
    (null == e ? void 0 : e.stack) && o && (e.stack = q(e.stack));
    var i = null == (r = null == e ? void 0 : e.stack) ? void 0 : r[0];
    return {
      message: encodeURIComponent(e.message || "Empty message"),
      stacktrace: B(e),
      type: e.name,
      col: i ? i.column : void 0,
      line: i ? i.line : void 0,
      file: encodeURIComponent(i && i.url ? i.url : ""),
      id: k()
    }
  }, a.genUid = x, a.generateSpanId = G, a.generateTraceId = X, a.generateUUID = k, a.getB3Header = Y, a.getConsoleLogStatus = function() {
    return !1
  }, a.getCorrectedTimeStamp = _, a.getInternalUrl = j, a.getJaegerTraceHeaderr = V, a.getRelativeTime = function(e) {
    return e - L()
  }, a.getTimeStamp = A, a.getW3CTraceHeader = K, a.includes = function(e, n) {
    return -1 !== e.indexOf(n)
  }, a.instrumentMethod = H, a.instrumentMethodAndCallOriginal = C, a.isError = function(e) {
    return "[object Error]" === Object.prototype.toString.call(e)
  }, a.isInternalUrl = function(e, n) {
    return e.includes("/track?APIVersion=0.6.0") || e === j(n)
  }, a.isNumber = O, a.isString = function(e) {
    return "[object String]" === Object.prototype.toString.call(e)
  }, a.isURL = function(e) {
    return "string" == typeof e && E.test(e)
  }, a.looksLikeRelativeTime = function(e) {
    return e < 31536e6
  }, a.makeWebPublicApi = function(e, n) {
    var t, o = {
        current: void 0
      },
      i = [],
      c = {};
    return "function" == typeof n && (c = null != (t = n(o)) ? t : {}),
      function(e, n) {
        for (var t in n || (n = {})) ye.call(n, t) && Se(e, t, n[t]);
        if (ge) {
          var o, i = r(ge(n));
          try {
            for (i.s(); !(o = i.n()).done;) t = o.value, Oe.call(n, t) && Se(e, t, n[t])
          } catch (e) {
            i.e(e)
          } finally {
            i.f()
          }
        }
        return e
      }({
        init: function(n) {
          if (null == o.current) {
            o.current = e(n);
            var t = o.current;
            i.forEach((function(e) {
              t.use(e)
            })), t.start()
          }
        },
        use: function(e) {
          i.push(e)
        },
        addLog: function(e) {
          var n = o.current;
          n && n.addLog(e)
        },
        onReady: function(e) {
          e()
        },
        setOptions: function(e) {
          var n = o.current;
          n && n.setOptions(e)
        }
      }, c)
  }, a.matchCondition = P, a.matchSample = function(e) {
    return 0 !== e && Math.random() <= e
  }, a.matchWithDefault = function(e, n) {
    return null == e ? n : !!e
  }, a.monitor = function(e) {
    return function() {
      return f(e, this, arguments)
    }
  }, a.noop = function() {}, a.parseUri = function(e) {
    var n = function(e) {
      try {
        return decodeURIComponent(e)
      } catch (n) {
        return unescape(e)
      }
    }(e || "").match(E);
    if (null == n) return null;
    var t = (n[3] || "").split(":"),
      r = t.length ? (n[2] || "").replace(/(.*\@)/, "") : n[2];
    return {
      uri: n[0],
      protocol: n[1],
      host: r,
      hostname: n[4],
      port: n[5],
      auth: n[3],
      user: t[0],
      password: t[1],
      path: n[6],
      search: n[7],
      hash: n[8]
    }
  }, a.patchHeaderOnRequest = function(e, n, r, o, i) {
    var c, a = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : "headers",
      u = null != r ? r : {};
    u[a] = null != (c = u[a]) ? c : {}, "Headers" === u[a].constructor.name ? (u[a].append("b3", Y(e, n)), u[a].append("traceparent", K(e, n)), u[a].append("uber-trace-id", V(e, n)), i && u[a].append("X-B3-TraceId", e), i && u[a].append("X-B3-SpanId", n)) : (u[a].b3 = Y(e, n), u[a].traceparent = K(e, n), u[a]["uber-trace-id"] = V(e, n), i && (u[a]["X-B3-TraceId"] = e), i && (u[a]["X-B3-SpanId"] = n));
    try {
      if (o)
        if (o instanceof Function) {
          var s = w(o, "run customTraceHeaders func error.")(e, n);
          if (s)
            for (var l = 0, f = Object.entries(s); l < f.length; l++) {
              var p = t(f[l], 2),
                d = p[0],
                v = p[1];
              "Headers" === u[a].constructor.name ? u[a].append(d, v) : u[a][d] = v
            }
        } else
          for (var h = 0, b = Object.entries(o); h < b.length; h++) {
            var m = t(b[h], 2),
              g = m[0],
              y = m[1];
            z[y] && ("Headers" === u[a].constructor.name ? u[a].append(g, z[y].getter(e, n)) : u[a][g] = z[y].getter(e, n))
          }
    } catch (e) {
      console.error("Failed utilizing customTraceHeaders.", e)
    }
    return u
  }, a.patchHeaderOnXhr = function(e, n, r, o, i) {
    e.setRequestHeader("b3", Y(n, r)), e.setRequestHeader("traceparent", K(n, r)), e.setRequestHeader("uber-trace-id", V(n, r)), i && e.setRequestHeader("X-B3-TraceId", n), i && e.setRequestHeader("X-B3-SpanId", r);
    try {
      if (o)
        if (o instanceof Function) {
          var c = w(o, "run customTraceHeaders func error.")(n, r);
          if (c)
            for (var a = 0, u = Object.entries(c); a < u.length; a++) {
              var s = t(u[a], 2),
                l = s[0],
                f = s[1];
              e.setRequestHeader(l, f)
            }
        } else
          for (var p = 0, d = Object.entries(o); p < d.length; p++) {
            var v = t(d[p], 2),
              h = v[0],
              b = v[1];
            z[b] && e.setRequestHeader(h, z[b].getter(n, r))
          }
    } catch (e) {
      console.error("Failed utilizing customTraceHeaders.", e)
    }
  }, a.relativeNow = R, a.relativeToClocks = function(e) {
    return {
      relative: e,
      timeStamp: _(e)
    }
  }, a.resetNavigationStart = function() {
    I = void 0
  }, a.round = S, a.shouldTrace = function(e, n) {
    return null == n || P(e, n)
  }, a.shouldTrackBody = function(e, n, t) {
    return !1 !== e && (!0 === e || "error" === e && !t || "function" == typeof e && w(e, "call shouldTrackBody failed")(n))
  }, a.startUnhandledErrorCollection = function(e) {
    var n = function(e) {
        return C(window, "onerror", {
          before: function(n, t, r, o, i) {
            var c;
            if (i) c = N(i), e(c, i);
            else {
              var a, u = {
                  url: t,
                  column: o,
                  line: r
                },
                s = n;
              if ("[object String]" === {}.toString.call(n)) {
                var l = D.exec(s);
                l && (a = l[1], s = l[2])
              }
              e(c = {
                name: a,
                message: "string" == typeof s ? s : void 0,
                stack: [u]
              }, n)
            }
          }
        })
      }(e).stop,
      t = function(e) {
        return C(window, "onunhandledrejection", {
          before: function(n) {
            var t = n.reason || "Empty reason",
              r = N(t);
            e(r, t)
          }
        })
      }(e).stop;
    return {
      stop: function() {
        n(), t()
      }
    }
  }, a.throttle = function(e, n) {
    var t = !0;
    return function() {
      if (t) {
        for (var r = arguments.length, o = new Array(r), i = 0; i < r; i++) o[i] = arguments[i];
        e.apply(this, o), t = !1, setTimeout((function() {
          t = !0
        }), n)
      }
    }
  }, a.timeStampNow = T, a.toServerDuration = function(e) {
    return O(e) ? S(1e6 * e, 0) : e
  }, a.toStackTraceString = B
}), (function(e) {
  return n({} [e], e)
})), n(1739784025837));