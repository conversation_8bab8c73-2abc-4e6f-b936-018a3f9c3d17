var t, e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  a = require("../../@babel/runtime/helpers/asyncToGenerator"),
  n = (t = require("../../87624F60549B04BFE10427674BE30D65.js")) && t.__esModule ? t : {
    default: t
  },
  i = require("../../1AA24145549B04BF7CC42942C3240D65.js"),
  r = require("../../A4000F75549B04BFC2666772D6B30D65.js"),
  o = require("../../6F218526549B04BF0947ED2133340D65.js"),
  s = require("../../83F188C3549B04BFE597E0C403C30D65.js");
var d = getApp();
Page({
  data: {
    img: d.globalData.img,
    positionList: [{
      awardTop: "60",
      awardLeft: "56"
    }, {
      awardTop: "60",
      awardLeft: "248"
    }, {
      awardTop: "60",
      awardLeft: "440"
    }, {
      awardTop: "230",
      awardLeft: "440"
    }, {
      awardTop: "404",
      awardLeft: "440"
    }, {
      awardTop: "404",
      awardLeft: "248"
    }, {
      awardTop: "404",
      awardLeft: "56"
    }, {
      awardTop: "230",
      awardLeft: "56"
    }],
    awardList: [],
    isContinue: !1,
    totalEnergy: 2e3,
    priceNum: null,
    priceInfo: {},
    propState: !1,
    propNum: 28,
    activityId: "",
    userInfo: {},
    energyNumber: 0,
    exchangeEnergyNumber: 0,
    giftDetailInfo: "",
    currentJf: "",
    newDrawRaffieInfo: [],
    allThanks: !1
  },
  getGiftInfo: function() {
    var t = this;
    return new Promise((function(e, a) {
      (0, r.getGiftDetails)({
        id: t.data.priceInfo.giftId
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  submitUserInfo: function(t) {
    var e = this;
    console.log("领取实物数据", t), (0, o.loadingOpen)(), (0, r.receiveReward)({
      participationId: this.data.priceData.participationId,
      activityId: this.data.activityId,
      winId: this.data.priceInfo.winId,
      consigneeName: t.detail.userName,
      consigneeMobile: t.detail.userPhone,
      consigneeAddress: t.detail.consigneeAddress
    }).then((function(t) {
      (0, o.loadingClose)(), (0, o.toastModel)("提交成功"), e.setData({
        propState: !1
      })
    }))
  },
  closeProp: function() {
    this.setData({
      propState: !1
    }), 8 != this.data.propNum || this.setData({
      isContinue: !1
    })
  },
  confirmProp: function(t) {
    var i = this;
    if (this.setData({
        propState: !1
      }), 8 == this.data.propNum) {
      if (console.log("dataInfo", t), 1 == t.detail) return void this.startHandle();
      if (10 == t.detail) return console.log("调用举哥抽奖十次接口"), (0, o.loadingOpen)(), this.setData({
        isContinue: !1
      }), void(0, r.participationTen)({
        activityId: this.data.activityId,
        openId: n.default.data.openid,
        userId: n.default.data.userInfo.id,
        token: n.default.data.token
      }).then(function() {
        var t = a(e().mark((function t(a) {
          var n;
          return e().wrap((function(t) {
            for (;;) switch (t.prev = t.next) {
              case 0:
                if ((0, o.loadingClose)(), console.log("participationTen", a), 200 != a.code) {
                  t.next = 12;
                  break
                }
                return console.log("res.data", a.data), a.data.forEach((function(t) {
                  null == t && (t = {
                    winData: null
                  })
                })), n = a.data.every((function(t) {
                  return !t.winData
                })), console.log("是否全部是谢谢参与", n), a.data.forEach((function(t) {
                  t.winData && (t.winData[0].awardsNameNew = t.winData[0].awardsName.replace("积分", "能量"))
                })), i.setData({
                  newDrawRaffieInfo: a.data,
                  propState: !0,
                  propNum: 28,
                  isContinue: !1,
                  priceNum: null,
                  allThanks: n
                }), t.next = 11, (0, s.getUser)();
              case 11:
                i.setCurrentJf();
              case 12:
              case "end":
                return t.stop()
            }
          }), t)
        })));
        return function(e) {
          return t.apply(this, arguments)
        }
      }())
    }
    20 != this.data.propNum ? 21 != this.data.propNum ? console.log("提交数据") : this.setData({
      propState: !0,
      propNum: 15
    }) : this.setData({
      propState: !0,
      propNum: 14
    })
  },
  setCurrentJf: function() {
    this.setData({
      userInfo: n.default.data.userInfo
    }), this.data.userInfo.currentJf > 1e4 ? this.setData({
      currentJf: (this.data.userInfo.currentJf / 1e3).toFixed(0) + "K"
    }) : this.setData({
      currentJf: this.data.userInfo.currentJf
    })
  },
  showRule: function() {
    this.setData({
      propState: !0,
      propNum: 26
    })
  },
  gotoShop: function() {
    wx.reLaunch({
      url: "/pages/EnergyMall/EnergyMall"
    })
  },
  getMoreEnergy: function() {
    wx.navigateTo({
      url: "/pages/TaskCenter/TaskCenter"
    })
  },
  onLoad: function(t) {
    console.log("fetchData", n.default.data)
  },
  getAwardsList: function() {
    var t = this;
    (0, r.getAwards)({
      activityId: this.data.activityId
    }).then((function(e) {
      var a = [];
      e.data.forEach((function(e, n) {
        n < t.data.positionList.length && (console.log("this.data.positionList[index]", t.data.positionList[n]), e.awardTop = t.data.positionList[n].awardTop, e.awardLeft = t.data.positionList[n].awardLeft, e.awardsName = e.awardsName.replace(/积分/g, "能量"), a.push(e))
      })), t.setData({
        awardList: a
      }), console.log("数据", a)
    }))
  },
  getJoinInfoData: function() {
    var t = this;
    (0, r.getJoinInfo)({
      activityId: this.data.activityId
    }).then((function(e) {
      t.setData({
        energyNumber: (e.data || {}).contentStr
      })
    }))
  },
  onReady: function() {
    this.setData({
      propState: !0,
      propNum: 31
    })
  },
  onShow: function() {
    var t = this;
    this.setData({
      userInfo: n.default.data.userInfo
    }), n.default.data.userInfo.currentJf > 1e4 ? this.setData({
      currentJf: (n.default.data.userInfo.currentJf / 1e3).toFixed(0) + "K"
    }) : this.setData({
      currentJf: n.default.data.userInfo.currentJf
    }), (0, o.loadingOpen)(), (0, r.dictionaryItems)({
      code: "interaction-address"
    }).then((function(e) {
      t.setData({
        activityId: e.data[0].itemValue
      }), Promise.all([t.getAwardsList(), t.getJoinInfoData()]).then((function(t) {
        (0, o.loadingClose)()
      })), console.log("activityId", t.data.activityId)
    }))
  },
  showExchangeProp: function() {
    if (!n.default.data.userInfo.mobile) return console.log("用户不存在", n.default.data.userInfo), void this.setData({
      propState: !0,
      propNum: 6
    });
    if (this.data.userInfo.currentJf < this.data.energyNumber) this.setData({
      propState: !0,
      propNum: 7
    });
    else {
      if (this.data.isContinue) return;
      this.setData({
        isContinue: !0,
        propState: !0,
        propNum: 8
      })
    }
  },
  getPrice: function() {
    var t = this;
    return new Promise((function(e, a) {
      (0, r.participationNine)({
        weChatOpenId: n.default.data.openid,
        digitCode: (0, i.newGuid)(),
        activityId: t.data.activityId,
        activityType: 11,
        isNewUser: n.default.data.registerUserInfo.isNewUser,
        gps: "",
        address: "",
        areaCode: "",
        gpsType: 3
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  getPriceLink: function() {
    var t = this;
    return new Promise((function(e, a) {
      (0, r.receiveLinkReward)({
        activityId: t.data.activityId,
        participationId: t.data.priceData.participationId,
        winId: t.data.priceInfo.winId
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  getPriceCoupon: function() {
    var t = this;
    return new Promise((function(e, a) {
      (0, r.receiveCouponReward)({
        activityId: t.data.activityId,
        participationId: t.data.priceData.participationId,
        winId: t.data.priceInfo.winId,
        rechargeAccount: n.default.data.userInfo.mobile
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  startHandle: function() {
    var t = this;
    return a(e().mark((function n() {
      var i, r, d, c, u, p;
      return e().wrap((function(n) {
        for (;;) switch (n.prev = n.next) {
          case 0:
            return console.log("调用抽奖接口"), (0, o.loadingOpen)(), n.next = 4, t.getPrice();
          case 4:
            if (i = n.sent, (0, o.loadingClose)(), console.log("getPriceInfo", i), 200 != i.code) {
              n.next = 15;
              break
            }
            console.log("getPriceInfo", i), t.setData({
              priceData: i.data[0]
            }), r = i.data[0].winData[0], console.log("this.data.awardList", t.data.awardList), t.data.awardList.forEach((function(e, a) {
              r.awardsId == e.id && (t.setData({
                priceNum: a,
                priceInfo: r
              }), console.log("indexindexindexindex", a))
            })), n.next = 34;
            break;
          case 15:
            if (21220103 != i.code && 21220100 != i.code && 21220099 != i.code) {
              n.next = 19;
              break
            }
            t.data.awardList.forEach((function(e, a) {
              1 == e.id && t.setData({
                priceNum: a,
                priceInfo: null
              })
            })), n.next = 34;
            break;
          case 19:
            if (22354036 != i.code) {
              n.next = 25;
              break
            }
            return (0, o.toastModel)("今日抽奖机会已用完，请改日再来"), t.setData({
              isContinue: !1
            }), n.abrupt("return");
          case 25:
            if (22354037 != i.code && 22354038 != i.code && 22354039 != i.code) {
              n.next = 31;
              break
            }
            return (0, o.toastModel)("没有抽奖机会了"), t.setData({
              isContinue: !1
            }), n.abrupt("return");
          case 31:
            return t.setData({
              isContinue: !1
            }), (0, o.toastModel)(i.message.replace(/积分/g, "能量")), n.abrupt("return");
          case 34:
            console.log("priceInfo", t.data.priceInfo), d = t, c = 0, u = 0, p = setInterval(a(e().mark((function a() {
              var n, i, r;
              return e().wrap((function(e) {
                for (;;) switch (e.prev = e.next) {
                  case 0:
                    if (c++, c %= 8, d.setData({
                        selectedIdx: c
                      }), !((u += 200) >= 200 * d.data.priceNum + 9600)) {
                      e.next = 51;
                      break
                    }
                    return clearInterval(p), e.next = 8, (0, s.getUser)();
                  case 8:
                    if (d.setCurrentJf(), d.data.priceInfo) {
                      e.next = 13;
                      break
                    }
                    d.setData({
                      propState: !0,
                      propNum: 18,
                      isContinue: !1
                    }), e.next = 50;
                    break;
                  case 13:
                    if (t.setData({
                        isContinue: !1
                      }), 5 != d.data.priceInfo.giftSendType && 6 != d.data.priceInfo.giftSendType && 7 != d.data.priceInfo.giftSendType) {
                      e.next = 22;
                      break
                    }
                    return e.next = 17, d.getPriceLink();
                  case 17:
                    if (n = e.sent, console.log("priceLinkInfo", n), 200 == n.code) {
                      e.next = 22;
                      break
                    }
                    return (0, o.toastModel)(n.message), e.abrupt("return");
                  case 22:
                    if (4 != d.data.priceInfo.giftType) {
                      e.next = 26;
                      break
                    }
                    d.setData({
                      propState: !0,
                      propNum: 24,
                      exchangeEnergyNumber: d.data.priceInfo.giftUnitScore * d.data.priceInfo.giftCount
                    }), e.next = 50;
                    break;
                  case 26:
                    if (1 != d.data.priceInfo.giftType || 7 != d.data.priceInfo.giftSendType) {
                      e.next = 30;
                      break
                    }
                    d.setData({
                      propState: !0,
                      propNum: 19
                    }), e.next = 50;
                    break;
                  case 30:
                    if (1 != d.data.priceInfo.giftType || 6 != d.data.priceInfo.giftSendType) {
                      e.next = 34;
                      break
                    }
                    d.setData({
                      propState: !0,
                      propNum: 20
                    }), e.next = 50;
                    break;
                  case 34:
                    if (1 != d.data.priceInfo.giftType || 4 != d.data.priceInfo.giftSendType) {
                      e.next = 41;
                      break
                    }
                    return e.next = 37, t.getGiftInfo();
                  case 37:
                    i = e.sent, t.setData({
                      propState: !0,
                      propNum: 16,
                      giftDetailInfo: i.data
                    }), e.next = 50;
                    break;
                  case 41:
                    if (1 != d.data.priceInfo.giftType || 5 != d.data.priceInfo.giftSendType) {
                      e.next = 49;
                      break
                    }
                    return e.next = 44, t.getGiftInfo();
                  case 44:
                    r = e.sent, console.log("giftDetailInfo", r, r.data), t.setData({
                      propState: !0,
                      propNum: 9,
                      giftDetailInfo: r.data
                    }), e.next = 50;
                    break;
                  case 49:
                    3 == d.data.priceInfo.giftType && d.setData({
                      propState: !0,
                      propNum: 17
                    });
                  case 50:
                    setTimeout((function() {
                      t.setData({
                        isContinue: !1
                      })
                    }), 1e3);
                  case 51:
                  case "end":
                    return e.stop()
                }
              }), a)
            }))), 50);
          case 39:
          case "end":
            return n.stop()
        }
      }), n)
    })))()
  },
  onShareAppMessage: function() {}
});