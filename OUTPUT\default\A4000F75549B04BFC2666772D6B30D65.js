Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.JfRecordPage = function(e) {
  return (0, t.request)({
    url: "marketing/v1/cus/Jf-record-page",
    method: "GET",
    data: e
  })
}, exports.appliedList = function(e) {
  return (0, t.request)({
    url: "customer/wildbrain/instituteMini/applied/list",
    method: "get",
    data: e
  })
}, exports.applyInfo = function(e) {
  return (0, t.request)({
    url: "customer/wildbrain/instituteMini/apply/info",
    method: "get",
    data: e
  })
}, exports.begin = function(e) {
  return (0, t.request)({
    url: "hivezs/wildbrain/v1/browseRewards/begin",
    method: "POST",
    data: e
  })
}, exports.canClock = function(e) {
  return (0, t.request)({
    url: "marketing/v1/wildbrain/store-clock/client/can-clock",
    method: "GET",
    data: e,
    enterpriseNo: !0
  })
}, exports.cancel = function(e) {
  return (0, t.request)({
    url: "hivezs/wildbrain/v1/browseRewards/cancel",
    method: "POST",
    data: e
  })
}, exports.client = function(e) {
  return (0, t.request)({
    url: "marketing/v1/wildbrain/store-clock/client",
    method: "POST",
    data: e,
    enterpriseNo: !0,
    separateMsg: !0
  })
}, exports.configQuery = function(e) {
  return (0, t.request)({
    url: "marketing/v1/wildbrain/store-clock/client/config/query",
    method: "GET",
    data: e
  })
}, exports.confirmReceipt = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5MallOrder/confirmReceipt",
    method: "PUT",
    data: e
  })
}, exports.convertGps = function(e) {
  return (0, t.request)({
    url: "cncop/area/convert-gps",
    method: "GET",
    data: e,
    enterpriseNo: !0
  })
}, exports.deleteDefaultAddress = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5-customer/delete-default-address",
    method: "DELETE",
    data: e
  })
}, exports.deleteOrder = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5MallOrder/deleteOrder",
    method: "PUT",
    data: e
  })
}, exports.dictionaryItems = function(e) {
  return (0, t.request)({
    url: "tt/basic/v1/info/items",
    method: "get",
    data: e
  })
}, exports.end = function(e) {
  return (0, t.request)({
    url: "hivezs/wildbrain/v1/browseRewards/end",
    method: "POST",
    data: e
  })
}, exports.fixSignUp = function(e) {
  return (0, t.request)({
    url: "marketing/woBeiCus/v1/fixSignUp",
    method: "post",
    data: e
  })
}, exports.fixSignUpOld = function(e) {
  return (0, t.request)({
    url: "marketing/cusSign/v1/fixSignUp",
    method: "post",
    data: e
  })
}, exports.fullInfo = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5-customer/full-info",
    method: "put",
    data: e
  })
}, exports.getAnti = function(e) {
  return (0, t.request)({
    url: "pms/v1/anti/get-anti",
    method: "get",
    data: e
  })
}, exports.getAwards = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5-marketing-activity/get-awards",
    method: "get",
    data: e
  })
}, exports.getBannerList = function(e) {
  return (0, t.request)({
    url: "marketing/noAuth/WbPersonalConfig/v1/get-banner-list",
    method: "get",
    data: e
  })
}, exports.getBrandList = function(e) {
  return (0, t.request)({
    url: "marketing/noAuth/WbPersonalConfig/v1/get-brand-list",
    method: "get",
    data: e
  })
}, exports.getBrowseConfig = function(e) {
  return (0, t.request)({
    url: "hivezs/wildbrain/v1/browseRewards/getBrowseConfig",
    method: "POST",
    data: e
  })
}, exports.getCardPassword = function(e) {
  return (0, t.request)({
    url: "marketing/v1/cus/get-card-password",
    method: "GET",
    data: e,
    separateMsg: !0
  })
}, exports.getCompleteFlag = function(e) {
  return (0, t.request)({
    url: "marketing/v1/cus/wild-brain-get-user-complete-flag",
    method: "GET",
    data: e,
    separateMsg: !0
  })
}, exports.getComponentConfig = function(e) {
  return (0, t.request)({
    url: "marketing/h5container/v1/template/get-component-config",
    method: "get",
    data: e
  })
}, exports.getGiftDetail = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5cart/get-gift-detail",
    method: "get",
    data: e
  })
}, exports.getGiftDetails = function(e) {
  return (0, t.request)({
    url: "marketing/v1/cus/get-gift-details",
    method: "GET",
    data: e
  })
}, exports.getGiftList = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5gift/get-gift-list",
    method: "get",
    data: e
  })
}, exports.getGroupList = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5gift/get-group-list",
    method: "get",
    data: e
  })
}, exports.getInviteConfig = function(e) {
  return (0, t.request)({
    url: "hivezs/wildbrain/v1/h5Invite/getInviteConfig",
    method: "POST",
    data: e
  })
}, exports.getJoinInfo = function(e) {
  return (0, t.request)({
    url: "marketing/v1/content-cus-api/get-join-info",
    method: "get",
    data: e
  })
}, exports.getOrderInfo = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5MallOrder/getOrderInfo",
    method: "get",
    data: e
  })
}, exports.getOrderList = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5MallOrder/getOrderList",
    method: "get",
    data: e
  })
}, exports.getOrderStatus = function(e) {
  return (0, t.request)({
    url: "hivezs/wildbrain/v1/platform/getOrderStatus",
    method: "get",
    data: e
  })
}, exports.getProductList = function(e) {
  return (0, t.request)({
    url: "marketing/noAuth/WbPersonalConfig/v1/get-show-product-list",
    method: "get",
    data: e
  })
}, exports.getRewardDetails = function(e) {
  return (0, t.request)({
    url: "marketing/v1/cus/get-reward-details",
    method: "GET",
    data: e
  })
}, exports.getRewordSign = function(e) {
  return (0, t.request)({
    url: "marketing/v1/client-sign/wild-brain-sign-record",
    method: "GET",
    data: e,
    separateMsg: !0
  })
}, exports.getSignConfig = function(e) {
  return (0, t.request)({
    url: "marketing/cusSign/v1/getSignConfig",
    method: "GET",
    data: e
  })
}, exports.getTemplate = function(e) {
  return (0, t.request)({
    url: "marketing/H5Template/v1/getTemplate",
    method: "post",
    data: e
  })
}, exports.getUserAddressList = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5-customer/get-user-address-list",
    method: "get",
    data: e
  })
}, exports.getUserInfo = function(e) {
  return (0, t.request)({
    url: "marketing/shyx/marketing/v1/cus/get-user-info",
    method: "get",
    data: e
  })
}, exports.getUserSignInfo = function(e) {
  return (0, t.request)({
    url: "marketing/cusSign/v1/getUserSignInfo",
    method: "get",
    data: e
  })
}, exports.getUserTaskList = function(e) {
  return (0, t.request)({
    url: "marketing/sign/marketing/v1/cus/get-user-task-list",
    method: "GET",
    data: e
  })
}, exports.getUsercompleteFlag = function(e) {
  return (0, t.request)({
    url: "marketing/v1/cus/get-user-complete-flag",
    method: "get",
    data: e
  })
}, exports.getVideoList = function(e) {
  return (0, t.request)({
    url: "marketing/noAuth/WbPersonalConfig/v1/get-video-list",
    method: "get",
    data: e
  })
}, exports.getVirtualGiftCard = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5-customer/getVirtualGiftCard",
    method: "get",
    data: e
  })
}, exports.getVirtualGiftCardList = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5MallOrder/getVirtualGiftCardList",
    method: "get",
    data: e
  })
}, exports.h5UserRecordCheck = function(e) {
  return (0, t.request)({
    url: "customer/wonderlab/v1/h5UserRecord/check",
    method: "get",
    data: e
  })
}, exports.instituteMiniApply = function(e) {
  return (0, t.request)({
    url: "customer/wildbrain/instituteMini/apply",
    method: "POST",
    data: e
  })
}, exports.inviteGrant = function(e) {
  return (0, t.request)({
    url: "hivezs/wildbrain/v1/h5Invite/inviteGrant",
    method: "POST",
    data: e,
    separateMsg: !0
  })
}, exports.justSign = function(e) {
  return (0, t.request)({
    url: "marketing/sign/woBeiCus/v1/justSign",
    method: "post",
    data: e
  })
}, exports.justSignOld = function(e) {
  return (0, t.request)({
    url: "marketing/cusSign/v1/justSign",
    method: "post",
    data: e
  })
}, exports.miniappGetPhoneNumber = function(e) {
  return (0, t.request)({
    isUrl: !0,
    url: "https://restapi.supercarrier8.com/shyx/third-interface/v1/wechat/getPhoneNumberWithSign",
    method: "get",
    data: e
  })
}, exports.miniappLogin = function(e) {
  return (0, t.request)({
    url: "marketing/v1/wechat-user-auth/miniapp-login",
    method: "get",
    data: e
  })
}, exports.participation = function(e) {
  return (0, t.request)({
    url: "marketing/participationActivity/v1/participation",
    method: "post",
    data: e
  })
}, exports.participationNine = function(e) {
  return (0, t.request)({
    url: "marketing/sign/interactionActivity/v1/participation",
    method: "post",
    data: e,
    separateMsg: !0
  })
}, exports.participationTen = function(e) {
  return (0, t.request)({
    url: "hivezs/wildbrain/v1/h5Draw/participation",
    method: "POST",
    data: e
  })
}, exports.payJf = function(e) {
  return (0, t.request)({
    url: "marketing/v1/buy-gift/pay-jf",
    method: "PUT",
    data: e
  })
}, exports.receiveCouponReward = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5-customer-winrecord/receive-coupon-reward",
    method: "GET",
    data: e,
    separateMsg: !0
  })
}, exports.receiveLinkReward = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5-customer-winrecord/receive-virtual-link-reward",
    method: "GET",
    data: e,
    separateMsg: !0
  })
}, exports.receiveReward = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5-customer-winrecord/receive-logistics-reward",
    method: "POST",
    data: e
  })
}, exports.recordPage = function(e) {
  return (0, t.request)({
    url: "marketing/v1/wildbrain/store-clock/client/record/page",
    method: "GET",
    data: e
  })
}, exports.saveUserAddress = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5-customer/save-user-address",
    method: "post",
    data: e
  })
}, exports.selectList = function(e) {
  return (0, t.request)({
    url: "cncop/area/select-list",
    method: "get",
    data: e
  })
}, exports.setMessage = function(e) {
  return (0, t.request)({
    url: "hivezs/wildbrain/v1/min-app/subscribe-msg?openId=".concat(e.openId),
    method: "POST",
    data: e
  })
}, exports.setTaskOk = function(e) {
  return (0, t.request)({
    url: "marketing/sign/marketing/v1/cus/set-task-ok",
    method: "POST",
    data: e
  })
}, exports.signCallback = function(e) {
  return (0, t.request)({
    url: "marketing/v1/client-sign/sign-callback",
    method: "GET",
    data: e,
    separateMsg: !0
  })
}, exports.storeList = function(e) {
  return (0, t.request)({
    url: "marketing/v1/wildbrain/store-clock/client/store-list",
    method: "GET",
    data: e,
    enterpriseNo: !0
  })
}, exports.storeListAll = function(e) {
  return (0, t.request)({
    url: "marketing/v1/wildbrain/store-clock/client/store-list/all",
    method: "GET",
    data: e,
    enterpriseNo: !0
  })
}, exports.taskComplete = function(e) {
  return (0, t.request)({
    url: "marketing/sign/marketing/v1/cus/task-complete",
    method: "POST",
    data: e,
    separateMsg: !0
  })
}, exports.underWayList = function(e) {
  return (0, t.request)({
    url: "customer/wildbrain/instituteMini/underWay/list",
    method: "get",
    data: e
  })
}, exports.updateHeadImage = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5-customer/update-headImage",
    method: "PUT",
    data: e
  })
}, exports.updateName = function(e) {
  return (0, t.request)({
    url: "marketing/v1/h5-customer/update-name",
    method: "PUT",
    data: e
  })
}, exports.wechatOpenid = function(e) {
  return (0, t.request)({
    url: "marketing/v1/customer-login/wechat-openid",
    method: "post",
    data: e
  })
};
var t = require("444320E7549B04BF222548E0E6A30D65.js");