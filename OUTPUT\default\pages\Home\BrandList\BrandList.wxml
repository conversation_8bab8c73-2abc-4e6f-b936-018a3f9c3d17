<view class="ProList">
    <block wx:if="{{list.length!=0}}">
        <view bindtap="gotoWebUrl" class="ProList_item" data-item="{{item}}" wx:for="{{list}}" wx:key="index">
            <view class="ProList_item_top">
                <image mode="" src="{{item.mainUrl}}"></image>
            </view>
            <view class="ProList_item_bot">
                <view class="ProList_item_bot_text">{{item.activityName}}</view>
            </view>
        </view>
    </block>
    <view class="pageNoList" wx:else>
        <view class="pageNoList_img">
            <image mode="" src="{{img}}noListIcon.png"></image>
        </view>
        <view class="pageNoList_text">即将上线，敬请期待</view>
    </view>
    <footer class="footer"></footer>
</view>
