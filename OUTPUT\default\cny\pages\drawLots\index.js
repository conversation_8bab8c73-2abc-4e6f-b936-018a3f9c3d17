var e = require("../../../@babel/runtime/helpers/toConsumableArray"),
  t = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  n = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  r = require("../../../71D07D80549B04BF17B615870C540D65.js"),
  a = require("../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  o = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  s = require("../../../A5622344549B04BFC3044B435F450D65.js"),
  i = !0;
Page({
  data: {
    statusHeaderBarHeight: o.statusHeaderBarHeight,
    imgUrl: o.imgUrl,
    imgVersion: o.imgVersion,
    screenInfo: (0, a.getScreenInfo)(),
    interval: null,
    lotsList: [{
      id: 1,
      img: o.imgUrl + "drawLots/t1.png?v=" + o.imgVersion,
      active: !1
    }, {
      id: 2,
      img: o.imgUrl + "drawLots/t2.png?v=" + o.imgVersion,
      active: !1
    }, {
      id: 3,
      img: o.imgUrl + "drawLots/t3.png?v=" + o.imgVersion,
      active: !1
    }],
    isRedCoverFlag: !1,
    userInfo: {}
  },
  onLoad: function(e) {
    this.getUserInfoFn(), s.pageView({
      refer_page_type: "首页",
      refer_page_name: "首页",
      page_type: "抽签页面",
      page_name: "抽签页面",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/drawLots/index"
    })
  },
  onReady: function() {},
  onShow: function() {
    this.data.isRedCoverFlag && this.selectComponent("#congratulationsComponents").changeCurrentState("back")
  },
  getUserInfoFn: function() {
    var e = this;
    return n(t().mark((function n() {
      var o, s, i;
      return t().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return t.prev = 0, t.next = 3, (0, r.getUserInfo)();
          case 3:
            o = t.sent, s = o.code, i = o.data, 200 === s && ((0, a.setStorageSync)("userInfo", i.userInfo), e.setData({
              userInfo: i.userInfo
            })), t.next = 12;
            break;
          case 9:
            t.prev = 9, t.t0 = t.catch(0), console.log("task userInfo error: ", t.t0);
          case 12:
          case "end":
            return t.stop()
        }
      }), n, null, [
        [0, 9]
      ])
    })))()
  },
  getRedCoverFn: function(e) {
    var a = this;
    return n(t().mark((function n() {
      var o, s, i;
      return t().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return t.prev = 0, t.next = 3, (0, r.getRedCover)();
          case 3:
            o = t.sent, s = o.code, i = o.data, o.msg, 200 == s ? a.selectComponent("#congratulationsComponents").openMask(i.RedCoverType, i.RedCoverUrl, i.Coupon) : 10008 == s ? a.selectComponent("#sorryComponents").openMask() : 10009 == s && a.selectComponent("#unlockAllComponents").openMask(), a.getUserInfoFn(), e && e(), t.next = 15;
            break;
          case 12:
            t.prev = 12, t.t0 = t.catch(0), console.log("getRedCover error: ", t.t0);
          case 15:
          case "end":
            return t.stop()
        }
      }), n, null, [
        [0, 12]
      ])
    })))()
  },
  startDraw: function() {
    var e = this;
    return n(t().mark((function n() {
      return t().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            if (!(e.data.userInfo.drawNub <= 0)) {
              t.next = 3;
              break
            }
            return t.abrupt("return", e.selectComponent("#regretComponents").openMask());
          case 3:
            i && (i = !1, e.setData({
              interval: setTimeout((function() {
                e.getRedCoverFn((function() {
                  e.moveForward(), e.stopDraw(), i = !0
                }))
              }), 1500)
            })), s.pageClickEvent({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "抽签页面",
              page_name: "抽签页面",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/drawLots/index",
              button_name: "抽签-开始抽签"
            });
          case 5:
          case "end":
            return t.stop()
        }
      }), n)
    })))()
  },
  stopDraw: function() {
    clearTimeout(this.data.interval), this.setData({
      interval: null
    })
  },
  moveForward: function() {
    var t = this.data.lotsList,
      n = e(t);
    n.forEach((function(e) {
      e.active = !1
    }));
    var r = n.shift();
    n.push(r), n[1].active = !0, n[0] && (n[0].active = !1), this.setData({
      lotsList: n
    })
  },
  handleIsGet: function(e) {
    var t = e.detail.flag;
    t && this.setData({
      isRedCoverFlag: t
    })
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return o.shareOptions
  }
});