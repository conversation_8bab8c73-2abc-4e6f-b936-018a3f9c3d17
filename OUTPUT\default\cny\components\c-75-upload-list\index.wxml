<scroll-view enhanced bindscrolltolower="bindscrolltolower" class="scroll-view-box" scrollY="true" showScrollbar="{{false}}">
    <view catch:tap="handleGoDetail" class="upload-list-box" data-id="{{item.id}}" data-info="{{item}}" data-status="{{item.status}}" wx:for="{{dataList}}" wx:key="index">
        <image binderror="" bindload="" class="box_bg" lazyLoad="true" src="{{imgUrl}}75/record/box_bg.png?v={{imgVersion}}"></image>
        <view class="list-box">
            <view class="showImgBox">
                <image binderror="" bindload="" class="showImg" lazyLoad="true" mode="aspectFill" src="{{item.photo}}"></image>
            </view>
            <view class="list-title SourceHanSerifCN-Bold">{{item.title}}</view>
            <view class="operation_box">
                <view class="list-header-box">
                    <image binderror="" bindload="" class="list-header-img" lazyLoad="true" src="{{item.avatar?item.avatar:'https://dm-assets.supercarrier8.com/wobei/banner1s.png'}}"></image>
                </view>
                <view class="o_box">
                    <image binderror="" bindload="" class="o_bg_img" lazyLoad="true" src="{{imgUrl}}75/record/operation_bg.png?v={{imgVersion}}"></image>
                    <view class="list-name-box SourceHanSerifCN-Bold">{{item.nickname}}</view>
                    <view catch:tap="handleLike" class="list-operation-box" data-story-id="{{item.id}}" data-user-id="{{item.userId}}">
                        <view class="list-operation-number SourceHanSansCN-Medium">{{item.likeNub}}</view>
                        <image binderror="" bindload="" class="list-operation-img" lazyLoad="true" src="{{imgUrl}}75/details/{{item.likeFlag==='1'?'love_select':'love'}}.png?v={{imgVersion}}"></image>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <view class="noMoreData" wx:if="{{noMoreData}}">-- 已经到最底部啦 --</view>
</scroll-view>
