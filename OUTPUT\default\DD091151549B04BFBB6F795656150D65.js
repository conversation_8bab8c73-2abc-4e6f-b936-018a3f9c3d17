var r = require("./@babel/runtime/helpers/toConsumableArray.js");
String.prototype.substr = function(t, i) {
  if (void 0 === t) return this.toString();
  if ("number" != typeof t || "number" != typeof i && void 0 !== i) return "";
  var e = r(this);
  return e.length + t < 0 && (t = 0), void 0 === i || t < 0 && t + i > 0 ? e.slice(t).join("") : e.slice(t, t + i).join("")
}, String.prototype.substring = function(t, i) {
  if (void 0 === t) return this.toString();
  if ("number" != typeof t || "number" != typeof i && void 0 !== i) return "";
  t > 0 || (t = 0), i > 0 || void 0 === i || (i = 0);
  var e = r(this),
    n = e.length;
  if (t > n && (t = n), i > n && (i = n), i < t) {
    var o = [i, t];
    t = o[0], i = o[1]
  }
  return e.slice(t, i).join("")
};