<view class="container fadeIn" hoverClass="none" hoverStopPropagation="false">
    <custom-header :isBack="{{true}}" title="PEANUTS"></custom-header>
    <view class="scroll-view {{screenInfo}}" hoverClass="none" hoverStopPropagation="false" style="top:{{statusHeaderBarHeight}}px">
        <image binderror="" bindload="" class="bg" lazyLoad="true" src="{{imgUrl}}75/record/bg.jpg?v={{imgVersion}}"></image>
        <view class="scroll-view-box">
            <c-75-upload-list bind:likeStory="likeStory" dataList="{{recordList}}" type="record"></c-75-upload-list>
        </view>
    </view>
</view>
