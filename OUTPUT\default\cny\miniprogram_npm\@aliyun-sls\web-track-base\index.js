var t, e, n = require("../../../../@babel/runtime/helpers/toConsumableArray"),
  r = require("../../../../@babel/runtime/helpers/classCallCheck"),
  s = require("../../../../@babel/runtime/helpers/createClass"),
  i = require("../../../../@babel/runtime/helpers/typeof");
module.exports = (t = {}, e = function(e, n) {
  if (!t[e]) return require(n);
  if (!t[e].status) {
    var r = t[e].m;
    r._exports = r._tempexports;
    var s = Object.getOwnPropertyDescriptor(r, "exports");
    s && s.configurable && Object.defineProperty(r, "exports", {
      set: function(t) {
        "object" === i(t) && t !== r._exports && (r._exports.__proto__ = t.__proto__, Object.keys(t).forEach((function(e) {
          r._exports[e] = t[e]
        }))), r._tempexports = t
      },
      get: function() {
        return r._tempexports
      }
    }), t[e].status = 1, t[e].func(t[e].req, r, r.exports)
  }
  return t[e].m.exports
}, function(e, n, r) {
  t[e] = {
    status: 0,
    func: n,
    req: r,
    m: {
      exports: {},
      _tempexports: {}
    }
  }
}(1739784025839, (function(t, e, o) {
  var a = function() {
      function t(e) {
        r(this, t), this.total = 0, this.count = 0, this.peddings = [], this.total = e
      }
      return s(t, [{
        key: "append",
        value: function(t) {
          this.peddings.push(t), this.run()
        }
      }, {
        key: "run",
        value: function() {
          var t = this;
          if (this.peddings.length && !(this.count >= this.total)) {
            var e = this.peddings.shift();
            e && (this.count++, e().finally((function() {
              t.count--, t.run()
            })))
          }
        }
      }]), t
    }(),
    u = function() {
      function t(e) {
        var n, s, i, o = this;
        r(this, t), this.timer = null, this.time = 10, this.count = 10, this.arr = [], this.time = null != (n = e.time) ? n : 10, this.count = null != (s = e.count) ? s : 10, this.ccController = new a(null != (i = e.maxReqCount) ? i : 10), e.host.startsWith("http://") || e.host.startsWith("https://") ? this.url = e.host + "/logstores/" + e.logstore + "/track" : this.url = "https://" + e.project + "." + e.host + "/logstores/" + e.logstore + "/track", this.opt = e, e.installUnloadHook && "function" == typeof e.installUnloadHook && e.installUnloadHook((function() {
          o.sendImmediateInner()
        }))
      }
      return s(t, [{
        key: "assemblePayload",
        value: function(t) {
          var e = {
            __logs__: t
          };
          return this.opt.tags && (e.__tags__ = this.opt.tags), this.opt.topic && (e.__topic__ = this.opt.topic), this.opt.source && (e.__source__ = this.opt.source), JSON.stringify(e)
        }
      }, {
        key: "platformSend",
        value: function() {
          var t = this;
          if (this.opt.sendPayload && "function" == typeof this.opt.sendPayload) {
            var e = this.assemblePayload(this.arr);
            this.ccController.append((function() {
              return t.opt.sendPayload(t.url, e)
            }))
          }
        }
      }, {
        key: "transString",
        value: function(t) {
          var e = {};
          for (var n in t) "object" == i(t[n]) ? e[n] = JSON.stringify(t[n]) : e[n] = String(t[n]);
          return e
        }
      }, {
        key: "sendImmediateInner",
        value: function() {
          this.arr && this.arr.length > 0 && (this.platformSend(), null != this.timer && (clearTimeout(this.timer), this.timer = null), this.arr = [])
        }
      }, {
        key: "sendInner",
        value: function() {
          if (this.timer) this.arr.length >= this.count && (clearTimeout(this.timer), this.timer = null, this.sendImmediateInner());
          else {
            var t = this;
            this.arr.length >= this.count || this.time <= 0 ? this.sendImmediateInner() : this.timer = setTimeout((function() {
              t.sendImmediateInner()
            }), 1e3 * this.time)
          }
        }
      }, {
        key: "send",
        value: function(t) {
          var e = this.transString(t);
          this.arr.push(e), this.sendInner()
        }
      }, {
        key: "sendImmediate",
        value: function(t) {
          var e = this.transString(t);
          this.arr.push(e), this.sendImmediateInner()
        }
      }, {
        key: "sendBatchLogs",
        value: function(t) {
          var e, r = this,
            s = t.map((function(t) {
              return r.transString(t)
            }));
          (e = this.arr).push.apply(e, n(s)), this.sendInner()
        }
      }, {
        key: "sendBatchLogsImmediate",
        value: function(t) {
          var e, r = this,
            s = t.map((function(t) {
              return r.transString(t)
            }));
          (e = this.arr).push.apply(e, n(s)), this.sendImmediateInner()
        }
      }, {
        key: "overwriteTransString",
        value: function(t) {
          this.transString = t.transString
        }
      }, {
        key: "getOpt",
        value: function() {
          return this.opt
        }
      }]), t
    }();
  e.exports = u
}), (function(t) {
  return e({} [t], t)
})), e(1739784025839));