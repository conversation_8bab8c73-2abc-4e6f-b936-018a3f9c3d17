Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0, require("./@babel/runtime/helpers/Arrayincludes.js");
var e = r(require("miniprogram_npm/lodash/index.js")),
  t = r(require("miniprogram_npm/crypto-js/index.js"));

function r(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
var a = {
  getSign: function(r, a, s, n) {
    var i, u = "[sign] getSign";
    a && (a = "/" + (a = e.default.trim(a, "/"))).indexOf("?") > 0 && (i = this.urlParamsToObj(a), a = a.split("?")[0], console.log(u, "[处理后url]", a), i && (s = e.default.assign(i, s)));
    var l = "",
      o = "";
    if (s) {
      var f = e.default.keys(s);
      f && e.default.size(f) > 0 && f.sort(), e.default.forEach(f, (function(t) {
        var r = s[t];
        e.default.includes(["", null, void 0], r) || ("string" != typeof r && ("[object Object]" === Object.prototype.toString.call(r) && e.default.size(r) > 0 && Object.keys(r).forEach((function(e) {
          null === r[e] && delete r[e]
        })), r = JSON.stringify(r)), o += r)
      }))
    }
    return o = this.base64(o), l = "".concat(r.toUpperCase()).concat(a).concat(o).concat(n), l = this.base64(l), l = t.default.MD5(l).toString()
  },
  getSignByReq: function(t) {
    var r = e.default.now() + "",
      a = e.default.get(t, "method").toUpperCase(),
      s = e.default.get(t, "url"),
      n = e.default.get(t, "GET" === a ? "params" : "data");
    console.log("method, url, params, timestamp", a, s, n, r);
    var i = this.getSign(a, s, n, r);
    return t.headers["x-request-ts"] = r, t.headers["x-request-sign"] = i, t
  },
  base64: function(e) {
    if (!e) return "";
    var r = "string" == typeof e ? e : JSON.stringify(e);
    return r = t.default.enc.Utf8.parse(r), r = t.default.enc.Base64.stringify(r)
  },
  urlParamsToObj: function(t) {
    var r = {};
    try {
      if (t) {
        var a = t.split("?")[1].split("&");
        a && a.length > 0 && e.default.forEach(a, (function(e) {
          var t = e.split("=")[0],
            a = e.split("=")[1];
          t && (r[t] = a)
        }))
      }
    } catch (e) {
      r = {}
    }
    return r
  }
};
exports.default = a;