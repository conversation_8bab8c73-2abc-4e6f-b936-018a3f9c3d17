<view class="empower">
    <block wx:if="{{!isLogin}}">
        <view class="empowerIcon">
            <image mode="" src="{{img}}empowerIcon.png"></image>
        </view>
        <view class="empowerText">花生帮粉丝俱乐部</view>
        <view class="empower_button" wx:if="{{readState}}">手机号快捷登录<button bindgetphonenumber="getPhoneNumber" class="buttonPhone" openType="getPhoneNumber"></button>
        </view>
        <view bindtap="showToast" class="empower_button" wx:else>手机号快捷登录</view>
        <view class="empower_read">
            <view class="empower_read_Icon">
                <image bindtap="changeRead" mode="" src="{{img}}noChoose.png" wx:if="{{!readState}}"></image>
                <image bindtap="changeRead" mode="" src="{{img}}choose.png" wx:else></image>
            </view>
            <view class="empower_read_text">我已阅读并同意<text bindtap="gotoUserAgreement">《用户协议》</text>和<text bindtap="gotoPrivacyPolicy">《隐私政策》</text>
            </view>
        </view>
    </block>
</view>
