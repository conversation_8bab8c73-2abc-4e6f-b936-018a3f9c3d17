<view class="container fadeIn" hoverClass="none" hoverStopPropagation="false">
    <custom-header :isBack="{{true}}" title="PEANUTS"></custom-header>
    <view class="scroll-view {{screenInfo}}" hoverClass="none" hoverStopPropagation="false" style="top:{{statusHeaderBarHeight}}px">
        <image binderror="" bindload="" class="bg" lazyLoad="true" src="{{imgUrl}}75/home/<USER>"></image>
        <image binderror="" bindload="" class="logo" lazyLoad="true" src="{{imgUrl}}75/home/<USER>"></image>
        <image binderror="" bindload="" class="title" lazyLoad="true" src="{{imgUrl}}75/home/<USER>"></image>
        <image binderror="" bindload="" catch:tap="handleStoryCollection" class="i1" lazyLoad="true" src="{{imgUrl}}75/home/<USER>"></image>
        <image binderror="" bindload="" catch:tap="handleRank" class="i2" lazyLoad="true" src="{{imgUrl}}75/home/<USER>"></image>
        <image binderror="" bindload="" catch:tap="handleRecord" class="i3" lazyLoad="true" src="{{imgUrl}}75/home/<USER>"></image>
        <image binderror="" bindload="" catch:tap="handleOpenRule" class="rule" lazyLoad="true" src="{{imgUrl}}75/home/<USER>"></image>
    </view>
    <copyright></copyright>
    <c-75-rule id="c-75-rule"></c-75-rule>
    <c-75-privacy bind:privacy="handlePrivacy" id="c-75-privacy"></c-75-privacy>
</view>
