var t, a = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  i = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  e = (t = require("../../../87624F60549B04BFE10427674BE30D65.js")) && t.__esModule ? t : {
    default: t
  };
var o = getApp();
Page({
  data: {
    img: o.globalData.img,
    propState: !1,
    propNum: 29,
    activityInfo: {},
    activityNo: "",
    userInfo: {}
  },
  confirmProp: function() {
    this.setData({
      propState: !1
    }), this.getActInfo()
  },
  closeProp: function() {
    this.setData({
      propState: !1
    })
  },
  gotoSq: function() {
    var t = this;
    this.data.userInfo.mobile ? ((0, a.loadingOpen)(), (0, i.instituteMiniApply)({
      activityNo: this.data.activityNo,
      openId: e.default.data.openid,
      token: e.default.data.token,
      userId: e.default.data.userInfo.id
    }).then((function(i) {
      (0, a.loadingClose)(), 200 == i.code ? t.setData({
        propState: !0,
        propNum: 29,
        timeMsg: i.data
      }) : (0, a.toastModel)(i.message)
    }))) : this.setData({
      propState: !0,
      propNum: 6
    })
  },
  onLoad: function(t) {
    console.log("fetchData", e.default), this.setData({
      activityNo: t.activityNo,
      userInfo: e.default.data.userInfo
    }), this.getActInfo()
  },
  getActInfo: function() {
    var t = this;
    (0, a.loadingOpen)(), (0, i.applyInfo)({
      activityNo: this.data.activityNo
    }).then((function(i) {
      (0, a.loadingClose)(), 200 == i.code ? (i.data.activityInfo = i.data.activityInfo.replace(/\<img/gi, '<img style="width:100%;height:auto;display:block;"'), i.data.activityInfo = i.data.activityInfo.replace("px", "rpx"), t.setData({
        activityInfo: i.data
      })) : (0, a.toastModel)(i.message)
    }))
  }
});