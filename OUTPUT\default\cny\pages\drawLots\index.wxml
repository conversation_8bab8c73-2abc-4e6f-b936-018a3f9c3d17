<view class="container fadeIn" hoverClass="none" hoverStopPropagation="false">
    <custom-header :isBack="{{true}}" title="PEANUTS"></custom-header>
    <view class="scroll-view {{screenInfo}}" hoverClass="none" hoverStopPropagation="false" style="top:{{statusHeaderBarHeight}}px">
        <image binderror="" bindload="" class="bg" lazyLoad="true" src="{{imgUrl}}drawLots/bg.jpg?v={{imgVersion}}"></image>
        <view class="kvBox" hoverClass="none" hoverStopPropagation="false">
            <image binderror="" bindload="" class="kvbg" lazyLoad="true" mode="widthFix" src="{{imgUrl}}drawLots/kv.png?v={{imgVersion}}"></image>
            <image binderror="" bindload="" class="tip1" lazyLoad="true" src="{{imgUrl}}drawLots/tip1.png?v={{imgVersion}}"></image>
            <view class="tipBox" hoverClass="none" hoverStopPropagation="false">
                <image binderror="" bindload="" class="tip2" lazyLoad="true" src="{{imgUrl}}drawLots/tip2.png?v={{imgVersion}}"></image>
                <view class="numberBox" hoverClass="none" hoverStopPropagation="false">
                    <image binderror="" bindload="" class="box" lazyLoad="false" src="{{imgUrl}}drawLots/box1.png?v={{imgVersion}}"></image>
                    <view class="number AaHouDiHei" hoverClass="none" hoverStopPropagation="false">{{userInfo.drawNub}}</view>
                </view>
            </view>
            <view class="circleBox" hoverClass="none" hoverStopPropagation="false">
                <image binderror="" bindload="" class="drawImg {{interval?'drawImg'+index:''}} carousel-item-{{index}} " lazyLoad="true" src="{{item.img}}" wx:for="{{lotsList}}" wx:key="index"></image>
            </view>
            <image binderror="" bindload="" class="dog" lazyLoad="true" src="{{imgUrl}}drawLots/dog.png?v={{imgVersion}}"></image>
            <image binderror="" bindload="" catch:tap="startDraw" class="s1" lazyLoad="true" src="{{imgUrl}}drawLots/s1.png?v={{imgVersion}}"></image>
        </view>
        <congratulations bind:isGet="handleIsGet" id="congratulationsComponents"></congratulations>
        <sorry id="sorryComponents"></sorry>
        <regret id="regretComponents"></regret>
        <unlockAll id="unlockAllComponents"></unlockAll>
    </view>
    <copyright></copyright>
</view>
