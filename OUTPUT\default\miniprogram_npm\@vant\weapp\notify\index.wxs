var style = require('..\..\..\..\require('.\miniprogram_npm\ @vant\ weapp\ wxs\ style.wxs ')();');
var addUnit = require('..\..\..\..\require('.\miniprogram_npm\ @vant\ weapp\ wxs\ add - unit.wxs ')();');

function rootStyle(data) {
  return (style(({
    'z-index': data.zIndex,
    top: addUnit(data.top),
  })))
};

function notifyStyle(data) {
  return (style(({
    background: data.background,
    color: data.color,
  })))
};
module.exports = ({
  rootStyle: rootStyle,
  notifyStyle: notifyStyle,
});