var e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  n = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  r = require("../../../71D07D80549B04BF17B615870C540D65.js"),
  a = require("../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  o = require("../../../6D59C885549B04BF0B3FA082E5940D65.js");
require("../../../03E40C94549B04BF6582649340450D65.js").mount();
var t = require("../../../A5622344549B04BFC3044B435F450D65.js");
Page({
  data: {
    statusHeaderBarHeight: o.statusHeaderBarHeight,
    imgUrl: o.imgUrl,
    imgVersion: o.imgVersion,
    screenInfo: (0, a.getScreenInfo)()
  },
  onLoad: function(i) {
    return n(e().mark((function n() {
      var u, s, c, p, l, d, g, _, f, m, x;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            if (console.log("cny index options: ", i), !(0, a.isTokenExpired)()) {
              e.next = 14;
              break
            }
            return e.prev = 2, e.next = 5, (0, r.getLogin)();
          case 5:
            u = e.sent, console.log("loginInfo: ", u), e.next = 12;
            break;
          case 9:
            e.prev = 9, e.t0 = e.catch(2), wx.showToast({
              title: o.errorInfo.tokenError,
              icon: "none"
            });
          case 12:
            e.next = 14;
            break;
          case 14:
            if (!(0, a.getAccessToken)()) {
              e.next = 27;
              break
            }
            return e.prev = 15, e.next = 18, (0, r.getUserInfo)();
          case 18:
            s = e.sent, c = s.code, p = s.data, 200 === c && ((0, a.setStorageSync)("userInfo", p.userInfo), (0, a.setStorageSync)("openid", p.userInfo.openId), (0, a.setStorageSync)("currentJf", p.currentJf), t.init({
              projectId: "14",
              projectName: "史努比",
              api_key: "115e840655b87fc84d67406c7b4646c3fb143f4f02bba502eb5a945eb358",
              openid: null !== (l = p.userInfo.openId) && void 0 !== l ? l : "",
              unionid: "",
              ad_id: null !== (d = null == i || null === (g = i.query) || void 0 === g ? void 0 : g.ad_id) && void 0 !== d ? d : "",
              url_link_id: null !== (_ = null == i || null === (f = i.query) || void 0 === f ? void 0 : f.url_link_id) && void 0 !== _ ? _ : "",
              short_link_id: null !== (m = null == i || null === (x = i.query) || void 0 === x ? void 0 : x.short_link_id) && void 0 !== m ? m : "",
              debug: !0
            })), e.next = 27;
            break;
          case 24:
            e.prev = 24, e.t1 = e.catch(15), console.log("index userInfo error: ", e.t1);
          case 27:
            return e.next = 29, (0, a.checkVersionUpdate)();
          case 29:
            t.pageView({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "首页",
              page_name: "首页",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/index/index"
            });
          case 30:
          case "end":
            return e.stop()
        }
      }), n, null, [
        [2, 9],
        [15, 24]
      ])
    })))()
  },
  handleStartDrawLots: function() {
    wx.navigateTo({
      url: "/cny/pages/drawLots/index"
    }), t.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "首页",
      page_name: "首页",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/index/index",
      button_name: "首页-开始抽签"
    })
  },
  handleMyTask: function() {
    wx.navigateTo({
      url: "/cny/pages/task/index"
    }), t.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "首页",
      page_name: "首页",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/index/index",
      button_name: "首页-我的任务"
    })
  },
  handleMyRedTicket: function() {
    wx.navigateTo({
      url: "/cny/pages/redPacket/index"
    }), t.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "首页",
      page_name: "首页",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/index/index",
      button_name: "首页-我的红包"
    })
  },
  handleOpenRule: function() {
    this.selectComponent("#ruleComponents").openMask()
  },
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return o.shareOptions
  }
});