var e = require("../../../6D59C885549B04BF0B3FA082E5940D65.js");
Component({
  properties: {
    title: {
      type: String,
      value: "页面标题"
    },
    bgColor: {
      type: String,
      value: "#fff"
    },
    textColor: {
      type: String,
      value: "#000"
    },
    isBack: {
      type: Boolean,
      value: !0
    }
  },
  data: {
    statusBarHeight: e.statusBarHeight,
    statusHeaderBarHeight: e.statusHeaderBarHeight
  },
  pageLifetimes: {
    show: function() {},
    hide: function() {},
    resize: function(e) {}
  },
  methods: {
    goBack: function() {
      wx.navigateBack({
        delta: 1,
        fail: function(e) {
          wx.reLaunch({
            url: "/cny/pages/home/<USER>"
          })
        }
      })
    }
  }
});