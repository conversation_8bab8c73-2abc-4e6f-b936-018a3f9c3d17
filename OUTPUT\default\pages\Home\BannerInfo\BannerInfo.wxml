<view class="BannerInfo">
    <view class="home_banner">
        <view class="home_banner_items">
            <view bindtap="changeIndex" class="home_banner_item" data-index="{{index}}" wx:for="{{peanutFamily}}" wx:key="index">
                <view class="home_banner_item_imgs" wx:if="{{chooseBanner==index}}">
                    <image mode="" src="{{item.imgUrl}}"></image>
                </view>
                <view class="home_banner_item_img" wx:else>
                    <image mode="" src="{{item.imgUrl}}"></image>
                </view>
                <view class="home_banner_item_text">{{item.name}}</view>
            </view>
        </view>
    </view>
    <image class="images" mode="widthFix" src="{{peanutFamily[chooseBanner].detailsImgUrl}}"></image>
    <view bindtap="gotoWebUrl" class="BannerInfo_button">
        <image mode="" src="{{img}}newVersion/030.png"></image>
    </view>
    <footer></footer>
</view>
