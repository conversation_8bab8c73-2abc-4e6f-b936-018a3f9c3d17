var e, t = require("../../../@babel/runtime/helpers/objectSpread2"),
  a = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  n = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  r = (e = require("../../../A5622344549B04BFC3044B435F450D65.js")) && e.__esModule ? e : {
    default: e
  },
  o = require("../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  i = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  s = require("../../../71D07D80549B04BF17B615870C540D65.js");
require("../../../03E40C94549B04BF6582649340450D65.js").mount();
var u = !0;
Page({
  data: {
    statusHeaderBarHeight: i.statusHeaderBarHeight,
    imgUrl: i.imgUrl,
    imgVersion: i.imgVersion,
    screenInfo: (0, o.getScreenInfo)(),
    isMenu: "hots",
    pageInfo: {
      pageNum: 1,
      pageSize: 10
    },
    total: 0,
    rankList: [],
    noMoreData: !1
  },
  onLoad: function(e) {
    return n(a().mark((function e() {
      return a().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            r.default.pageView({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "排行榜",
              page_name: "排行榜",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/rankList/index"
            });
          case 1:
          case "end":
            return e.stop()
        }
      }), e)
    })))()
  },
  handleSelectMenu: function(e) {
    var t = e.currentTarget.dataset.ismenu;
    this.setData({
      isMenu: t
    }), u && (u = !1, this.resetData(), "hots" === t ? this.getStoryWallByLikeFn() : "news" === t && this.getStoryWallByTimeFn(), r.default.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "排行榜",
      page_name: "排行榜",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/rankList/index",
      button_name: "hots" === t ? "最热" : "最新"
    }))
  },
  handleGoCollection: function() {
    wx.navigateTo({
      url: "/cny/pages/collection/index?isDel=false"
    }), r.default.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "排行榜",
      page_name: "排行榜",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/rankList/index",
      button_name: "75周年新品合辑"
    })
  },
  getStoryWallByTimeFn: function() {
    var e = this;
    return n(a().mark((function n() {
      var r, o, i, u;
      return a().wrap((function(a) {
        for (;;) switch (a.prev = a.next) {
          case 0:
            if (a.prev = 0, !e.data.noMoreData) {
              a.next = 3;
              break
            }
            return a.abrupt("return");
          case 3:
            return a.next = 5, (0, s.getStoryWallByTime)(t({}, e.data.pageInfo));
          case 5:
            r = a.sent, o = r.code, i = r.rows, u = r.total, 200 === o && e.dataProcess(i, u), a.next = 15;
            break;
          case 12:
            a.prev = 12, a.t0 = a.catch(0), console.error(a.t0);
          case 15:
          case "end":
            return a.stop()
        }
      }), n, null, [
        [0, 12]
      ])
    })))()
  },
  getStoryWallByLikeFn: function() {
    var e = this;
    return n(a().mark((function n() {
      var r, o, i, u;
      return a().wrap((function(a) {
        for (;;) switch (a.prev = a.next) {
          case 0:
            if (a.prev = 0, !e.data.noMoreData) {
              a.next = 3;
              break
            }
            return a.abrupt("return");
          case 3:
            return a.next = 5, (0, s.getStoryWallByLike)(t({}, e.data.pageInfo));
          case 5:
            r = a.sent, o = r.code, i = r.rows, u = r.total, 200 === o && e.dataProcess(i, u), a.next = 15;
            break;
          case 12:
            a.prev = 12, a.t0 = a.catch(0), console.error(a.t0);
          case 15:
          case "end":
            return a.stop()
        }
      }), n, null, [
        [0, 12]
      ])
    })))()
  },
  dataProcess: function(e, t) {
    var a = this;
    e.forEach((function(e) {
      e.likeNub = a.formatLikes(e.likeNub)
    })), this.setData({
      rankList: this.data.rankList.concat(e),
      total: t,
      "pageInfo.pageNum": this.data.pageInfo.pageNum + 1
    }, (function() {
      a.setData({
        noMoreData: e.length < a.data.pageInfo.pageSize || a.data.rankList.length >= t
      }), a.data.noMoreData && a.selectComponent("#c-75-upload-list").noMoreDataFn(!0), u = !0
    }))
  },
  formatLikes: function(e) {
    return e >= 1e4 ? (e / 1e4).toFixed(1) + "w" : e.toString()
  },
  bindscrolltolower: function(e) {
    e.detail.flag && ("hots" === this.data.isMenu ? this.getStoryWallByLikeFn() : "news" === this.data.isMenu && this.getStoryWallByTimeFn())
  },
  resetData: function() {
    this.setData({
      "pageInfo.pageNum": 1,
      "pageInfo.pageSize": 10,
      total: 0,
      rankList: [],
      noMoreData: !1
    }), this.selectComponent("#c-75-upload-list").noMoreDataFn(!1)
  },
  onReady: function() {},
  onShow: function() {
    this.resetData(), "hots" === this.data.isMenu ? this.getStoryWallByLikeFn() : "news" === this.data.isMenu && this.getStoryWallByTimeFn()
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return i.shareOptions
  }
});