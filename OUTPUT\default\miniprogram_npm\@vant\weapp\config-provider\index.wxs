var object = require('..\..\..\..\require('.\miniprogram_npm\ @vant\ weapp\ wxs\ object.wxs ')();');
var style = require('..\..\..\..\require('.\miniprogram_npm\ @vant\ weapp\ wxs\ style.wxs ')();');

function kebabCase(word) {
  var newWord = word.replace(getRegExp("[A-Z]", 'g'), (function(i) {
    return ('-' + i)
  })).toLowerCase().replace(getRegExp("^-"), '');
  return (newWord)
};

function mapThemeVarsToCSSVars(themeVars) {
  var cssVars = ({});
  object.keys(themeVars).forEach((function(key) {
    var cssVarsKey = '--' + kebabCase(key);
    cssVars[((nt_0 = (cssVarsKey), null == nt_0 ? undefined : 'number' === typeof nt_0 ? nt_0 : "" + nt_0))] = themeVars[((nt_1 = (key), null == nt_1 ? undefined : 'number' === typeof nt_1 ? nt_1 : "" + nt_1))]
  }));
  return (style(cssVars))
};
module.exports = ({
  kebabCase: kebabCase,
  mapThemeVarsToCSSVars: mapThemeVarsToCSSVars,
});