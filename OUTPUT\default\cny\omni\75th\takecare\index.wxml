<view class="container fadeIn" hoverClass="none" hoverStopPropagation="false">
    <custom-header :isBack="{{true}}" title="PEANUTS"></custom-header>
    <c-layouts bgColor="#F7C129" bind:onLogin="onLogin">
        <cImage src="omni/75th/takecare/top_bg.png"></cImage>
        <view class="sBox">
            <swiper autoplay circular bindchange="bindSwiperChange" class="swiperBox" current="{{sIndex}}" duration="2500" interval="5000">
                <swiper-item wx:for="{{sData}}" wx:key="index">
                    <cImage src="{{item.img}}"></cImage>
                </swiper-item>
            </swiper>
            <cImage class="prevIcon" src="omni/75th/takecare/prev.png"></cImage>
            <cImage class="nextIcon" src="omni/75th/takecare/next.png"></cImage>
        </view>
        <cImage class="text" src="{{sData[sIndex].text}}"></cImage>
        <cImage catch:tap="handleGo" class="submit" src="omni/75th/takecare/submit.png"></cImage>
    </c-layouts>
</view>
<view class="maskContainer" wx:if="{{maskFlag}}">
    <view class="maskBox">
        <cImage class="qcode" showMenuByLongpress="true" src="omni/75th/takecare/qcode.png"></cImage>
        <cImage catch:tap="handleClose" class="close" src="omni/75th/takecare/close.png"></cImage>
    </view>
</view>
