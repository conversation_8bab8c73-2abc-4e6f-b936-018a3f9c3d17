var e, t = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  n = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  o = require("../../../@babel/runtime/helpers/objectSpread2"),
  a = require("../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  i = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  s = (e = require("../../../58EF87A0549B04BF3E89EFA7D7840D65.js")) && e.__esModule ? e : {
    default: e
  },
  r = require("../../../71D07D80549B04BF17B615870C540D65.js");
require("../../../03E40C94549B04BF6582649340450D65.js").mount();
var d = require("../../../A5622344549B04BFC3044B435F450D65.js");
Page({
  data: {
    statusHeaderBarHeight: i.statusHeaderBarHeight,
    imgUrl: i.imgUrl,
    imgVersion: i.imgVersion,
    screenInfo: (0, a.getScreenInfo)(),
    title: "",
    content: "",
    uploadImg: "",
    storyId: "",
    sunCode: "",
    posterConfig: {}
  },
  onLoad: function(e) {
    d.pageView({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "故事征集",
      page_name: "故事征集",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/uploads/index"
    })
  },
  handleGetTitle: function(e) {
    var t = e.detail.value;
    this.setData({
      title: t
    })
  },
  handleGetContent: function(e) {
    var t = e.detail.value;
    this.setData({
      content: t
    })
  },
  handleUploadStore: function() {
    "" === this.data.title ? wx.showToast({
      icon: "none",
      title: "请输入标题"
    }) : this.data.title.length > 10 ? wx.showToast({
      icon: "none",
      title: "标题最多10个字符"
    }) : "" === this.data.uploadImg ? wx.showToast({
      icon: "none",
      title: "请上传图片"
    }) : "" === this.data.content ? wx.showToast({
      icon: "none",
      title: "请输入内容"
    }) : this.submitStory()
  },
  handleUploadImg: function() {
    var e = this;
    wx.showLoading({
      title: "上传中",
      mask: !0
    }), wx.chooseImage({
      success: function(t) {
        var n = t.tempFilePaths;
        wx.getFileInfo({
          filePath: n[0],
          success: function(t) {
            t.size > 10485760 ? wx.showToast({
              title: "图片大小不能超过10MB",
              icon: "none"
            }) : e.uploadImages(n[0])
          },
          fail: function(e) {
            wx.showToast({
              title: "获取文件信息失败",
              icon: "none"
            })
          }
        })
      },
      fail: function(e) {
        wx.hideLoading()
      },
      complete: function(e) {}
    })
  },
  uploadImages: function(e) {
    var t = this;
    wx.uploadFile({
      url: i.ApiBaseUri + "/common/picture/quality",
      filePath: e,
      name: "file",
      formData: {},
      success: function(e) {
        var n = e.data;
        if (200 === e.statusCode) {
          var o = JSON.parse(n);
          200 === o.code ? t.setData({
            uploadImg: o.msg
          }) : 20010 === o.code ? wx.showToast({
            icon: "none",
            title: o.msg,
            duration: 2e3
          }) : wx.showToast({
            icon: "none",
            title: "图片上传不合规",
            duration: 2e3
          })
        }
      },
      fail: function(e) {},
      complete: function(e) {
        wx.hideLoading()
      }
    })
  },
  onPosterSuccess: function(e) {
    var t = e.detail;
    wx.hideLoading(), t && wx.saveImageToPhotosAlbum({
      filePath: t
    })
  },
  onPosterFail: function(e) {
    console.log(e)
  },
  setPosterConfig: function() {
    console.log(this.data.sunCode, "==this.data.sunCode=="), this.setData({
      posterConfig: {
        width: 750,
        height: 1e3,
        backgroundColor: "#ffffff",
        debug: !1,
        pixelRatio: 2,
        images: [{
          url: this.data.uploadImg,
          width: 542,
          height: 555,
          y: 180,
          x: 115
        }, {
          url: i.imgUrl + "75/components/upDetails/dowload_bg.png?v=" + i.imgVersion,
          width: 750,
          height: 1e3,
          y: 0,
          x: 0
        }, {
          url: this.data.sunCode,
          width: 98,
          height: 98,
          x: 490,
          y: 790,
          zIndex: 3
        }],
        texts: [{
          x: 215,
          y: 844,
          baseLine: "middle",
          text: this.data.title,
          fontSize: 32,
          color: "#fed200",
          lineNum: 1,
          width: 449,
          lineHeight: 32,
          textAlign: "center",
          zIndex: 99,
          fontFamily: "LogoSCUnboundedSans-Regular"
        }]
      }
    }), this.onCreatePoster()
  },
  onCreatePoster: function() {
    var e = this;
    this.setData({
      posterConfig: o({}, this.data.posterConfig)
    }, (function() {
      s.default.create(!0, e)
    }))
  },
  handleSaveImg: function(e) {
    e.detail.flag && (wx.showLoading({
      title: "生成中"
    }), this.setPosterConfig())
  },
  getQRCodeFn: function() {
    var e = arguments,
      o = this;
    return n(t().mark((function n() {
      var a, s, d, u;
      return t().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return a = e.length > 0 && void 0 !== e[0] ? e[0] : "", t.prev = 1, t.next = 4, (0, r.getQRCode)({
              appid: i.APPID,
              page: "cny/pages/details/index",
              scene: a,
              env_version: i.ENV
            });
          case 4:
            s = t.sent, d = s.code, u = s.data, 200 === d && (o.setData({
              sunCode: u.QRCodeUrl
            }), o.selectComponent("#upload-success").openMask(o.data.uploadImg, o.data.title, o.data.content, u.QRCodeUrl, o.data.storyId)), wx.hideLoading(), t.next = 14;
            break;
          case 11:
            t.prev = 11, t.t0 = t.catch(1), console.log("getQRCodeFn:", t.t0);
          case 14:
          case "end":
            return t.stop()
        }
      }), n, null, [
        [1, 11]
      ])
    })))()
  },
  submitStory: function() {
    var e = this;
    return n(t().mark((function n() {
      var o, a, i;
      return t().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return wx.showLoading({
              title: "上传中",
              mask: !0
            }), t.prev = 1, t.next = 4, (0, r.submitStory)({
              title: e.data.title,
              photo: e.data.uploadImg,
              content: e.data.content
            });
          case 4:
            if (o = t.sent, a = o.code, i = o.data, 200 !== a) {
              t.next = 13;
              break
            }
            return e.setData({
              storyId: i.storyId
            }), t.next = 11, e.getQRCodeFn("storyId=" + i.storyId);
          case 11:
            wx.showToast({
              icon: "none",
              title: "故事正在审核中，通过后积分+10",
              duration: 2e3
            }), d.pageClickEvent({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "故事征集",
              page_name: "故事征集",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/uploads/index",
              button_name: "上传故事"
            });
          case 13:
            t.next = 18;
            break;
          case 15:
            t.prev = 15, t.t0 = t.catch(1), console.log("submitStory", t.t0);
          case 18:
          case "end":
            return t.stop()
        }
      }), n, null, [
        [1, 15]
      ])
    })))()
  },
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return i.shareOptions
  }
});