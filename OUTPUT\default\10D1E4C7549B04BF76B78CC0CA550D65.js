var e = require("./@babel/runtime/helpers/objectSpread2.js"),
  n = Page,
  o = require("A5622344549B04BFC3044B435F450D65.js");
module.exports = function() {
  var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
    a = t.onLoad,
    i = t.onShow,
    r = t.onReady,
    s = t.onHide,
    p = (t.onShareAppMessage, t.data),
    u = {
      data: e({}, p),
      onLoad: function() {
        for (var e, n = arguments.length, t = new Array(n), i = 0; i < n; i++) t[i] = arguments[i];
        (e = console).log.apply(e, ["小程序全局mixin"].concat(t)), setTimeout((function() {
          o.uid && o.createSession((function() {
            setTimeout((function() {
              o.recordPageChange.apply(o, t)
            }), 1e3)
          }))
        }), 3e3);
        t[0];
        var r = {};
        this.$opts = r, a && a.apply(this, t)
      },
      onShow: function() {
        i && i.apply(this)
      },
      onReady: function() {
        r && r.apply(this)
      },
      onHide: function() {
        s && (o.endSession(), s.apply(this))
      }
    };
  return n(Object.assign({}, t, u))
};