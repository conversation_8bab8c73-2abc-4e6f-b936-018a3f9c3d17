<input adjustPosition="{{adjustPosition}}" alwaysEmbed="{{alwaysEmbed}}" autoFocus="{{autoFocus}}" bindblur="onBlur" bindconfirm="onConfirm" bindfocus="onFocus" bindinput="onInput" bindkeyboardheightchange="onKeyboardHeightChange" bindtap="onClickInput" class="{{utils.bem( 'field__control',[ inputAlign,{disabled:disabled,error:error} ] )}} input-class" confirmHold="{{confirmHold}}" confirmType="{{confirmType}}" cursor="{{cursor}}" cursorSpacing="{{cursorSpacing}}" disabled="{{disabled||readonly}}" focus="{{focus}}" holdKeyboard="{{holdKeyboard}}" maxlength="{{maxlength}}" password="{{password||type==='password'}}" placeholder="{{placeholder}}" placeholderClass="{{utils.bem( 'field__placeholder',{error:error} )}}" placeholderStyle="{{placeholderStyle}}" selectionEnd="{{selectionEnd}}" selectionStart="{{selectionStart}}" type="{{type}}" value="{{innerValue}}"></input>
