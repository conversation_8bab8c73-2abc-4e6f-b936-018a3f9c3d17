var t, e = require("./@babel/runtime/helpers/regeneratorRuntime.js"),
  o = require("./@babel/runtime/helpers/asyncToGenerator.js"),
  r = (t = require("CB284115549B04BFAD4E2912D8A40D65.js")) && t.__esModule ? t : {
    default: t
  },
  n = require("6D59C885549B04BF0B3FA082E5940D65.js"),
  a = require("95D1B746549B04BFF3B7DF41DA740D65.js");
var u = function() {
    var t = o(e().mark((function t() {
      return e().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return t.abrupt("return", (0, r.default)({
              url: "/snoopy/online",
              method: "GET",
              data: {}
            }));
          case 1:
          case "end":
            return t.stop()
        }
      }), t)
    })));
    return function() {
      return t.apply(this, arguments)
    }
  }(),
  s = function() {
    var t = o(e().mark((function t() {
      var o;
      return e().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return t.next = 2, (0, a.getJsCode)();
          case 2:
            if (!(o = t.sent)) {
              t.next = 7;
              break
            }
            return t.abrupt("return", (0, r.default)({
              url: "/snoopy/login",
              method: "post",
              data: {
                authorizerAppid: n.APPID,
                jsCode: o
              }
            }).then((function(t) {
              var e = t.data;
              if (e) return (0, a.setAccessToken)(e.token), wx.setStorageSync("token_expire", Date.now()), t
            })));
          case 7:
            wx.showToast({
              icon: "none",
              title: "获取jscode接口失败"
            });
          case 8:
          case "end":
            return t.stop()
        }
      }), t)
    })));
    return function() {
      return t.apply(this, arguments)
    }
  }();
module.exports = {
  homeOnline: u,
  getLogin: s,
  getUserInfo: function() {
    return (0, r.default)({
      url: "/snoopy/getUserInfo",
      method: "GET",
      data: {}
    })
  },
  userShare: function() {
    return (0, r.default)({
      url: "/snoopy/userShare",
      data: {}
    })
  },
  checkUserShareInfo: function() {
    return (0, r.default)({
      url: "/snoopy/userShareInfo",
      data: {}
    })
  },
  exchangeDrawNub: function() {
    return (0, r.default)({
      url: "/snoopy/exchangeDrawNub",
      method: "post",
      data: {},
      showLoadingText: ""
    })
  },
  getRedCoverList: function() {
    return (0, r.default)({
      url: "/snoopy/getRedCoverList",
      method: "get",
      data: {}
    })
  },
  getRedCoverConfig: function() {
    return (0, r.default)({
      url: "/snoopy/getRedCoverConfig",
      method: "get",
      data: {}
    })
  },
  getCouponList: function() {
    return (0, r.default)({
      url: "/snoopy/getCouponList",
      method: "get",
      data: {}
    })
  },
  getRedCover: function() {
    return (0, r.default)({
      url: "/snoopy/getRedCover",
      method: "post",
      data: {},
      showLoadingText: ""
    })
  },
  getUserExchangeInfo: function() {
    return (0, r.default)({
      url: "/snoopy/userExchangeInfo",
      method: "get",
      data: {},
      showLoadingText: ""
    })
  },
  getQRCode: function(t) {
    return (0, r.default)({
      url: "/snoopy/getQRCode",
      method: "post",
      data: t,
      showLoadingText: ""
    })
  },
  submitStory: function(t) {
    return (0, r.default)({
      url: "/snoopy/submitStory",
      method: "post",
      data: t,
      showLoadingText: ""
    })
  },
  getMyStoryList: function() {
    return (0, r.default)({
      url: "/snoopy/storyListMy",
      method: "get",
      data: {},
      showLoadingText: ""
    })
  },
  getStoryDetail: function(t) {
    return (0, r.default)({
      url: "/snoopy/storyDetail/".concat(t.storyId),
      method: "get",
      data: {},
      showLoadingText: ""
    })
  },
  deleteMyStory: function(t) {
    return (0, r.default)({
      url: "/snoopy/deleteMyStory/".concat(t.storyId),
      method: "post",
      data: {},
      showLoadingText: ""
    })
  },
  likeStory: function(t) {
    return (0, r.default)({
      url: "/snoopy/likeStory/".concat(t.storyId),
      method: "post",
      data: {},
      showLoadingText: ""
    })
  },
  getStoryWallByTime: function(t) {
    return (0, r.default)({
      url: "/snoopy/storyWallByTime",
      method: "get",
      data: t,
      showLoadingText: ""
    })
  },
  getStoryWallByLike: function(t) {
    return (0, r.default)({
      url: "/snoopy/storyWallByLike",
      method: "get",
      data: t,
      showLoadingText: ""
    })
  }
};