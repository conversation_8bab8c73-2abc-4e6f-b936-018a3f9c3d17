Object.defineProperty(exports, "__esModule", {
  value: !0
});
var e = require("../common/component"),
  t = require("../common/relation");
(0, e.VantComponent)({
  classes: ["active-class", "disabled-class"],
  relation: (0, t.useParent)("sidebar"),
  props: {
    dot: <PERSON>olean,
    badge: null,
    info: null,
    title: String,
    disabled: <PERSON>olean
  },
  methods: {
    onClick: function() {
      var e = this,
        t = this.parent;
      if (t && !this.data.disabled) {
        var i = t.children.indexOf(this);
        t.setActive(i).then((function() {
          e.$emit("click", i), t.$emit("change", i)
        }))
      }
    },
    setActive: function(e) {
      return this.setData({
        selected: e
      })
    }
  }
});