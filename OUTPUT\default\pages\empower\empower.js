var e, n = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/asyncToGenerator"),
  a = require("../../6F218526549B04BF0947ED2133340D65.js"),
  o = require("../../8F86AFB2549B04BFE9E0C7B593D30D65.js"),
  i = require("../../A4000F75549B04BFC2666772D6B30D65.js"),
  r = require("../../83F188C3549B04BFE597E0C403C30D65.js"),
  s = (e = require("../../87624F60549B04BFE10427674BE30D65.js")) && e.__esModule ? e : {
    default: e
  };
var u = getApp();
Page({
  data: {
    img: u.globalData.img,
    readState: !1,
    isLogin: !0
  },
  showToast: function() {
    (0, a.toastModel)("请先阅读并同意《用户协议》和《隐私政策》")
  },
  changeRead: function() {
    this.setData({
      readState: !this.data.readState
    })
  },
  gotoUserAgreement: function() {
    wx.navigateTo({
      url: "/pages/empower/UserAgreement/UserAgreement"
    })
  },
  gotoPrivacyPolicy: function() {
    wx.navigateTo({
      url: "/pages/empower/PrivacyPolicy/PrivacyPolicy"
    })
  },
  getPhone: function(e) {
    return console.log("code", e), new Promise((function(n, t) {
      (0, i.miniappGetPhoneNumber)({
        appid: u.globalData.appId,
        code: e
      }).then((function(e) {
        n(e)
      }))
    }))
  },
  gxPhone: function(e) {
    return new Promise((function(n, t) {
      (0, i.fullInfo)({
        mobile: e
      }).then((function(e) {
        n(e)
      }))
    }))
  },
  getPhoneNumber: function(e) {
    var u = this;
    return t(n().mark((function t() {
      var d;
      return n().wrap((function(n) {
        for (;;) switch (n.prev = n.next) {
          case 0:
            if ("getPhoneNumber:ok" != e.detail.errMsg) {
              n.next = 17;
              break
            }
            return (0, a.loadingOpen)(), n.next = 4, u.getPhone(e.detail.code);
          case 4:
            return d = n.sent, n.next = 7, (0, r.getUser)();
          case 7:
            if (!(0, o.get)("inviteOpenId")) {
              n.next = 13;
              break
            }
            console.log("准备调用邀请注册接口"), (0, a.loadingOpen)(), (0, i.inviteGrant)({
              binviteId: s.default.data.userInfo.id,
              binviteOpenId: s.default.data.openid,
              inviteId: (0, o.get)("inviteId"),
              inviteOpenId: (0, o.get)("inviteOpenId"),
              token: s.default.data.token,
              mobile: d.data.phoneNumber,
              sign: d.data.sign,
              timestamp: d.data.timestamp
            }).then((function(e) {
              console.log("邀请注册接口返回数据", e), (0, a.loadingClose)(), wx.reLaunch({
                url: "/pages/Home/Home"
              })
            })), n.next = 17;
            break;
          case 13:
            return n.next = 15, u.gxPhone(d.data.phoneNumber);
          case 15:
            (0, a.loadingClose)(), wx.reLaunch({
              url: "/pages/Home/Home"
            });
          case 17:
          case "end":
            return n.stop()
        }
      }), t)
    })))()
  },
  miniappLogin: function(e) {
    return new Promise((function(n, t) {
      (0, i.miniappLogin)({
        authorizerAppid: u.globalData.appId,
        jsCode: e
      }).then((function(e) {
        n(e)
      }))
    }))
  },
  getToken: function(e) {
    return new Promise((function(n, t) {
      (0, i.wechatOpenid)({
        serviceSign: e.serviceSign,
        openid: e.openid,
        appId: u.globalData.appId,
        appType: 2
      }).then((function(e) {
        s.default.data.registerUserInfo = e.data, n(e)
      }))
    }))
  },
  onLoad: function(e) {
    var i = this;
    return t(n().mark((function t() {
      var u, d, c;
      return n().wrap((function(n) {
        for (;;) switch (n.prev = n.next) {
          case 0:
            if (s.default.data.token) {
              n.next = 15;
              break
            }
            return n.next = 3, (0, a.getCode)();
          case 3:
            return u = n.sent, n.next = 6, i.miniappLogin(u);
          case 6:
            return d = n.sent, console.log("获取用户openid", d), s.default.data.openid = d.data.openid, console.log(d.data), n.next = 12, i.getToken(d.data);
          case 12:
            c = n.sent, console.log("获取用户token", c), s.default.data.token = c.data.token;
          case 15:
            return n.next = 17, (0, r.getUser)();
          case 17:
            if (console.log("fetchData.data", s.default.data), console.log("当前用户数据", s.default), !s.default.data.userInfo.mobile) {
              n.next = 23;
              break
            }
            return i.setData({
              isLogin: !0
            }), wx.reLaunch({
              url: "/pages/Home/Home"
            }), n.abrupt("return");
          case 23:
            i.setData({
              isLogin: !1
            }), console.log("注册页面数据", e), console.log("options", e.inviteId), console.log("options", e.inviteOpenId), (e.inviteId || e.inviteOpenId) && ((0, o.set)("inviteId", e.inviteId), (0, o.set)("inviteOpenId", e.inviteOpenId));
          case 28:
          case "end":
            return n.stop()
        }
      }), t)
    })))()
  },
  onReady: function() {},
  onShow: function() {
    this.setData({
      isLogin: !0
    })
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {}
});