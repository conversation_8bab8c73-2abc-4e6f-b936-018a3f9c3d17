<view class="prop">
    <view class="prop_rule" wx:if="{{propNum==100}}">
        <view bindtap="closeProp" class="prop_rule_close">
            <image mode="" src="{{img}}closeProp.png"></image>
        </view>
        <view class="prop_rule_topBg">活动规则</view>
        <view class="prop_rule_text">
            <view>抽奖说明：</view>
            <view>1、【第一步】扫描二维码查询防伪信息</view>
            <view>2、【第二步】点击下方“参与抽奖活动”按钮进行抽奖（抽奖二维码为单次有效）</view>
            <view>3、【第三步】提示恭喜您中奖，显示获取能量值，</view>
            <view>4、【第四步】点击“个人中心”按钮前往个人中心，或点击下方关注公众号，并长按识别进入公众号</view>
            <view>5、【第五步】进入“花生帮粉俱乐部”个人中心前往能量商城进行兑换</view>
        </view>
    </view>
    <view class="prop_rule" wx:if="{{propNum==0}}">
        <view bindtap="closeProp" class="prop_rule_closes">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_rule_topBg"></view>
        <rich-text class="cnt" nodes="{{ruleText}}"></rich-text>
    </view>
    <view bindtap="closeProp" class="prop_energy_close" wx:if="{{propNum==2}}">
        <image mode="" src="{{img}}closeProp.png"></image>
    </view>
    <view class="prop_energy" wx:if="{{propNum==1}}">
        <view class="prop_energy_top">
            <view bindtap="closeProp" class="prop_Cj_nlbz_close">
                <image mode="" src="{{img}}newVersion/008.png"></image>
            </view>
            <view class="prop_Cj_nlbz_top" style="top:-40rpx;">
                <view class="prop_Cj_nlbz_top_text">恭喜您！</view>
                <image mode="" src="{{img}}newVersion/007.png"></image>
            </view>
            <view class="prop_Cj_nlbz_text">获得{{energyNumber}}能量奖励!</view>
            <view bindtap="gotoMy" class="prop_energy_top_button">前往个人中心</view>
            <view class="prop_Cj_nlbz_bqNew" style="color:#000;margin-top:42rpx;">
                <view>©2025 Peanuts Worldwide LLC</view>
                <view>www.peanuts.com</view>
            </view>
        </view>
        <view bindtap="showGZH" class="prop_energy_bot"></view>
    </view>
    <view class="prop_gzh" wx:if="{{propNum==2}}">
        <view class="prop_gzh_qrcode">
            <image showMenuByLongpress mode="" src="{{img}}GZH.jpg"></image>
        </view>
        <view class="prop_gzh_text">
            <view>扫码关注史努比SNOOPY公众号</view>
            <view>了解更多信息</view>
        </view>
        <view class="prop_Cj_nlbz_bqNew" style="color:#000;margin-top:42rpx;">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_chooseHead" wx:if="{{propNum==3}}">
        <view class="prop_chooseHead_top">请选择你喜爱的头像</view>
        <view class="prop_chooseHead_items">
            <view bindtap="chooseHead" class="prop_chooseHead_item" data-index="{{index}}" data-item="{{item}}" wx:for="{{bannerList}}" wx:key="index">
                <view class="prop_chooseHead_item_icon">
                    <image mode="" src="{{img+item.url}}" wx:if="{{bannerActive!=index}}"></image>
                    <image mode="" src="{{img+item.chooseUrl}}" wx:else></image>
                </view>
                <view class="prop_chooseHead_item_name">{{item.name}}</view>
            </view>
        </view>
        <view bindtap="gotoSureHead" class="prop_chooseHead_sure">确认</view>
        <view class="prop_Cj_nlbz_bqNew" style="margin-top:52rpx;">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_exchange" wx:if="{{propNum==4}}">
        <view class="prop_exchange_title">
            <view>兑换确认</view>
            <view style="font-size:30rpx;">
                <text style="font-weight:500;">本次兑换需要消耗</text>{{energyNumber}}能量</view>
        </view>
        <view class="prop_exchange_buts">
            <view bindtap="closeProp" class="prop_exchange_buts_l">取消</view>
            <view bindtap="confirmProp" class="prop_exchange_buts_r">确定</view>
        </view>
        <view class="prop_Cj_nlbz_bqNew" style="margin-top:32rpx;">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_exSuccess" wx:if="{{propNum==5}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_Cj_nlbz_top" style="top:-40rpx;">
            <view class="prop_Cj_nlbz_top_text23">兑换成功！</view>
            <image class="borders" mode="" src="{{img}}newVersion/007.png"></image>
        </view>
        <view class="prop_Cj_dkcg_text"></view>
        <view class="prop_exSuccess_text">请在【我的订单】里查看订单信息</view>
        <view class="prop_exchange_buts" style="width:80%;margin:0 auto;margin-top:80rpx;">
            <view bindtap="gotoOrder" class="prop_exchange_buts_l">查看订单</view>
            <view bindtap="gotoMall" class="prop_exchange_buts_r">继续兑换</view>
        </view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_sq" wx:if="{{propNum==6}}">
        <view bindtap="closeProp" class="prop_sq_close">
            <image mode="" src="{{img}}grayClose.png"></image>
        </view>
        <view class="prop_sq_text">
            <view>您当前身份为游客</view>
            <view>请授权登陆后进入</view>
        </view>
        <view bindtap="gotoEmpower" class="prop_sq_button">前往授权</view>
        <view class="prop_Cj_nlbz_bqNew" style="margin-top:62rpx;">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_Cj_nlbz" wx:if="{{propNum==7}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_Cj_nlbz_top">
            <image class="borders" mode="" src="{{img}}newVersion/055.png"></image>
        </view>
        <view class="prop_Cj_nlbz_text">
            <view>抱歉，您的能量值不足</view>
            <view>快去任务中心获取能量值吧</view>
        </view>
        <view bindtap="gotoTask" class="prop_Cj_nlbz_button">前往任务中心</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_Cj_nlbz" wx:if="{{propNum==8&&isShowLottery10}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_Cj_nlbz_top" style="top:-32rpx;">
            <image class="borders" mode="" src="{{img}}newVersion/056.png"></image>
        </view>
        <block wx:if="{{!isLottery10}}">
            <view class="prop_Cj_nlbz_text">是否消耗{{energyNumber}}能量值兑换一次抽奖机会</view>
            <view class="prop_Cj_nlbz_buttons">
                <view bindtap="closeProp" class="prop_Cj_nlbz_buttons_cancel">取消</view>
                <view bindtap="confirmProp" class="prop_Cj_nlbz_buttons_sure" data-index="1">确认</view>
            </view>
        </block>
        <block wx:else>
            <view class="prop_Cj_nlbz_text">是否消耗{{energyNumber}}能量值兑换一次抽奖机会<view>或者消耗{{energyNumber*10}}能量值兑换十次抽奖机会</view>
            </view>
            <view class="prop_Cj_nlbz_buttons">
                <view bindtap="confirmProp" class="prop_Cj_nlbz_buttons_cancel" data-index="1">一次</view>
                <view bindtap="confirmProp" class="prop_Cj_nlbz_buttons_sure" data-index="10">十次</view>
            </view>
        </block>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_Cj_nlbz" wx:if="{{propNum==9||propNum==19||propNum==20||propNum==21||propNum==24||propNum==25}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_Cj_nlbz_top" style="top:-40rpx;">
            <view class="prop_Cj_nlbz_top_text">恭喜您！</view>
            <image mode="" src="{{img}}newVersion/007.png"></image>
        </view>
        <view class="prop_Cj_nlbz_text">
            <view wx:if="{{propNum==9||propNum==20||propNum==21}}">获得{{priceInfo.giftName}}！</view>
            <view wx:elif="{{propNum==19}}">获得{{priceInfo.giftCount}}张{{priceInfo.giftName}}！</view>
            <view wx:elif="{{propNum==24}}">获得{{exchangeEnergyNumber}}能量值!</view>
            <view wx:elif="{{propNum==25}}">{{priceMsg}}</view>
        </view>
        <view class="prop_Cj_nlbz_copy" wx:if="{{propNum==9}}">
            <view>请点击“复制链接”按钮后</view>
            <view>并前往浏览器打开领取奖励</view>
        </view>
        <view bindtap="gotoCopy" class="prop_Cj_nlbz_button" data-value="{{giftDetailInfo.linkPath}}" style="width:220rpx;margin-top:34rpx;" wx:if="{{propNum==9}}">复制链接</view>
        <view bindtap="closeProp" class="prop_Cj_nlbz_button" style="width:220rpx;margin-top:34rpx;" wx:elif="{{propNum==19||propNum==24||propNum==25}}">确认</view>
        <view bindtap="confirmProp" class="prop_Cj_nlbz_button" style="width:220rpx;margin-top:34rpx;" wx:else>去领取</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_rule" wx:if="{{propNum==10||propNum==26}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_ruleTop">
            <image mode="" src="{{img}}newVersion/070.png"></image>
        </view>
        <view class="prop_rule_box" wx:if="{{propNum==10}}">
            <view class="prop_rule_box_rule">
                <rich-text class="cnt" nodes="{{ruleText}}"></rich-text>
            </view>
        </view>
        <view class="prop_rule_box" wx:else>
            <view class="prop_rule_boxRule">
                <view>积分抽奖活动</view>
                <view>1、用户可消耗能量参与抽奖，每次抽奖消耗{{energyNumber}}能量</view>
                <view>2、抽奖礼品具体见大转盘页面，最终根据概率命中不同奖品；</view>
                <view>3、幸运大抽奖的活动规则遵循花生帮粉丝俱乐部兑换、售后通用规则，具体内容见“我的--规则说明”</view>
            </view>
        </view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_error" wx:if="{{propNum==11}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_error_top">
            <image mode="" src="{{img}}newVersion/066.png"></image>
        </view>
        <view class="prop_error_text">您的补签卡数量不足</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_error" wx:if="{{propNum==12}}">
        <view bindtap="confirmProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_error_top">
            <image mode="" src="{{img}}newVersion/065.png"></image>
        </view>
        <view class="prop_error_text">补签成功</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_error" wx:if="{{propNum==112}}">
        <view bindtap="confirmProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_error_top">
            <image mode="" src="{{img}}newVersion/007.png"></image>
        </view>
        <view class="prop_error_text">签到成功</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_Cj_nlbz" wx:if="{{propNum==13}}">
        <view bindtap="confirmProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_Cj_nlbz_top" style="top:-40rpx;">
            <view class="prop_Cj_nlbz_top_text">签到成功!</view>
            <image mode="" src="{{img}}newVersion/007.png"></image>
        </view>
        <view class="prop_Cj_nlbz_text">
            <view style="font-size:32rpx;color:#000" wx:if="{{signNum!=0}}">恭喜您获得{{signNum}}能量</view>
            <view style="font-size:20rpx;color:#bdbdbd;margin-top:8rpx">(订阅签到提醒得更多能量)</view>
        </view>
        <view bindtap="confirmTips" class="prop_Cj_nlbz_button" style="width:220rpx;margin-top:44rpx;">明日提醒</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view bindtap="closeProp" class="prop_hbs" wx:if="{{propNum==14}}">
        <view class="prop_head">
            <view catchtap="gotohb" class="prop_head_img">
                <image showMenuByLongpress class="borderR" mode="" src="{{priceInfo.giftImage||priceInfo.giftImg}}"></image>
            </view>
            <view catchtap="gotohb" class="prop_head_text">长按保存</view>
        </view>
    </view>
    <view bindtap="closeProp" class="prop_hbs" wx:if="{{propNum==15}}">
        <view class="prop_hb">
            <view catchtap="gotohb" class="prop_hb_text">长按保存海报</view>
            <image showMenuByLongpress catchtap="gotohb" class="borderR" mode="" src="{{priceInfo.giftImage||priceInfo.giftImg}}"></image>
        </view>
    </view>
    <view class="prop_virtually" wx:if="{{propNum==16}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_virtually_top">
            <view>
                <view class="prop_virtually_top_title">{{priceInfo.giftName}}</view>
                <view class="prop_rule_topBg_sub">中奖编码:{{priceInfo.winNo}}</view>
            </view>
        </view>
        <view class="prop_virtually_goods">
            <image mode="" src="{{priceInfo.giftImage||priceInfo.giftImg}}"></image>
        </view>
        <view class="prop_virtually_km">
            <view class="prop_virtually_km_icon">
                <image mode="" src="{{img}}newVersion/057.png"></image>
            </view>
            <view class="prop_virtually_km_text">{{carmiValue}}</view>
            <block wx:if="{{priceInfo.giftName=='红包封面'}}">
                <view bindtap="gotoReceive" class="prop_virtually_km_copys" data-value="{{carmiValue}}" wx:if="{{carmiValueState}}">立即领取</view>
                <view bindtap="getCardPw" class="prop_virtually_km_copy" wx:else>获取</view>
            </block>
            <block wx:else>
                <view bindtap="gotoCopy" class="prop_virtually_km_copy" data-value="{{carmiValue}}" wx:if="{{carmiValueState}}">复制</view>
                <view bindtap="getCardPw" class="prop_virtually_km_copy" wx:else>获取</view>
            </block>
        </view>
        <block wx:if="{{priceInfo.giftName!='红包封面'}}">
            <view class="prop_virtually_kms" wx:if="{{priceInfo.giftName=='高德虚拟车标'}}">
                <view class="prop_virtually_km_icon" style="margin-top:7rpx;">
                    <image mode="" src="{{img}}newVersion/058.png"></image>
                </view>
                <view class="prop_virtually_km_text_t">
                    <view style="font-size:24rpx;">兑换流程</view>
                    <view>本兑换码仅限在高德APP使用，请复制兑换码，并前往“高德APP-我的—钱包卡券—券码兑换——数字资产兑换”进行兑换使用。本兑换时间截止至2024年11月30日。</view>
                </view>
            </view>
            <view class="prop_virtually_kms" wx:else>
                <view class="prop_virtually_km_icon" style="margin-top:7rpx;">
                    <image mode="" src="{{img}}newVersion/058.png"></image>
                </view>
                <view class="prop_virtually_km_text_t">
                    <view style="font-size:24rpx;">兑换流程</view>
                    <view>1.兑换入口：https://22233.cn/1689</view>
                    <view>2.进入页面后，选择对应卡券类型后，点击立即兑换即可。</view>
                </view>
            </view>
        </block>
        <view class="prop_virtually_kms" style="background:none;" wx:else></view>
        <view bindtap="closeProp" class="prop_virtually_sub">返回</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_virtually" style="height:1046rpx;" wx:if="{{propNum==17}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_virtually_top">
            <view>
                <view class="prop_virtually_top_title">{{priceInfo.giftName}}</view>
                <view class="prop_rule_topBg_sub">中奖编码:{{priceInfo.winNo}}</view>
            </view>
        </view>
        <view class="prop_virtually_goods">
            <image mode="" src="{{priceInfo.giftImage||priceInfo.giftImg}}"></image>
        </view>
        <view class="prop_virtually_km">
            <view class="prop_virtually_km_icon">
                <image mode="" src="{{img}}newVersion/059.png"></image>
            </view>
            <view class="prop_virtually_km_text">
                <input bindinput="setUserName" maxlength="20" placeholder="输入姓名" type="text" value="{{userName}}"></input>
            </view>
        </view>
        <view class="prop_virtually_km">
            <view class="prop_virtually_km_icon">
                <image mode="" src="{{img}}newVersion/060.png"></image>
            </view>
            <view class="prop_virtually_km_text">
                <input bindinput="setUserPhone" maxlength="11" placeholder="输入手机号码" type="text" value="{{userPhone}}"></input>
            </view>
        </view>
        <view class="prop_virtually_km">
            <view class="prop_virtually_km_icon">
                <image mode="" src="{{img}}newVersion/061.png"></image>
            </view>
            <view class="prop_virtually_km_texts">
                <view bindtap="showCity" class="km_text_p" data-index="0">{{province}}</view>
                <view class="km_text_ps">省</view>
                <view class="km_text_psIcon">
                    <image alt="" src="{{img}}newVersion/063.png"></image>
                </view>
                <view bindtap="showCity" class="km_text_p" data-index="1">{{city}}</view>
                <view class="km_text_ps">市</view>
                <view class="km_text_psIcon">
                    <image alt="" src="{{img}}newVersion/063.png"></image>
                </view>
                <view bindtap="showCity" class="km_text_p" data-index="2">{{area}}</view>
                <view class="km_text_ps">区</view>
                <view class="km_text_psIcon">
                    <image alt="" src="{{img}}newVersion/063.png"></image>
                </view>
            </view>
        </view>
        <view class="prop_virtually_kms" style="height:128rpx;">
            <view class="prop_virtually_km_icon" style="margin-top:7rpx;">
                <image mode="" src="{{img}}newVersion/062.png"></image>
            </view>
            <view class="prop_virtually_km_text_area">
                <textarea bindinput="setDetailAddress" placeholder="输入详细地址" value="{{detailAddress}}"></textarea>
            </view>
        </view>
        <view bindtap="submitUserInfo" class="prop_virtually_sub">提交</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_Choose" wx:if="{{cityProp}}">
        <van-picker showToolbar bind:cancel="onCancel" bind:change="onChange" bind:confirm="onConfirm" class="prop_Choose_b" columns="{{columns}}"></van-picker>
    </view>
    <view class="prop_error" wx:if="{{propNum==18}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_error_tops">
            <view class="prop_error_tops_text">很遗憾~</view>
            <image mode="" src="{{img}}newVersion/064.png"></image>
        </view>
        <view class="prop_Cj_nlbz_text">
            <view style="font-size:30rpx;">您未中奖！</view>
        </view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_Cj_dkcg" wx:if="{{propNum==22}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_Cj_nlbz_top" style="top:-40rpx;">
            <view class="prop_Cj_nlbz_top_text2">恭喜打卡成功！</view>
            <image class="borders" mode="" src="{{img}}newVersion/007.png"></image>
        </view>
        <view class="prop_Cj_dkcg_text">获得{{energyNumber}}能量值</view>
        <view class="prop_Cj_dkcg_name">打卡门店：{{storeName}}</view>
        <view bindtap="closeProp" class="prop_Cj_dkcg_button">确认</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_ClockIn" wx:if="{{propNum==23}}">
        <view class="prop_ClockIn_title">{{clockMsg}}</view>
        <view bindtap="closeProp" class="prop_ClockIn_button">确定</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_FWgetNL2s" wx:if="{{propNum==27}}">
        <view bindtap="confirmProp" class="prop_FWgetNL2_img22">
            <image mode="" src="https://cdn.omnimkt.com/2025/snoppy/images/kv.png?v={{date}}"></image>
        </view>
        <view bindtap="closeProp" class="prop_FWgetNL2_close">
            <image mode="" src="{{img}}closeProp.png"></image>
        </view>
    </view>
    <view class="prop_tenPro" wx:if="{{propNum==28}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}draw/close.png"></image>
        </view>
        <view class="prop_Cj_nlbz_top" style="top:-40rpx;">
            <view class="prop_Cj_nlbz_top_text">恭喜您！</view>
            <image class="borders" mode="" src="{{img}}draw/gx.png"></image>
        </view>
        <view class="prop_tenPro_items">
            <view class="prop_tenPro_item" wx:for="{{newDrawRaffieInfo}}" wx:key="index">
                <view class="prop_tenPro_item_top">
                    <image mode="" src="{{item.winData[0].giftImage}}" wx:if="{{item.winData}}"></image>
                    <image mode="" src="https://dm-assets.supercarrier8.com/wobei/nopriceNew.png?123123" wx:else></image>
                </view>
                <view class="prop_tenPro_item_title">{{item.winData?item.winData[0].awardsNameNew:'谢谢参与'}}</view>
            </view>
        </view>
        <view bindtap="gotoGoods" class="prop_tenPro_buts" wx:if="{{!allThanks}}">点击前往填写收货信息</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_Cj_dkcg2" wx:if="{{propNum==29}}">
        <view bindtap="confirmProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_Cj_nlbz_top" style="top:-40rpx;">
            <view class="prop_Cj_nlbz_top_text2">申请已完成！</view>
            <image class="borders" mode="" src="{{img}}newVersion/007.png"></image>
        </view>
        <view class="prop_Cj_dkcg_text"></view>
        <view class="prop_Cj_dkcg_name">中奖名单将于<text style="font-weight:bold;">{{timeMsg}}</text>公布</view>
        <view bindtap="confirmProp" class="prop_Cj_dkcg_button">确认</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_Cj_dkcg3" wx:if="{{propNum==30}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_Cj_nlbz_top" style="top:-40rpx;">
            <view class="prop_Cj_nlbz_top_text23">联系我们！</view>
            <image class="borders" mode="" src="{{img}}newVersion/007.png"></image>
        </view>
        <view class="prop_Cj_dkcg_text"></view>
        <view class="prop_Cj_dkcg_names">
            <view style="font-weight:bold;">{{emailValue}}</view>
            <view bindtap="gotoCopy" class="prop_virtually_km_copys2" data-value="{{emailValue}}"></view>
        </view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_rule" wx:if="{{propNum==31}}">
        <view bindtap="closeProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_ruleTop">
            <view class="prop_Cj_nlbz_top_ruletext">能量攻略</view>
            <image mode="" src="{{img}}newVersion/028.png"></image>
        </view>
        <view class="prop_energy_box">
            <view class="prop_energy_box_item" wx:for="{{energyList}}" wx:key="index">
                <view>{{item.label}}</view>
                <view class="energy_tag">
                    <text class="energy_tag_bold_num" wx:if="{{item.isNum}}">{{item.value}} </text>
                    <text wx:else>{{item.value}}</text>能量</view>
            </view>
        </view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
    <view class="prop_Cj_nlbz" wx:if="{{propNum==32}}">
        <view bindtap="confirmProp" class="prop_Cj_nlbz_close">
            <image mode="" src="{{img}}newVersion/008.png"></image>
        </view>
        <view class="prop_Cj_nlbz_top" style="top:-40rpx;">
            <view class="prop_Cj_nlbz_top_text">额外奖励!</view>
            <image mode="" src="{{img}}newVersion/007.png"></image>
        </view>
        <view class="prop_Cj_nlbz_text">
            <view style="font-size:32rpx;color:#000" wx:if="{{signNum!=0}}">恭喜您获得{{signNum}}能量</view>
            <view style="font-size:20rpx;color:#bdbdbd;margin-top:8rpx">(订阅签到提醒得更多能量)</view>
        </view>
        <view bindtap="confirmTips" class="prop_Cj_nlbz_button" style="width:220rpx;margin-top:44rpx;">明日提醒</view>
        <view class="prop_Cj_nlbz_bq">
            <view>©2025 Peanuts Worldwide LLC</view>
            <view>www.peanuts.com</view>
        </view>
    </view>
</view>
