<view class="maskContainer" hoverClass="none" hoverStopPropagation="false" wx:if="{{open}}">
    <view class="maskBox maskImgCenter" hoverClass="none" hoverStopPropagation="false">
        <image binderror="" bindload="" class="maskBg" lazyLoad="false" src="{{imgUrl}}75/rule/bg.png?v={{imgVersion}}"></image>
        <scroll-view scrollY class="sView">
            <image binderror="" bindload="" class="text" lazyLoad="false" mode="widthFix" src="{{imgUrl}}75/rule/text.png?v={{imgVersion}}"></image>
        </scroll-view>
        <view catch:tap="handleClose" class="close"></view>
    </view>
</view>
