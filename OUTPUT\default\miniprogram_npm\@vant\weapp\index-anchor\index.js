Object.defineProperty(exports, "__esModule", {
  value: !0
});
var e = require("../common/utils"),
  o = require("../common/component"),
  t = require("../common/relation");
(0, o.VantComponent)({
  relation: (0, t.useParent)("index-bar"),
  props: {
    useSlot: Boolean,
    index: null
  },
  data: {
    active: !1,
    wrapperStyle: "",
    anchorStyle: ""
  },
  methods: {
    scrollIntoView: function(o) {
      var t = this;
      (0, e.getRect)(this, ".van-index-anchor-wrapper").then((function(e) {
        wx.pageScrollTo({
          duration: 0,
          scrollTop: o + e.top - t.parent.data.stickyOffsetTop
        })
      }))
    }
  }
});