@import "..\common\index.wxss";

.van-loading {
    -webkit-align-items: center;
    align-items: center;
    color: var(--loading-spinner-color,#c8c9cc);
    display: -webkit-inline-flex;
    display: inline-flex;
    -webkit-justify-content: center;
    justify-content: center
}

.van-loading__spinner {
    -webkit-animation: van-rotate var(--loading-spinner-animation-duration,.8s) linear infinite;
    animation: van-rotate var(--loading-spinner-animation-duration,.8s) linear infinite;
    box-sizing: border-box;
    height: var(--loading-spinner-size,30px);
    max-height: 100%;
    max-width: 100%;
    position: relative;
    width: var(--loading-spinner-size,30px)
}

.van-loading__spinner--spinner {
    -webkit-animation-timing-function: steps(12);
    animation-timing-function: steps(12)
}

.van-loading__spinner--circular {
    border: 1px solid transparent;
    border-radius: 100%;
    border-top-color: initial
}

.van-loading__text {
    color: var(--loading-text-color,#969799);
    font-size: var(--loading-text-font-size,14px);
    line-height: var(--loading-text-line-height,20px);
    margin-left: var(--padding-xs,8px)
}

.van-loading__text:empty {
    display: none
}

.van-loading--vertical {
    -webkit-flex-direction: column;
    flex-direction: column
}

.van-loading--vertical .van-loading__text {
    margin: var(--padding-xs,8px) 0 0
}

.van-loading__dot {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.van-loading__dot:before {
    background-color: currentColor;
    border-radius: 40%;
    content: " ";
    display: block;
    height: 25%;
    margin: 0 auto;
    width: 2px
}

.van-loading__dot:first-of-type {
    opacity: 1;
    -webkit-transform: rotate(30deg);
    transform: rotate(30deg)
}

.van-loading__dot:nth-of-type(2) {
    opacity: .9375;
    -webkit-transform: rotate(60deg);
    transform: rotate(60deg)
}

.van-loading__dot:nth-of-type(3) {
    opacity: .875;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg)
}

.van-loading__dot:nth-of-type(4) {
    opacity: .8125;
    -webkit-transform: rotate(120deg);
    transform: rotate(120deg)
}

.van-loading__dot:nth-of-type(5) {
    opacity: .75;
    -webkit-transform: rotate(150deg);
    transform: rotate(150deg)
}

.van-loading__dot:nth-of-type(6) {
    opacity: .6875;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.van-loading__dot:nth-of-type(7) {
    opacity: .625;
    -webkit-transform: rotate(210deg);
    transform: rotate(210deg)
}

.van-loading__dot:nth-of-type(8) {
    opacity: .5625;
    -webkit-transform: rotate(240deg);
    transform: rotate(240deg)
}

.van-loading__dot:nth-of-type(9) {
    opacity: .5;
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg)
}

.van-loading__dot:nth-of-type(10) {
    opacity: .4375;
    -webkit-transform: rotate(300deg);
    transform: rotate(300deg)
}

.van-loading__dot:nth-of-type(11) {
    opacity: .375;
    -webkit-transform: rotate(330deg);
    transform: rotate(330deg)
}

.van-loading__dot:nth-of-type(12) {
    opacity: .3125;
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn)
}

@-webkit-keyframes van-rotate {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes van-rotate {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}