.empower {
    height: 100vh;
    overflow: hidden;
    width: 100%
}

.empowerIcon {
    height: 195rpx;
    margin: 93rpx auto 0;
    width: 195rpx
}

.empowerText {
    color: #202020;
    font-size: 40rpx;
    font-weight: 700;
    margin-top: 33rpx
}

.empowerText,.empower_button {
    font-family: Source <PERSON> Sans CN;
    text-align: center
}

.empower_button {
    background: #1cac4e;
    border-radius: 43rpx;
    color: #fff;
    font-size: 36rpx;
    font-weight: 500;
    height: 86rpx;
    line-height: 86rpx;
    margin: 60rpx auto 0;
    position: relative;
    width: 526rpx
}

.buttonPhone {
    background: none;
    height: 86rpx!important;
    left: 0;
    position: absolute;
    top: 0;
    width: 526rpx!important
}

.empower_read {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    margin: 32rpx auto 0;
    width: 526rpx
}

.empower_read_Icon {
    height: 24rpx;
    margin-right: 10rpx;
    width: 24rpx
}

.empower_read_text {
    color: #000;
    font-family: Source <PERSON>;
    font-size: 20rpx;
    font-weight: 400
}
