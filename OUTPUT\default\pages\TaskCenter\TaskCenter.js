var t, e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  a = require("../../@babel/runtime/helpers/asyncToGenerator"),
  n = require("../../83F188C3549B04BFE597E0C403C30D65.js"),
  r = require("../../A4000F75549B04BFC2666772D6B30D65.js"),
  o = require("../../6F218526549B04BF0947ED2133340D65.js"),
  s = (t = require("../../87624F60549B04BFE10427674BE30D65.js")) && t.__esModule ? t : {
    default: t
  },
  i = require("../../8F86AFB2549B04BFE9E0C7B593D30D65.js");
var u = getApp();
Page({
  data: {
    img: u.globalData.img,
    taskList: [],
    userInfo: {},
    propState: !1,
    propNum: 0,
    priceMsg: "",
    taskButtonState: !1,
    ruleText: "",
    isInviteState: !1,
    configRubric: ""
  },
  gotoScan: function() {
    return a(e().mark((function t() {
      var a;
      return e().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return t.next = 2, (0, o.scanCode)();
          case 2:
            a = t.sent, wx.navigateTo({
              url: "/pages/SecurityCheck/SecurityCheck?q=" + encodeURIComponent(a.result)
            });
          case 4:
          case "end":
            return t.stop()
        }
      }), t)
    })))()
  },
  showRule: function() {
    this.setData({
      propState: !0,
      propNum: 10
    })
  },
  onLoad: function(t) {
    console.log("任务中心options", t)
  },
  closeProp: function() {
    this.setData({
      propState: !1
    }), 10 != this.data.propNum && this.getList()
  },
  gotoSignIn: function() {
    wx.navigateTo({
      url: "/pages/signIn/signIn"
    })
  },
  gotoShare: function(t) {
    var n = this;
    return a(e().mark((function a() {
      var r;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            if (r = t.currentTarget.dataset.item, !n.data.taskButtonState) {
              e.next = 3;
              break
            }
            return e.abrupt("return");
          case 3:
            return n.setData({
              taskButtonState: !0
            }), e.next = 6, n.getTaskOk(r.id);
          case 6:
          case "end":
            return e.stop()
        }
      }), a)
    })))()
  },
  gotoShareReg: function() {
    console.log("点击分享按钮"), console.log("fetchData存储数据", s.default), console.log("openid", s.default.data.openid), console.log("用户id", s.default.data.userInfo.id)
  },
  gotoStore: function(t) {
    var n = this;
    return a(e().mark((function a() {
      var r;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            if (!n.data.taskButtonState) {
              e.next = 2;
              break
            }
            return e.abrupt("return");
          case 2:
            return n.setData({
              taskButtonState: !0
            }), r = t.currentTarget.dataset.item, console.log(r), e.next = 7, n.getTaskOk(r.id);
          case 7:
            wx.navigateTo({
              url: "/pages/storeClockIn/storeClockIn"
            });
          case 8:
          case "end":
            return e.stop()
        }
      }), a)
    })))()
  },
  getTaskOk: function(t) {
    return new Promise((function(e, a) {
      (0, r.setTaskOk)({
        taskId: t,
        userId: s.default.data.registerUserInfo.id,
        mode: "1"
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  getCom: function(t) {
    return new Promise((function(e, a) {
      (0, r.taskComplete)({
        taskId: t,
        userId: s.default.data.registerUserInfo.id
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  gotoLook: function(t) {
    var n = this;
    return a(e().mark((function r() {
      var o;
      return e().wrap((function(r) {
        for (;;) switch (r.prev = r.next) {
          case 0:
            if (console.log("this.data.taskButtonState", n.data.taskButtonState), !n.data.taskButtonState) {
              r.next = 3;
              break
            }
            return r.abrupt("return");
          case 3:
            if (n.setData({
                taskButtonState: !0
              }), console.log(t.currentTarget.dataset.item.linkType), 1 != (o = t.currentTarget.dataset.item).linkType) {
              r.next = 12;
              break
            }
            return r.next = 9, n.getTaskOk(o.id);
          case 9:
            wx.navigateTo({
              url: "/pages/WebUrl/WebUrl?url=" + o.linkPath
            }), r.next = 13;
            break;
          case 12:
            2 == o.linkType && (o.miniProAppId ? wx.navigateToMiniProgram({
              appId: o.miniProAppId,
              path: o.miniProPagePath || o.miniProBackupPath,
              extraData: {},
              envVersion: "release",
              success: function() {
                var t = a(e().mark((function t(a) {
                  return e().wrap((function(t) {
                    for (;;) switch (t.prev = t.next) {
                      case 0:
                        return console.log("res", a), t.next = 3, n.getTaskOk(o.id);
                      case 3:
                        n.setData({
                          taskButtonState: !1
                        });
                      case 4:
                      case "end":
                        return t.stop()
                    }
                  }), t)
                })));
                return function(e) {
                  return t.apply(this, arguments)
                }
              }(),
              complete: function(t) {
                n.setData({
                  taskButtonState: !1
                })
              }
            }) : wx.navigateToMiniProgram({
              shortLink: o.miniProPagePath || o.miniProBackupPath,
              success: function() {
                var t = a(e().mark((function t(a) {
                  return e().wrap((function(t) {
                    for (;;) switch (t.prev = t.next) {
                      case 0:
                        return console.log("res", a), t.next = 3, n.getTaskOk(o.id);
                      case 3:
                        n.setData({
                          taskButtonState: !1
                        });
                      case 4:
                      case "end":
                        return t.stop()
                    }
                  }), t)
                })));
                return function(e) {
                  return t.apply(this, arguments)
                }
              }(),
              complete: function(t) {
                n.setData({
                  taskButtonState: !1
                })
              }
            }));
          case 13:
          case "end":
            return r.stop()
        }
      }), r)
    })))()
  },
  gotoReceivePrice: function(t) {
    var n = this;
    return a(e().mark((function a() {
      var r, s;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return console.log(t.currentTarget.dataset.item), r = t.currentTarget.dataset.item, (0, o.loadingOpen)(), e.next = 5, n.getCom(r.id);
          case 5:
            if (s = e.sent, console.log("compDataInfo", s), (0, o.loadingClose)(), 200 != s.code) {
              e.next = 12;
              break
            }
            r.awardsValue ? n.setData({
              propState: !0,
              propNum: 25,
              priceMsg: "完成".concat(r.taskName, "，获得").concat(r.awardsValue, "能量值！"),
              taskButtonState: !1
            }) : (n.setData({
              taskButtonState: !1
            }), n.getList()), e.next = 16;
            break;
          case 12:
            if (21210108 != s.code) {
              e.next = 15;
              break
            }
            return (0, o.toastModel)("该任务奖励每人只能领取一次"), e.abrupt("return");
          case 15:
            (0, o.toastModel)(s.message);
          case 16:
          case "end":
            return e.stop()
        }
      }), a)
    })))()
  },
  onReady: function() {},
  miniappLogin: function(t) {
    return new Promise((function(e, a) {
      (0, r.miniappLogin)({
        authorizerAppid: u.globalData.appId,
        jsCode: t
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  getToken: function(t) {
    return new Promise((function(e, a) {
      (0, r.wechatOpenid)({
        serviceSign: t.serviceSign,
        openid: t.openid,
        appId: u.globalData.appId,
        appType: 2
      }).then((function(t) {
        s.default.data.registerUserInfo = t.data, e(t)
      }))
    }))
  },
  onShow: function() {
    var t = this;
    return a(e().mark((function a() {
      var r, u, c;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            if (s.default.data.token) {
              e.next = 15;
              break
            }
            return e.next = 3, (0, o.getCode)();
          case 3:
            return r = e.sent, e.next = 6, t.miniappLogin(r);
          case 6:
            return u = e.sent, console.log("获取用户openid", u), s.default.data.openid = u.data.openid, console.log(u.data), e.next = 12, t.getToken(u.data);
          case 12:
            c = e.sent, console.log("获取用户token", c), s.default.data.token = c.data.token;
          case 15:
            return e.next = 17, (0, n.getUser)();
          case 17:
            t.setData({
              taskButtonState: !1
            }), console.log("onshow111111", t.data.taskButtonState), t.getRule(), 1 == (0, i.get)("wb_ruleProp") && (0, i.set)("wb_rulePropState", 1), t.getList(), t.getConfig();
          case 23:
          case "end":
            return e.stop()
        }
      }), a)
    })))()
  },
  getConfig: function() {
    var t = this;
    (0, r.getInviteConfig)({}).then((function(e) {
      200 == e.code && 1 == e.data.openStatus ? t.setData({
        isInviteState: !0,
        configRubric: e.data.configRubric
      }) : t.setData({
        isInviteState: !1
      })
    }))
  },
  getRule: function() {
    this.setData({
      ruleText: '\n            <p>1、浏览微信推文：每月微信文章仅首次浏览得能量值。</p>\n            <p>2、浏览视频：每月视频仅首次浏览得能量值。</p>\n            <p>3、邀请好友：邀请不同好友成功完成注册得能量，每月上限为8次。</p>\n            <p>4、能量值自获取之日起有效期为1年；能量值有效期</p>\n            <p style="padding-left:18px">届满后，系统自动清除。</p>'
    })
  },
  userTaskInfo: function(t) {
    return new Promise((function(n, o) {
      (0, r.getUsercompleteFlag)({
        taskId: t
      }).then(function() {
        var t = a(e().mark((function t(a) {
          return e().wrap((function(t) {
            for (;;) switch (t.prev = t.next) {
              case 0:
                n(a);
              case 1:
              case "end":
                return t.stop()
            }
          }), t)
        })));
        return function(e) {
          return t.apply(this, arguments)
        }
      }())
    }))
  },
  getList: function() {
    var t = this;
    return a(e().mark((function i() {
      return e().wrap((function(i) {
        for (;;) switch (i.prev = i.next) {
          case 0:
            return i.next = 2, (0, n.getUser)();
          case 2:
            t.setData({
              userInfo: s.default.data.userInfo
            }), (0, o.loadingOpen)(), (0, r.getUserTaskList)({
              UserID: s.default.data.registerUserInfo.id,
              UserType: s.default.data.registerUserInfo.userType
            }).then(function() {
              var n = a(e().mark((function a(n) {
                var s, i, u, c;
                return e().wrap((function(e) {
                  for (;;) switch (e.prev = e.next) {
                    case 0:
                      (0, o.loadingClose)(), s = 0;
                    case 2:
                      if (!(s < n.data.length)) {
                        e.next = 15;
                        break
                      }
                      if ("i_0002" != (i = n.data[s]).indexNo && "i_0003" != i.indexNo) {
                        e.next = 11;
                        break
                      }
                      return e.next = 7, (0, r.getCompleteFlag)({
                        taskId: n.data[s].id
                      });
                    case 7:
                      (u = e.sent).data && 1 == n.data[s].status ? n.data[s].taskInfo = 0 : (n.data[s].taskInfo = "1" == u.data ? 1 : 0, n.data[s].status = 0), e.next = 12;
                      break;
                    case 11:
                      n.data[s].taskInfo = 0;
                    case 12:
                      s++, e.next = 2;
                      break;
                    case 15:
                      console.log("res.data", n.data), c = n.data, t.setData({
                        taskList: c
                      }), console.log("taskList", t.data.taskList);
                    case 19:
                    case "end":
                      return e.stop()
                  }
                }), a)
              })));
              return function(t) {
                return n.apply(this, arguments)
              }
            }());
          case 5:
          case "end":
            return i.stop()
        }
      }), i)
    })))()
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return {
      title: this.data.configRubric,
      path: "/pages/empower/empower?inviteId=".concat(s.default.data.userInfo.id, "&inviteOpenId=").concat(s.default.data.openid)
    }
  }
});