var t = function(t, e, a) {
    if (a || 2 === arguments.length)
      for (var n, i = 0, o = e.length; i < o; i++) !n && i in e || (n || (n = Array.prototype.slice.call(e, 0, i)), n[i] = e[i]);
    return t.concat(n || Array.prototype.slice.call(e))
  },
  e = function(t) {
    return t && t.__esModule ? t : {
      default: t
    }
  };
Object.defineProperty(exports, "__esModule", {
  value: !0
});
var a, n = require("../common/component"),
  i = require("./utils"),
  o = e(require("../toast/toast")),
  r = require("../common/utils"),
  s = (0, i.getToday)().getTime(),
  l = (a = (0, i.getToday)(), new Date(a.getFullYear(), a.getMonth() + 6, a.getDate()).getTime()),
  c = function(t) {
    return t instanceof Date ? t.getTime() : t
  };
(0, n.VantComponent)({
  props: {
    title: {
      type: String,
      value: "日期选择"
    },
    color: String,
    show: {
      type: Boolean,
      observer: function(t) {
        t && (this.initRect(), this.scrollIntoView())
      }
    },
    formatter: null,
    confirmText: {
      type: String,
      value: "确定"
    },
    confirmDisabledText: {
      type: String,
      value: "确定"
    },
    rangePrompt: String,
    showRangePrompt: {
      type: Boolean,
      value: !0
    },
    defaultDate: {
      type: null,
      observer: function(t) {
        this.setData({
          currentDate: t
        }), this.scrollIntoView()
      }
    },
    allowSameDay: Boolean,
    type: {
      type: String,
      value: "single",
      observer: "reset"
    },
    minDate: {
      type: Number,
      value: s
    },
    maxDate: {
      type: Number,
      value: l
    },
    position: {
      type: String,
      value: "bottom"
    },
    rowHeight: {
      type: null,
      value: i.ROW_HEIGHT
    },
    round: {
      type: Boolean,
      value: !0
    },
    poppable: {
      type: Boolean,
      value: !0
    },
    showMark: {
      type: Boolean,
      value: !0
    },
    showTitle: {
      type: Boolean,
      value: !0
    },
    showConfirm: {
      type: Boolean,
      value: !0
    },
    showSubtitle: {
      type: Boolean,
      value: !0
    },
    safeAreaInsetBottom: {
      type: Boolean,
      value: !0
    },
    closeOnClickOverlay: {
      type: Boolean,
      value: !0
    },
    maxRange: {
      type: null,
      value: null
    },
    minRange: {
      type: Number,
      value: 1
    },
    firstDayOfWeek: {
      type: Number,
      value: 0
    },
    readonly: Boolean
  },
  data: {
    subtitle: "",
    currentDate: null,
    scrollIntoView: ""
  },
  created: function() {
    this.setData({
      currentDate: this.getInitialDate(this.data.defaultDate)
    })
  },
  mounted: function() {
    !this.data.show && this.data.poppable || (this.initRect(), this.scrollIntoView())
  },
  methods: {
    reset: function() {
      this.setData({
        currentDate: this.getInitialDate()
      }), this.scrollIntoView()
    },
    initRect: function() {
      var t = this;
      null != this.contentObserver && this.contentObserver.disconnect();
      var e = this.createIntersectionObserver({
        thresholds: [0, .1, .9, 1],
        observeAll: !0
      });
      this.contentObserver = e, e.relativeTo(".van-calendar__body"), e.observe(".month", (function(e) {
        e.boundingClientRect.top <= e.relativeRect.top && t.setData({
          subtitle: (0, i.formatMonthTitle)(e.dataset.date)
        })
      }))
    },
    limitDateRange: function(t, e, a) {
      return void 0 === e && (e = null), void 0 === a && (a = null), e = e || this.data.minDate, a = a || this.data.maxDate, -1 === (0, i.compareDay)(t, e) ? e : 1 === (0, i.compareDay)(t, a) ? a : t
    },
    getInitialDate: function(t) {
      var e = this;
      void 0 === t && (t = null);
      var a = this.data,
        n = a.type,
        o = a.minDate,
        r = a.maxDate,
        s = a.allowSameDay,
        l = (0, i.getToday)().getTime();
      if ("range" === n) {
        Array.isArray(t) || (t = []);
        var u = t || [],
          h = u[0],
          m = u[1],
          p = this.limitDateRange(h || l, o, (0, i.getPrevDay)(new Date(r)).getTime()),
          y = c(m || l);
        return [p, this.limitDateRange(y, s ? y : (0, i.getNextDay)(new Date(o)).getTime())]
      }
      return "multiple" === n ? Array.isArray(t) ? t.map((function(t) {
        return e.limitDateRange(t)
      })) : [this.limitDateRange(l)] : (t && !Array.isArray(t) || (t = l), this.limitDateRange(t))
    },
    scrollIntoView: function() {
      var t = this;
      (0, r.requestAnimationFrame)((function() {
        var e = t.data,
          a = e.currentDate,
          n = e.type,
          o = e.show,
          r = e.poppable,
          s = e.minDate,
          l = e.maxDate,
          c = "single" === n ? a : a[0];
        !c || !o && r || (0, i.getMonths)(s, l).some((function(e, a) {
          return 0 === (0, i.compareMonth)(e, c) && (t.setData({
            scrollIntoView: "month".concat(a)
          }), !0)
        }))
      }))
    },
    onOpen: function() {
      this.$emit("open")
    },
    onOpened: function() {
      this.$emit("opened")
    },
    onClose: function() {
      this.$emit("close")
    },
    onClosed: function() {
      this.$emit("closed")
    },
    onClickDay: function(e) {
      if (!this.data.readonly) {
        var a = e.detail.date,
          n = this.data,
          o = n.type,
          r = n.currentDate,
          s = n.allowSameDay;
        if ("range" === o) {
          var l = r[0],
            u = r[1];
          if (l && !u) {
            var h = (0, i.compareDay)(a, l);
            if (1 === h) {
              var m = this.selectComponent(".month").data.days;
              m.some((function(t, e) {
                var n = "disabled" === t.type && c(l) < c(t.date) && c(t.date) < c(a);
                return n && (a = m[e - 1].date), n
              })), this.select([l, a], !0)
            } else - 1 === h ? this.select([a, null]) : s && this.select([a, a], !0)
          } else this.select([a, null])
        } else if ("multiple" === o) {
          var p;
          if (r.some((function(t, e) {
              var n = 0 === (0, i.compareDay)(t, a);
              return n && (p = e), n
            }))) {
            var y = r.splice(p, 1);
            this.setData({
              currentDate: r
            }), this.unselect(y)
          } else this.select(t(t([], r, !0), [a], !1))
        } else this.select(a, !0)
      }
    },
    unselect: function(t) {
      var e = t[0];
      e && this.$emit("unselect", (0, i.copyDates)(e))
    },
    select: function(t, e) {
      if (e && "range" === this.data.type && !this.checkRange(t)) return void(this.data.showConfirm ? this.emit([t[0], (0, i.getDayByOffset)(t[0], this.data.maxRange - 1)]) : this.emit(t));
      this.emit(t), e && !this.data.showConfirm && this.onConfirm()
    },
    emit: function(t) {
      this.setData({
        currentDate: Array.isArray(t) ? t.map(c) : c(t)
      }), this.$emit("select", (0, i.copyDates)(t))
    },
    checkRange: function(t) {
      var e = this.data,
        a = e.maxRange,
        n = e.rangePrompt,
        r = e.showRangePrompt;
      return !(a && (0, i.calcDateNum)(t) > a) || (r && (0, o.default)({
        context: this,
        message: n || "选择天数不能超过 ".concat(a, " 天")
      }), this.$emit("over-range"), !1)
    },
    onConfirm: function() {
      var t = this;
      ("range" !== this.data.type || this.checkRange(this.data.currentDate)) && wx.nextTick((function() {
        t.$emit("confirm", (0, i.copyDates)(t.data.currentDate))
      }))
    },
    onClickSubtitle: function(t) {
      this.$emit("click-subtitle", t)
    }
  }
});