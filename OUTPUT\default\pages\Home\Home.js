var e, t = require("../../@babel/runtime/helpers/defineProperty"),
  n = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  a = require("../../@babel/runtime/helpers/asyncToGenerator"),
  r = require("../../6F218526549B04BF0947ED2133340D65.js"),
  i = require("../../A4000F75549B04BFC2666772D6B30D65.js"),
  o = p(require("../../87624F60549B04BFE10427674BE30D65.js")),
  s = require("../../83F188C3549B04BFE597E0C403C30D65.js"),
  u = require("../../8F86AFB2549B04BFE9E0C7B593D30D65.js"),
  c = p(require("../../B6135D02549B04BFD0753505DD930D65.js"));

function p(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
var g = getApp();
Page((t(e = {
  data: {
    img: g.globalData.img,
    imgNew: g.globalData.imgNew,
    indicatorDots: !0,
    autoplay: !0,
    interval: 3e3,
    duration: 500,
    circular: !0,
    bannerImgs: [],
    peanutFamily: [],
    propState: !1,
    propNum: 3,
    userInfo: {},
    signSwitch: 0,
    ggState: !0,
    webUrlSrc: "",
    showsws: !1,
    currentIndex: 0
  },
  onNext: function() {
    this.data.currentIndex < this.data.bannerImgs.length - 1 ? this.setData({
      currentIndex: this.data.currentIndex += 1
    }) : this.setData({
      currentIndex: 0
    })
  },
  onPrev: function() {
    0 == this.data.currentIndex ? this.setData({
      currentIndex: this.data.bannerImgs.length - 1
    }) : this.setData({
      currentIndex: this.data.currentIndex -= 1
    })
  },
  gotoHsSws: function() {
    wx.navigateTo({
      url: "/pages/hssws/hssws"
    })
  },
  gotoTaskCenter: function() {
    wx.navigateTo({
      url: "/pages/TaskCenter/TaskCenter"
    })
  },
  getBanner: function() {
    return new Promise((function(e, t) {
      (0, i.getBannerList)({
        sort: "priority",
        flag: 1,
        pageIndex: 1,
        pageSize: 100
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  confirmProp: function() {
    if (this.setData({
        propState: !1
      }), 27 != this.data.propNum);
    else {
      var e = this.data.wbHomePopupData.find((function(e) {
          return "webUrl" == e.itemName
        })),
        t = this.data.wbHomePopupData.find((function(e) {
          return "appid" == e.itemName
        })),
        n = this.data.wbHomePopupData.find((function(e) {
          return "path" == e.itemName
        })),
        a = this.data.wbHomePopupData.find((function(e) {
          return "shortLink" == e.itemName
        }));
      "webUrl" !== e.itemValue ? wx.navigateTo({
        url: "/pages/WebUrl/WebUrl?url=" + e.itemValue
      }) : "appid" !== t.itemValue && "path" !== n.itemValue ? wx.navigateToMiniProgram({
        appId: t.itemValue,
        path: n.itemValue
      }) : "appid" === t.itemValue && "path" !== n.itemValue ? wx.navigateTo({
        url: n.itemValue
      }) : "shortLink" !== a.itemValue && wx.navigateToMiniProgram({
        shortLink: a.itemValue
      })
    }
  },
  closeProp: function() {
    this.setData({
      propState: !1
    })
  },
  gotoSureHead: function(e) {
    this.setData({
      propState: !1
    }), Promise.all([this.setName(e.detail), this.setHeadImage(e.detail)]).then(function() {
      var e = a(n().mark((function e(t) {
        return n().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return e.next = 2, (0, s.getUser)();
            case 2:
            case "end":
              return e.stop()
          }
        }), e)
      })));
      return function(t) {
        return e.apply(this, arguments)
      }
    }())
  },
  setName: function(e) {
    return new Promise((function(t, n) {
      (0, i.updateName)({
        name: e.name
      }).then((function(e) {
        t(e)
      }))
    }))
  },
  setHeadImage: function(e) {
    var t = this;
    return new Promise((function(n, a) {
      (0, i.updateHeadImage)({
        headImage: t.data.img + e.chooseUrl
      }).then((function(e) {
        n(e)
      }))
    }))
  },
  gotoZp: function() {
    var e = this;
    return a(n().mark((function t() {
      var a;
      return n().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            if (e.data.userInfo.mobile) {
              t.next = 3;
              break
            }
            return e.setData({
              propState: !0,
              propNum: 6
            }), t.abrupt("return");
          case 3:
            return t.next = 5, e.dictionaryItems();
          case 5:
            a = t.sent, console.log("IsZpOpenState", a), 0 == a.data[0].id ? (0, r.toastModel)("抽奖功能已关闭") : wx.navigateTo({
              url: "/pages/drawReward/drawReward"
            });
          case 8:
          case "end":
            return t.stop()
        }
      }), t)
    })))()
  },
  gotoSignIn: function() {
    this.data.userInfo.mobile ? this.data.signSwitch ? wx.navigateTo({
      url: "/pages/signIn/signIn"
    }) : (0, r.toastModel)("签到功能已关闭") : this.setData({
      propState: !0,
      propNum: 6
    })
  },
  configQuery: function() {
    return new Promise((function(e, t) {
      (0, i.configQuery)({}).then((function(t) {
        e(t)
      }))
    }))
  },
  gotoStoreClockIn: function() {
    var e = this;
    return a(n().mark((function t() {
      return n().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            if (e.data.userInfo.mobile) {
              t.next = 3;
              break
            }
            return e.setData({
              propState: !0,
              propNum: 6
            }), t.abrupt("return");
          case 3:
            return t.next = 5, e.configQuery();
          case 5:
            t.sent.data.flag ? wx.navigateTo({
              url: "/pages/storeClockIn/storeClockIn"
            }) : (0, r.toastModel)("门店打卡功能已关闭");
          case 7:
          case "end":
            return t.stop()
        }
      }), t)
    })))()
  },
  gotoVideoList: function() {
    wx.navigateTo({
      url: "/pages/Home/VideoList/VideoList"
    })
  },
  gotoProList: function() {
    wx.navigateTo({
      url: "/pages/Home/ProList/ProList"
    })
  },
  gotoBrandList: function() {
    wx.navigateTo({
      url: "/pages/Home/BrandList/BrandList"
    })
  },
  gotoGZH: function() {
    this.setData({
      propState: !0,
      propNum: 2
    })
  },
  gotoSecurity: function() {
    return a(n().mark((function e() {
      var t;
      return n().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.next = 2, (0, r.scanCode)();
          case 2:
            t = e.sent, wx.navigateTo({
              url: "/pages/SecurityCheck/SecurityCheck?q=" + encodeURIComponent(t.result)
            });
          case 4:
          case "end":
            return e.stop()
        }
      }), e)
    })))()
  },
  gotoBannerInfo: function(e) {
    var t = e.currentTarget.dataset.index;
    wx.navigateTo({
      url: "/pages/Home/BannerInfo/BannerInfo?index=" + t
    })
  },
  miniappLogin: function(e) {
    return new Promise((function(t, n) {
      (0, i.miniappLogin)({
        authorizerAppid: g.globalData.appId,
        jsCode: e
      }).then((function(e) {
        t(e)
      }))
    }))
  },
  getToken: function(e) {
    return new Promise((function(t, n) {
      (0, i.wechatOpenid)({
        serviceSign: e.serviceSign,
        openid: e.openid,
        appId: g.globalData.appId,
        appType: 2
      }).then((function(e) {
        o.default.data.registerUserInfo = e.data, t(e)
      }))
    }))
  },
  gotoWebUrl: function(e) {
    var t = e.currentTarget.dataset.item;
    t.linkUrl && (console.log(t), -1 != t.linkUrl.indexOf("http") ? wx.navigateTo({
      url: "/pages/WebUrl/WebUrl?url=" + t.linkUrl
    }) : -1 != t.linkUrl.indexOf("#小程序://") ? wx.navigateToMiniProgram({
      shortLink: t.linkUrl,
      envVersion: "release"
    }) : wx.navigateTo({
      url: t.linkUrl
    }))
  }
}, "getBanner", (function() {
  return new Promise((function(e, t) {
    (0, i.getBannerList)({
      sort: "priority",
      flag: 1,
      pageIndex: 1,
      pageSize: 100
    }).then((function(t) {
      e(t)
    }))
  }))
})), t(e, "getRule", (function() {
  return new Promise((function(e, t) {
    (0, i.getSignConfig)({}).then((function(t) {
      e(t)
    }))
  }))
})), t(e, "dictionaryItems", (function() {
  return new Promise((function(e, t) {
    (0, i.dictionaryItems)({
      code: "interaction-address"
    }).then((function(t) {
      e(t)
    }))
  }))
})), t(e, "getCurrentDate", (function() {
  var e = new Date,
    t = e.getFullYear(),
    n = (e.getMonth() + 1).toString().padStart(2, "0"),
    a = e.getDate().toString().padStart(2, "0");
  return "".concat(t, "-").concat(n, "-").concat(a)
})), t(e, "getUserData", (function() {
  var e = this;
  return a(n().mark((function t() {
    var a, u, p, g, d, l, f, m, h, w;
    return n().wrap((function(t) {
      for (;;) switch (t.prev = t.next) {
        case 0:
          if (o.default.data.token) {
            t.next = 15;
            break
          }
          return t.next = 3, (0, r.getCode)();
        case 3:
          return a = t.sent, t.next = 6, e.miniappLogin(a);
        case 6:
          return u = t.sent, console.log("获取用户openid", u), o.default.data.openid = u.data.openid, console.log(u.data), t.next = 12, e.getToken(u.data);
        case 12:
          p = t.sent, console.log("获取用户token", p), o.default.data.token = p.data.token;
        case 15:
          return t.next = 17, (0, s.getUser)();
        case 17:
          return g = t.sent, console.log("获取用户信息", g), e.setData({
            userInfo: g.data
          }), t.next = 22, e.getBanner();
        case 22:
          return d = t.sent, t.next = 25, e.getRule();
        case 25:
          return l = t.sent, e.setData({
            ggState: !1,
            signSwitch: l.data.signSwitch,
            bannerImgs: d.data.list
          }), e.data.bannerImgs.length > 6 ? e.setData({
            indicatorDots: !1
          }) : e.setData({
            indicatorDots: !0
          }), g.data.mobile && !g.data.headImage && e.setData({
            propState: !0,
            propNum: 3
          }), t.next = 31, (0, i.dictionaryItems)({
            code: "wb_home_popup_url",
            enterpriseNo: c.default.enterpriseNo
          });
        case 31:
          if (f = t.sent, m = f.data.find((function(e) {
              return "flag" == e.itemName
            })), 200 != f.code) {
            t.next = 40;
            break
          }
          if (1 != m.itemValue) {
            t.next = 40;
            break
          }
          return h = e.getCurrentDate().replace(new RegExp("-", "g"), ""), t.next = 38, (0, i.h5UserRecordCheck)({
            userId: o.default.data.userInfo.id + c.default.env === "release" ? "".concat(h) : "".concat((new Date).getTime())
          });
        case 38:
          200 != (w = t.sent).code || w.data || e.setData({
            wbHomePopupData: f.data,
            propState: !0,
            propNum: 27
          });
        case 40:
        case "end":
          return t.stop()
      }
    }), t)
  })))()
})), t(e, "onShareAppMessage", (function() {})), t(e, "onLoad", (function(e) {
  var t = this;
  return a(n().mark((function e() {
    return n().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return console.log("fetchData", o.default.data.userInfo.id), e.t0 = t, e.next = 4, o.default.getConfigJson();
        case 4:
          if (e.t1 = e.sent, e.t1) {
            e.next = 7;
            break
          }
          e.t1 = {};
        case 7:
          e.t2 = e.t1.peanutFamily, e.t3 = {
            peanutFamily: e.t2
          }, e.t0.setData.call(e.t0, e.t3), (0, i.dictionaryItems)({
            code: "wb_show_sws",
            enterpriseNo: c.default.enterpriseNo
          }).then((function(e) {
            var n = (e.data || [])[0].itemValue;
            t.setData({
              showsws: "1" == n
            })
          }));
        case 11:
        case "end":
          return e.stop()
      }
    }), e)
  })))()
})), t(e, "onShow", (function() {
  var e, t, r = this;
  (0, u.get)("wb_ruleProp") ? (0, u.get)("wb_rulePropState") && (0, u.set)("wb_ruleProp", 2) : (0, u.set)("wb_ruleProp", 1), (0, u.get)("wb_signProp") ? (0, u.get)("wb_signPropState") && (0, u.set)("wb_signProp", 2) : (0, u.set)("wb_signProp", 1), wx.getPrivacySetting ? wx.getPrivacySetting({
    success: (t = a(n().mark((function e(t) {
      return n().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            if (!t.needAuthorization) {
              e.next = 2;
              break
            }
            return e.abrupt("return", wx.navigateTo({
              url: "/pages/privacyContract/privacyContract"
            }));
          case 2:
            r.getUserData();
          case 3:
          case "end":
            return e.stop()
        }
      }), e)
    }))), function(e) {
      return t.apply(this, arguments)
    }),
    fail: (e = a(n().mark((function e(t) {
      return n().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            r.getUserData();
          case 1:
          case "end":
            return e.stop()
        }
      }), e)
    }))), function(t) {
      return e.apply(this, arguments)
    })
  }) : this.getUserData()
})), e));