var e = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  p = require("../../../A5622344549B04BFC3044B435F450D65.js");
Component({
  properties: {},
  data: {
    imgUrl: e.imgUrl,
    imgVersion: e.imgVersion,
    open: !1
  },
  methods: {
    openMask: function() {
      this.setData({
        open: !0
      }), p.pagePopupViews({
        page_type: "",
        page_name: "首页",
        popup_name: "活动规则",
        page_path: "cny/pages/home/<USER>"
      })
    },
    closeMask: function() {
      this.setData({
        open: !1
      })
    },
    handleClose: function() {
      this.closeMask(), p.pagePopupClickEvents({
        page_type: "首页",
        page_name: "首页",
        popup_name: "活动规则",
        page_path: "cny/pages/home/<USER>",
        button_name: "活动规则-关闭"
      })
    }
  }
});