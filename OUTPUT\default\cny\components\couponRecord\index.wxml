<view class="maskContainer" hoverClass="none" hoverStopPropagation="false" wx:if="{{open}}">
    <view class="maskBox maskImgCenter" hoverClass="none" hoverStopPropagation="false">
        <image binderror="" bindload="" class="maskBg " lazyLoad="true" src="{{imgUrl}}components/couponRecord/bg.png?v={{imgVersion}}"></image>
        <image binderror="" bindload="" class="title " lazyLoad="true" src="{{imgUrl}}components/couponRecord/title.png?v={{imgVersion}}"></image>
        <scroll-view scrollY class="sBox">
            <view class="cBox" hoverClass="none" hoverStopPropagation="false" wx:for="{{couponList}}" wx:key="index">
                <image binderror="" bindload="" class="cBg " lazyLoad="true" src="{{imgUrl}}components/couponRecord/cBg.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" class="sImg " lazyLoad="true" src="{{item.listPic}}"></image>
                <view class="content">
                    <view class="counonName SourceHanSerifCN-Bold">{{item.infoTitleTwo}}</view>
                    <view class="exprieTime SourceHanSerifCN-Light">{{item.listValidPeriod}}</view>
                </view>
                <view catch:tap="handleStartUse" class="startUse" data-info="{{item}}"></view>
            </view>
        </scroll-view>
        <image binderror="" bindload="" catch:tap="handleClose" class="close " lazyLoad="true" src="{{imgUrl}}components/couponRecord/close.png?v={{imgVersion}}"></image>
    </view>
</view>
