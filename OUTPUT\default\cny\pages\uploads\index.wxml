<view class="container fadeIn" hoverClass="none" hoverStopPropagation="false">
    <custom-header :isBack="{{true}}" title="PEANUTS"></custom-header>
    <view class="scroll-view {{screenInfo}}" hoverClass="none" hoverStopPropagation="false" style="top:{{statusHeaderBarHeight}}px">
        <image binderror="" bindload="" class="bg" lazyLoad="true" src="{{imgUrl}}75/home/<USER>"></image>
        <image binderror="" bindload="" class="logo" lazyLoad="true" src="{{imgUrl}}75/home/<USER>"></image>
        <view class="titleBox">
            <image binderror="" bindload="" class="title" lazyLoad="true" src="{{imgUrl}}75/upload/title.png?v={{imgVersion}}"></image>
            <input bindinput="handleGetTitle" class="titleInput SourceHanSerifCN-Bold" placeholder="标题最多10个字符" placeholderClass="titlePlaceHolder" type="text"></input>
        </view>
        <view class="photoBox">
            <image binderror="" bindload="" class="photoBg" lazyLoad="true" src="{{imgUrl}}75/upload/photo.png?v={{imgVersion}}"></image>
            <view class="upBox">
                <image binderror="" bindload="" catch:tap="handleUploadImg" class="up_add" lazyLoad="true" src="{{imgUrl}}75/upload/up_add.png?v={{imgVersion}}" wx:if="{{!uploadImg}}"></image>
                <view catch:tap="handleUploadImg" class="upAfterBox" wx:else>
                    <image binderror="" bindload="" class="upAfter" lazyLoad="true" mode="aspectFit" src="{{uploadImg}}"></image>
                    <image binderror="" bindload="" class="up_add_2" lazyLoad="true" src="{{imgUrl}}75/upload/up_add_2.png?v={{imgVersion}}"></image>
                </view>
            </view>
        </view>
        <view class="contentBox">
            <image binderror="" bindload="" class="contentBg" lazyLoad="true" src="{{imgUrl}}75/upload/content.png?v={{imgVersion}}"></image>
            <textarea bindinput="handleGetContent" class="contentInput SourceHanSerifCN-Regular" maxlength="-1" placeholder="请输入您的内容"></textarea>
        </view>
        <image binderror="" bindload="" catch:tap="handleUploadStore" class="submit" lazyLoad="true" src="{{imgUrl}}75/upload/up_submit.png?v={{imgVersion}}"></image>
    </view>
    <copyright></copyright>
    <upload-success bind:saveImg="handleSaveImg" id="upload-success"></upload-success>
</view>
<poster bind:fail="onPosterFail" bind:success="onPosterSuccess" config="{{posterConfig}}" id="poster"></poster>
