<view class="maskContainer" hoverClass="none" hoverStopPropagation="false" wx:if="{{open}}">
    <view class="maskBox maskImgCenter" hoverClass="none" hoverStopPropagation="false">
        <image binderror="" bindload="" class="maskBg" lazyLoad="false" src="{{imgUrl}}75/components/privacy/bg.png?v={{imgVersion}}"></image>
        <image binderror="" bindload="" class="title" lazyLoad="false" src="{{imgUrl}}75/components/privacy/title.png?v={{imgVersion}}"></image>
        <view class="contentBox">
            <image binderror="" bindload="" class="content" lazyLoad="false" src="{{imgUrl}}75/components/privacy/content.png?v={{imgVersion}}"></image>
            <button bindtap="handleOpenPrivacyContract" class="provacyBtn"></button>
        </view>
        <view class="menuBox">
            <image binderror="" bindload="" catch:tap="handleClose" class="i1" lazyLoad="false" src="{{imgUrl}}75/components/privacy/i1.png?v={{imgVersion}}"></image>
            <image binderror="" bindload="" catch:tap="handleAgree" class="i2" lazyLoad="false" src="{{imgUrl}}75/components/privacy/i2.png?v={{imgVersion}}"></image>
        </view>
    </view>
</view>
