var n = wx.getAccountInfoSync().miniProgram.envVersion;
Component({
  properties: {},
  data: {
    imgUrl: "https://cdn.omnimkt.com/2025/snoppy/images/",
    imgVersion: "v" + Math.random().toString(36).substr(2),
    open: !0,
    isOnline: !1
  },
  pageLifetimes: {
    show: function() {
      this.isOnline()
    },
    hide: function() {},
    resize: function() {}
  },
  methods: {
    isOnline: function() {
      var e = this;
      wx.request({
        url: "release" === n ? "https://snoopy-api.omnimkt.com/snoopy/online" : "https://api.omnimkt.com/snoopy/snoopy/online",
        data: {},
        method: "GET",
        success: function(n) {
          if (console.log("online res: ", n), 200 == n.statusCode) {
            if (200 == n.data.code) {
              var i = n.data.data;
              e.setData({
                isOnline: i.online
              })
            }
          } else e.setData({
            isOnline: !1
          })
        },
        fail: function(n) {
          e.setData({
            isOnline: !1
          })
        }
      })
    },
    handleOpenMask: function() {
      this.setData({
        open: !0
      })
    },
    handleOpenKv: function() {
      wx.navigateTo({
        url: "/cny/pages/index/index"
      }), this.handleMaskClose()
    },
    handleMaskClose: function() {
      this.setData({
        open: !1
      })
    }
  }
});