var n = require("../../../95D1B746549B04BFF3B7DF41DA740D65.js");
Page({
  data: {
    couponUrl: ""
  },
  onLoad: function(o) {
    var t;
    this.setData({
      couponUrl: null !== (t = (0, n.getStorageSync)("couponUrl")) && void 0 !== t ? t : ""
    })
  },
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {}
});