.drawReward {
    background: url("http://dm-assets.supercarrier8.com/wobei/newVersion/047.png");
    background-size: 100% 100%;
    height: 100vh;
    overflow: hidden
}

.drawReward_head {
    background: #fff;
    border: 4rpx solid #ffd900;
    border-radius: 50%;
    height: 87rpx;
    left: 35rpx;
    overflow: hidden;
    position: absolute;
    top: 39rpx;
    width: 87rpx
}

.drawReward_dh {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    left: 136rpx;
    position: relative;
    top: 47rpx;
    width: 262rpx
}

.drawReward_dh_l {
    margin-left: 20rpx
}

.drawReward_dh_l_num {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 36rpx;
    font-weight: 700
}

.drawReward_dh_l_text {
    color: #000;
    font-family: SourceHanSansSC;
    font-size: 20rpx;
    font-weight: 400
}

.drawReward_dh_r {
    background: #000;
    border-radius: 5rpx;
    color: #fff;
    font-family: Source <PERSON>N;
    font-size: 20rpx;
    font-weight: 400;
    height: 40rpx;
    line-height: 40rpx;
    margin-left: 26rpx;
    text-align: center;
    width: 84rpx
}

.drawReward_jl {
    height: 68rpx;
    position: absolute;
    right: 101rpx;
    top: 45rpx;
    width: 81rpx
}

.drawReward_rule {
    height: 39rpx;
    position: absolute;
    right: 21rpx;
    top: 21rpx;
    width: 39rpx
}

.drawReward_dh_icon {
    height: 68.8rpx;
    left: 4rpx;
    position: absolute;
    top: 4rpx;
    width: 71.2rpx
}

.drawReward_dh_num {
    background: url("http://dm-assets.supercarrier8.com/wobei/draw/dh.png");
    background-size: 100% 100%;
    color: #00208e;
    font-family: Source Han Sans CN;
    font-size: 30rpx;
    font-weight: 700;
    line-height: 68rpx;
    padding-left: 60rpx;
    padding-right: 100rpx;
    text-align: center
}

.container-out {
    height: 605rpx;
    left: 50%;
    position: relative;
    top: 76rpx;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 671rpx
}

.circle {
    border-radius: 50%;
    display: block;
    height: 20rpx;
    position: absolute;
    width: 20rpx
}

.container-in {
    background: url("http://dm-assets.supercarrier8.com/wobei/newVersion/049.png");
    background-size: 100% 100%;
    border-radius: 40rpx;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    height: 605rpx;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin: auto;
    width: 671rpx
}

.award {
    border-radius: 15rpx;
    height: 162rpx;
    position: absolute;
    width: 162rpx
}

.awardBg {
    z-index: 1
}

.awardBg,.award_item {
    height: 162rpx;
    left: 0;
    position: absolute;
    top: 0;
    width: 162rpx
}

.award_item {
    overflow: hidden;
    z-index: 2
}

.award_item_img {
    height: 100rpx;
    margin: 20rpx auto 0;
    width: 100rpx
}

.award_item_text {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 18rpx;
    font-weight: 500;
    text-align: center
}

.start-btn {
    background: url("http://dm-assets.supercarrier8.com/wobei/newVersion/054.png");
    background-size: 100% 100%;
    border-radius: 15rpx;
    box-sizing: border-box;
    font-weight: 600;
    height: 162rpx;
    left: 248rpx;
    padding-top: 20rpx;
    position: absolute;
    text-align: center;
    top: 230rpx;
    width: 162rpx
}

.startFlex {
    margin: 20rpx auto 0
}

.start_text {
    color: #fff;
    font-family: STYuanti;
    font-size: 30rpx;
    font-weight: 700;
    text-shadow: 0 4rpx 0 rgba(192,38,38,.38)
}

.start_textnum {
    font-size: 18rpx;
    font-weight: 700;
    text-shadow: 0 2rpx 0 rgba(195,38,38,.38)
}

.start_button,.start_textnum {
    color: #fff;
    font-family: Source Han Sans CN
}

.start_button {
    background: linear-gradient(105deg,#f90,#ffad00,#ffc600);
    border-radius: 13rpx;
    font-size: 16rpx;
    font-weight: 400;
    height: 25rpx;
    line-height: 25rpx;
    margin: 6rpx auto 0;
    width: 117rpx
}

.drawReward_tt {
    height: 295rpx;
    left: 50%;
    position: absolute;
    top: 720rpx;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 685rpx
}

.drawReward_nl {
    height: 100rpx;
    position: absolute;
    right: 100rpx;
    top: 750rpx;
    width: 304rpx;
    z-index: 2
}

.drawReward_rw {
    height: 247rpx;
    left: 0rpx;
    position: absolute;
    top: 792rpx;
    width: 348rpx;
    z-index: 1
}

.drawReward_text {
    color: #231815;
    font-family: Arial;
    font-size: 18rpx;
    font-weight: 400;
    left: 50%;
    position: absolute;
    text-align: center;
    top: 1140rpx;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}
