var e = require("B163DCC5549B04BFD705B4C2F8640D65.js"),
  t = wx.getAccountInfoSync().miniProgram.envVersion;
console.log("当前小程序版本：".concat(t)), console.log("当前接口域名", e.ApiBaseUri[t]), console.log("当前小程序屏幕的宽度", wx.getSystemInfoSync().windowWidth), console.log("当前小程序屏幕的高度", wx.getSystemInfoSync().windowHeight), module.exports = {
  APPID: "wx841a8e9e6972a9a6",
  ENV: t,
  ApiBaseUri: e.ApiBaseUri[t],
  statusBarHeight: wx.getSystemInfoSync().statusBarHeight,
  statusHeaderBarHeight: wx.getSystemInfoSync().statusBarHeight + 44,
  imgUrl: "https://cdn.omnimkt.com/2025/snoppy/images/",
  imgVersion: "release" == e.ApiBaseUri[t] ? "v1.0.0" : "v" + Math.random().toString(36).substr(2),
  fontsUrl: "https://cdn.omnimkt.com/",
  shareOptions: {
    title: "点击分享你的史努比珍藏",
    path: "/cny/pages/home/<USER>",
    imageUrl: "https://cdn.omnimkt.com/2025/snoppy/images/share.png?v=" + 100 * Math.random()
  },
  errorInfo: {
    tokenError: "当前访问人数过多，请稍后再试"
  },
  tipInfos: {
    copyTitle: "复制成功，粘贴至浏览器领取优惠券"
  }
};