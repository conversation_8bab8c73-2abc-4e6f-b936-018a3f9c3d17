require("../../../../@babel/runtime/helpers/Arrayincludes");
var e, t, n = require("../../../../@babel/runtime/helpers/typeof");
module.exports = (e = {}, t = function(t, r) {
  if (!e[t]) return require(r);
  if (!e[t].status) {
    var o = e[t].m;
    o._exports = o._tempexports;
    var i = Object.getOwnPropertyDescriptor(o, "exports");
    i && i.configurable && Object.defineProperty(o, "exports", {
      set: function(e) {
        "object" === n(e) && e !== o._exports && (o._exports.__proto__ = e.__proto__, Object.keys(e).forEach((function(t) {
          o._exports[t] = e[t]
        }))), o._tempexports = e
      },
      get: function() {
        return o._tempexports
      }
    }), e[t].status = 1, e[t].func(e[t].req, o, o.exports)
  }
  return e[t].m.exports
}, function(t, n, r) {
  e[t] = {
    status: 0,
    func: n,
    req: r,
    m: {
      exports: {},
      _tempexports: {}
    }
  }
}(1739784025861, (function(e, t, r) {
  var o, i;
  o = window, i = function() {
    return function(e) {
      var t = {};

      function r(n) {
        if (t[n]) return t[n].exports;
        var o = t[n] = {
          i: n,
          l: !1,
          exports: {}
        };
        return e[n].call(o.exports, o, o.exports, r), o.l = !0, o.exports
      }
      return r.m = e, r.c = t, r.d = function(e, t, n) {
        r.o(e, t) || Object.defineProperty(e, t, {
          enumerable: !0,
          get: n
        })
      }, r.r = function(e) {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
          value: "Module"
        }), Object.defineProperty(e, "__esModule", {
          value: !0
        })
      }, r.t = function(e, t) {
        if (1 & t && (e = r(e)), 8 & t) return e;
        if (4 & t && "object" == n(e) && e && e.__esModule) return e;
        var o = Object.create(null);
        if (r.r(o), Object.defineProperty(o, "default", {
            enumerable: !0,
            value: e
          }), 2 & t && "string" != typeof e)
          for (var i in e) r.d(o, i, function(t) {
            return e[t]
          }.bind(null, i));
        return o
      }, r.n = function(e) {
        var t = e && e.__esModule ? function() {
          return e.default
        } : function() {
          return e
        };
        return r.d(t, "a", t), t
      }, r.o = function(e, t) {
        return Object.prototype.hasOwnProperty.call(e, t)
      }, r.p = "./dist/", r(r.s = 13)
    }([function(e, t, n) {
      var r = this && this.__createBinding || (Object.create ? function(e, t, n, r) {
          void 0 === r && (r = n);
          var o = Object.getOwnPropertyDescriptor(t, n);
          o && !("get" in o ? !t.__esModule : o.writable || o.configurable) || (o = {
            enumerable: !0,
            get: function() {
              return t[n]
            }
          }), Object.defineProperty(e, r, o)
        } : function(e, t, n, r) {
          void 0 === r && (r = n), e[r] = t[n]
        }),
        o = this && this.__exportStar || function(e, t) {
          for (var n in e) "default" === n || Object.prototype.hasOwnProperty.call(t, n) || r(t, e, n)
        };
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.Shell = t.Reporter = t.Context = t.Client = void 0;
      var i = n(7);
      t.Client = i.default;
      var a = n(9);
      t.Context = a.default;
      var u = n(17);
      t.Reporter = u.default;
      var s = n(19);
      t.Shell = s.default, o(n(8), t), o(n(20), t), o(n(21), t), o(n(22), t), o(n(5), t), o(n(23), t), o(n(24), t), o(n(11), t), o(n(2), t), o(n(25), t), o(n(12), t), o(n(26), t), o(n(10), t)
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }
      var i;
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.HEADER_KEY = t.getPerformance = t.setStorageSync = t.getStorageSync = t.appName = t.sdk = t.VERSION = void 0, t.VERSION = "cdn-0.0.32";
      var a = "object";

      function u(e) {
        return "function" == typeof e
      }
      i = function() {
        var e, t = "unknown";
        if (("undefined" == typeof swan ? "undefined" : o(swan)) === a) e = swan, t = "swan";
        else if (("undefined" == typeof qq ? "undefined" : o(qq)) === a) e = qq, t = "qq";
        else if (("undefined" == typeof tt ? "undefined" : o(tt)) === a) e = tt, t = "bytedance";
        else if (("undefined" == typeof dd ? "undefined" : o(dd)) === a) e = dd, t = "dingtalk";
        else if (("undefined" == typeof my ? "undefined" : o(my)) === a) e = my, t = "alipay";
        else if (("undefined" == typeof jd ? "undefined" : o(jd)) === a) e = jd, t = "jd";
        else {
          if (("undefined" == typeof wx ? "undefined" : o(wx)) !== a) throw new Error("Current platform is not supported.");
          e = wx, t = "wechat"
        }
        return {
          sdk: e,
          appName: t
        }
      }(), t.sdk = i.sdk, t.appName = i.appName, t.getStorageSync = function(e) {
        return u(t.sdk.getStorageSync) ? "alipay" === t.appName || "dingtalk" === t.appName ? t.sdk.getStorageSync({
          key: e
        }).data : t.sdk.getStorageSync(e) : void 0
      }, t.setStorageSync = function(e, n) {
        return u(t.sdk.getStorageSync) ? "alipay" === t.appName || "dingtalk" === t.appName ? t.sdk.setStorageSync({
          key: e,
          data: n
        }) : t.sdk.setStorageSync(e, n) : void 0
      }, t.getPerformance = function() {
        return u(t.sdk.getPerformance) ? t.sdk.getPerformance() : "performance" in t.sdk ? t.sdk.performance : void 0
      }, t.HEADER_KEY = function() {
        switch (t.appName) {
          case "alipay":
            return "headers";
          default:
            return "header"
        }
      }(), t.default = t.sdk
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        return o(e) === t
      }

      function a(e, t) {
        return Object.prototype.toString.call(e) === "[object ".concat(t, "]")
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.isZero = t.isObject = t.isNull = t.isNumber = t.isBoolean = t.isRegExp = t.isArray = t.isToString = t.isString = t.isFunction = t.isTypeof = void 0, t.isTypeof = i, t.isFunction = function(e) {
        return "function" == typeof e
      }, t.isString = function(e) {
        return i(e, "string")
      }, t.isToString = a, t.isArray = function(e) {
        return a(e, "Array")
      }, t.isRegExp = function(e) {
        return a(e, "RegExp")
      }, t.isBoolean = function(e) {
        return i(e, "boolean")
      }, t.isNumber = function(e) {
        return i(e, "number") && !isNaN(e) || i(e, "bigint")
      }, t.isNull = function(e) {
        return null === e
      }, t.isObject = function(e) {
        return !(0, t.isNull)(e) && i(e, "object")
      }, t.isZero = function() {
        for (var e = 0; e < arguments.length; e++)
          if (0 !== (e < 0 || arguments.length <= e ? void 0 : arguments[e])) return !1;
        return !0
      }
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.getCurView = t.getCurPage = void 0, t.getCurPage = function() {
        if ("function" == typeof getCurrentPages) try {
          var e = getCurrentPages() || [];
          return e[e.length - 1]
        } catch (e) {
          console.warn("[arms] error in getCurView", e)
        }
      }, t.getCurView = function(e) {
        if (e) {
          var t = e.getViews();
          if (t && t.length) return t[t.length - 1]
        }
      }
    }, function(e, t, n) {
      function r(e) {
        return function(e) {
          if (Array.isArray(e)) return o(e)
        }(e) || function(e) {
          if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e)
        }(e) || function(e, t) {
          if (e) {
            if ("string" == typeof e) return o(e, t);
            var n = Object.prototype.toString.call(e).slice(8, -1);
            return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? o(e, t) : void 0
          }
        }(e) || function() {
          throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
        }()
      }

      function o(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var n = 0, r = Array(t); n < t; n++) r[n] = e[n];
        return r
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.removePageListener = t.addPageListener = t.originalComponent = t.originalPage = void 0;
      var i = n(0),
        a = n(1),
        u = {
          onLoad: [],
          onShow: [],
          onHide: [],
          onUnload: [],
          "*": []
        },
        s = ["onLoad", "onShow", "onReady", "onHide", "onUnload", "onPullDownRefresh", "onReachBottom", "onShareAppMessage", "onShareTimeline", "onAddToFavorites", "onPageScroll", "onResize", "onTabItemTap", "onSaveExitState"];

      function c(e) {
        Object.keys(e).forEach((function(t) {
          var n = e[t];
          !(0, i.isFunction)(n) || s.includes(t) || t in u || (u[t] = [])
        })), Object.keys(u).forEach((function(t) {
          "swan" === a.appName && Object.defineProperty(e, t, {
            writable: !0
          }), "*" === t || (0, i.interceptFunction)(e, t, (function() {
            for (var e = this, n = arguments.length, o = Array(n), i = 0; i < n; i++) o[i] = arguments[i];
            var a = [].concat(r(u[t]), r(u["*"]));
            a.forEach((function(t) {
              t.apply(e, o)
            }))
          }))
        }))
      }
      t.originalPage = Page, t.originalComponent = Component, Page = function() {
        for (var e = arguments.length, n = Array(e), r = 0; r < e; r++) n[r] = arguments[r];
        try {
          c.apply(this, n)
        } catch (e) {}
        return t.originalPage.apply(this, n)
      }, Object.keys(t.originalPage).forEach((function(e) {
        Page[e] = t.originalPage[e]
      })), Component = function() {
        for (var e = arguments.length, n = Array(e), r = 0; r < e; r++) n[r] = arguments[r];
        try {
          var o = n[0] || {};
          o.methods || (o.methods = {}), c.call(this, o.methods)
        } catch (e) {}
        return t.originalComponent.apply(this, n)
      }, Object.keys(t.originalComponent).forEach((function(e) {
        Component[e] = t.originalComponent[e]
      })), t.addPageListener = function(e, t) {
        e in u || (u[e] = []), u[e].push(t)
      }, t.removePageListener = function(e, t) {
        if (e in u) {
          var n = u[e],
            r = n.indexOf(t);
          n.splice(r, 1)
        }
      }
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.AppType = t.ResourceStatus = t.RumEventType = void 0,
        function(e) {
          e.VIEW = "view", e.RESOURCE = "resource", e.EXCEPTION = "exception", e.LONG_TASK = "longTask", e.ACTION = "action", e.CUSTOM = "custom", e.APPLICATION = "application"
        }(t.RumEventType || (t.RumEventType = {})),
        function(e) {
          e[e.Unknown = -1] = "Unknown", e[e.Failed = 0] = "Failed", e[e.Success = 1] = "Success"
        }(t.ResourceStatus || (t.ResourceStatus = {})),
        function(e) {
          e.browser = "browser", e.miniapp = "miniapp", e.uniapp = "uniapp", e.minigame = "minigame"
        }(t.AppType || (t.AppType = {}))
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.getCurrentTime = t.formatNumber = void 0;
      var r = n(1);
      t.formatNumber = function(e) {
        var t = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : 3;
        if (!e) return e;
        var n = e.toString(),
          r = n.indexOf(".");
        return n = -1 === r ? n.substring(0) : n.substring(0, t + r + 1), parseFloat(n)
      }, t.getCurrentTime = function(e) {
        var t = (0, r.getPerformance)(),
          n = 0;
        return t && t.timeOrigin && t.now && (n = Math.round(t.timeOrigin + (e || t.now()))), 1e12 > n && (n = (new Date).getTime()), n
      }
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        var n = "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
        if (!n) {
          if (Array.isArray(e) || (n = a(e)) || t && e && "number" == typeof e.length) {
            n && (e = n);
            var r = 0,
              o = function() {};
            return {
              s: o,
              n: function() {
                return r >= e.length ? {
                  done: !0
                } : {
                  done: !1,
                  value: e[r++]
                }
              },
              e: function(e) {
                throw e
              },
              f: o
            }
          }
          throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
        }
        var i, u = !0,
          s = !1;
        return {
          s: function() {
            n = n.call(e)
          },
          n: function() {
            var e = n.next();
            return u = e.done, e
          },
          e: function(e) {
            s = !0, i = e
          },
          f: function() {
            try {
              u || null == n.return || n.return()
            } finally {
              if (s) throw i
            }
          }
        }
      }

      function a(e, t) {
        if (e) {
          if ("string" == typeof e) return u(e, t);
          var n = Object.prototype.toString.call(e).slice(8, -1);
          return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? u(e, t) : void 0
        }
      }

      function u(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
        return r
      }

      function s(e, t) {
        for (var n = 0; n < t.length; n++) {
          var r = t[n];
          r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, c(r.key), r)
        }
      }

      function c(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var f = r(8),
        l = r(16),
        p = r(9),
        v = r(2),
        y = function(e, t, n) {
          return t && s(e.prototype, t), n && s(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
          }), e
        }((function e() {
          var t = this;
          (function(e, t) {
            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
          })(this, e), this.emitter = new l.EventEmitter, this.sendEvent = function(e) {
            t.emitter.emit(f.EventType.collect, e)
          }
        }), [{
          key: "init",
          value: function(e, t) {
            var n = this;
            this.ctx = new p.default(e, t);
            var r = this.ctx,
              o = this.collectors,
              a = this.processors,
              u = this.reporter;
            a.forEach((function(e) {
              (0, v.isFunction)(e.setup) && e.setup(r)
            })), this.emitter.on(f.EventType.collect, (function(e) {
              r.setRumEvent(e);
              var t, n = i(a);
              try {
                for (n.s(); !(t = n.n()).done;) {
                  var o = t.value;
                  if (o.match || (o.match = function() {
                      return !0
                    }), o.match(r)) {
                    var s = o.process(r);
                    s && r.setRumEvent(s)
                  }
                }
              } catch (e) {
                n.e(e)
              } finally {
                n.f()
              }
              u.report(r)
            })), o.forEach((function(e) {
              e.setup(r, n.sendEvent)
            }))
          }
        }, {
          key: "setContext",
          value: function(e) {
            this.ctx = e
          }
        }, {
          key: "getContext",
          value: function() {
            return this.ctx
          }
        }, {
          key: "useCollectors",
          value: function(e) {
            this.collectors = e
          }
        }, {
          key: "useProcessors",
          value: function(e) {
            this.processors = e
          }
        }, {
          key: "useReporter",
          value: function(e) {
            this.reporter = e
          }
        }]);
      t.default = y
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.EventType = void 0,
        function(e) {
          e.collect = "collect"
        }(t.EventType || (t.EventType = {}))
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        for (var n = 0; n < t.length; n++) {
          var r = t[n];
          r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, a(r.key), r)
        }
      }

      function a(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var u = function(e, t, n) {
        return t && i(e.prototype, t), n && i(e, n), Object.defineProperty(e, "prototype", {
          writable: !1
        }), e
      }((function e(t, n) {
        (function(e, t) {
          if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
        })(this, e), this.config = t, this.views = [], n && (this.session = n, this.session.init(this))
      }), [{
        key: "getConfig",
        value: function() {
          return this.config
        }
      }, {
        key: "setConfig",
        value: function(e) {
          this.config = e
        }
      }, {
        key: "getViews",
        value: function() {
          return this.views
        }
      }, {
        key: "addView",
        value: function(e) {
          this.views.push(e)
        }
      }, {
        key: "removeView",
        value: function(e) {
          this.views = this.views.filter((function(t) {
            return t.id !== e
          }))
        }
      }, {
        key: "getRumEvent",
        value: function() {
          return this.rumEvent
        }
      }, {
        key: "setRumEvent",
        value: function(e) {
          this.rumEvent = e
        }
      }]);
      t.default = u
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.verifyProperties = void 0;
      var r = n(2);
      t.verifyProperties = function(e) {
        if ((0, r.isObject)(e)) {
          var t = Object.assign({}, e);
          Object.keys(t).forEach((function(e) {
            var n = t[e];
            if (e.length > 50) {
              var o = e.substring(0, 50);
              t[o] = n, delete t[e], e = o
            }(0, r.isString)(n) || (0, r.isNumber)(n) ? (0, r.isString)(n) && n.length > 2e3 && (t[e] = n.substring(0, 2e3)): delete t[e]
          }));
          var n = Object.keys(t);
          if (n.length) return n.length > 20 && n.forEach((function(e, n) {
            n > 19 && delete t[e]
          })), t
        }
      }
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.assign = t.endsWith = t.startsWith = t.find = void 0, t.find = function(e, t) {
        for (var n = 0; n < e.length; n += 1) {
          var r = e[n];
          if (t(r, n)) return r
        }
      }, t.startsWith = function(e, t) {
        return e.slice(0, t.length) === t
      }, t.endsWith = function(e, t) {
        return e.slice(-t.length) === t
      }, t.assign = function(e) {
        for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), r = 1; r < t; r++) n[r - 1] = arguments[r];
        return n.forEach((function(t) {
          for (var n in t) Object.prototype.hasOwnProperty.call(t, n) && (e[n] = t[n])
        })), e
      }
    }, function(e, t, n) {
      function r(e) {
        return function(e) {
          if (Array.isArray(e)) return o(e)
        }(e) || function(e) {
          if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e)
        }(e) || function(e, t) {
          if (e) {
            if ("string" == typeof e) return o(e, t);
            var n = Object.prototype.toString.call(e).slice(8, -1);
            return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? o(e, t) : void 0
          }
        }(e) || function() {
          throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
        }()
      }

      function o(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
        return r
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.urlMatch = t.matchList = t.isMatchOption = void 0;
      var i = n(2),
        a = n(11);

      function u(e, t) {
        var n = !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2];
        return e.some((function(e) {
          try {
            if ("function" == typeof e) return e(t);
            if (e instanceof RegExp) return e.test(t);
            if ("string" == typeof e) return n ? (0, a.startsWith)(t, e) : e === t
          } catch (e) {}
          return !1
        }))
      }
      t.isMatchOption = function(e) {
        return (0, i.isString)(e) || (0, i.isFunction)(e) || (0, i.isRegExp)(e)
      }, t.matchList = u, t.urlMatch = function(e) {
        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [];
        try {
          (0, i.isArray)(t) || (t = [t]);
          var n = [/^(https?:)?\/\/.*rum|retcode|log-global|log\.aliyuncs\.com/, /^(https?:)?\/\/.*\.mmstat\.com/, /^(https?:)?\/\/.*hm\.baidu\.com/, /^(https?:)?\/\/.*google-analytics\.com/, /data:(.+?)(;base64)?,(.+)$/].concat(r(t));
          return u(n, e)
        } catch (e) {
          return !1
        }
      }
    }, function(e, t, n) {
      e.exports = n(14)
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.ArmsRum = void 0;
      var r = n(15);
      Object.defineProperty(t, "ArmsRum", {
        enumerable: !0,
        get: function() {
          return r.ArmsRum
        }
      }), t.default = r.default
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t, n) {
        return (t = s(t)) in e ? Object.defineProperty(e, t, {
          value: n,
          enumerable: !0,
          configurable: !0,
          writable: !0
        }) : e[t] = n, e
      }

      function a(e, t) {
        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
      }

      function u(e, t) {
        for (var n, r = 0; r < t.length; r++)(n = t[r]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, s(n.key), n)
      }

      function s(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }

      function c(e, t, n) {
        return t = f(t),
          function(e, t) {
            if (t && ("object" === o(t) || "function" == typeof t)) return t;
            if (void 0 !== t) throw new TypeError("Derived constructors may only return object or undefined");
            return function(e) {
              if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
              return e
            }(e)
          }(e, function() {
            try {
              var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {})))
            } catch (e) {}
            return !!e
          }() ? Reflect.construct(t, n || [], f(e).constructor) : t.apply(e, n))
      }

      function f(e) {
        return (f = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e) {
          return e.__proto__ || Object.getPrototypeOf(e)
        })(e)
      }

      function l(e, t) {
        if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
        e.prototype = Object.create(t && t.prototype, {
          constructor: {
            value: e,
            writable: !0,
            configurable: !0
          }
        }), Object.defineProperty(e, "prototype", {
          writable: !1
        }), t && function(e, t) {
          (Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e, t) {
            return e.__proto__ = t, e
          })(e, t)
        }(e, t)
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.ArmsRum = void 0;
      var p = r(0),
        v = r(27),
        y = r(28),
        d = r(29),
        m = r(31),
        h = r(34),
        g = r(35),
        b = r(36),
        w = r(37),
        O = r(38),
        E = r(39),
        S = r(1),
        _ = r(40),
        j = function(e) {
          function t() {
            var e;
            return a(this, t), (e = c(this, t, arguments)).version = S.VERSION, e.updateNetType = function(t) {
              var n = e.getConfig().net,
                r = void 0 === n ? {} : n;
              e.setConfig("net", Object.assign(Object.assign({}, r), {
                type: t
              }))
            }, e
          }
          return l(t, e),
            function(e, t, n) {
              return t && u(e.prototype, t), n && u(e, n), Object.defineProperty(e, "prototype", {
                writable: !1
              }), e
            }(t, [{
              key: "init",
              value: function(e) {
                return this.client.useCollectors([new v.default, new y.default, new d.default, new m.default, new h.default, new g.default]), this.client.useProcessors([new b.default, new w.default]), this.client.useReporter(new O.default), this.client.init(e, new _.RumSession), (0, E.getNetType)(this.updateNetType), this
              }
            }, {
              key: "setConfig",
              value: function() {
                var e = this.client.getContext();
                if (2 === arguments.length) {
                  var t = this.client.getContext(),
                    n = t.getConfig();
                  t.setConfig(Object.assign(Object.assign({}, n), i({}, 0 >= arguments.length ? void 0 : arguments[0], 1 >= arguments.length ? void 0 : arguments[1])))
                } else e.setConfig(0 >= arguments.length ? void 0 : arguments[0])
              }
            }])
        }(p.Shell);
      t.ArmsRum = j, t.default = new j
    }, function(e, t, r) {
      var o, i = "object" == ("undefined" == typeof Reflect ? "undefined" : n(Reflect)) ? Reflect : null,
        a = i && "function" == typeof i.apply ? i.apply : function(e, t, n) {
          return Function.prototype.apply.call(e, t, n)
        };
      o = i && "function" == typeof i.ownKeys ? i.ownKeys : Object.getOwnPropertySymbols ? function(e) {
        return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))
      } : function(e) {
        return Object.getOwnPropertyNames(e)
      };
      var u = Number.isNaN || function(e) {
        return e != e
      };

      function s() {
        s.init.call(this)
      }
      e.exports = s, e.exports.once = function(e, t) {
        return new Promise((function(n, r) {
          function o(n) {
            e.removeListener(t, i), r(n)
          }

          function i() {
            "function" == typeof e.removeListener && e.removeListener("error", o), n([].slice.call(arguments))
          }
          g(e, t, i, {
            once: !0
          }), "error" !== t && function(e, t, n) {
            "function" == typeof e.on && g(e, "error", t, n)
          }(e, o, {
            once: !0
          })
        }))
      }, s.EventEmitter = s, s.prototype._events = void 0, s.prototype._eventsCount = 0, s.prototype._maxListeners = void 0;
      var c = 10;

      function f(e) {
        if ("function" != typeof e) throw new TypeError('The "listener" argument must be of type Function. Received type ' + n(e))
      }

      function l(e) {
        return void 0 === e._maxListeners ? s.defaultMaxListeners : e._maxListeners
      }

      function p(e, t, n, r) {
        var o, i, a;
        if (f(n), void 0 === (i = e._events) ? (i = e._events = Object.create(null), e._eventsCount = 0) : (void 0 !== i.newListener && (e.emit("newListener", t, n.listener ? n.listener : n), i = e._events), a = i[t]), void 0 === a) a = i[t] = n, ++e._eventsCount;
        else if ("function" == typeof a ? a = i[t] = r ? [n, a] : [a, n] : r ? a.unshift(n) : a.push(n), (o = l(e)) > 0 && a.length > o && !a.warned) {
          a.warned = !0;
          var u = new Error("Possible EventEmitter memory leak detected. " + a.length + " " + String(t) + " listeners added. Use emitter.setMaxListeners() to increase limit");
          u.name = "MaxListenersExceededWarning", u.emitter = e, u.type = t, u.count = a.length,
            function(e) {
              console && console.warn && console.warn(e)
            }(u)
        }
        return e
      }

      function v() {
        if (!this.fired) return this.target.removeListener(this.type, this.wrapFn), this.fired = !0, 0 === arguments.length ? this.listener.call(this.target) : this.listener.apply(this.target, arguments)
      }

      function y(e, t, n) {
        var r = {
            fired: !1,
            wrapFn: void 0,
            target: e,
            type: t,
            listener: n
          },
          o = v.bind(r);
        return o.listener = n, r.wrapFn = o, o
      }

      function d(e, t, n) {
        var r = e._events;
        if (void 0 === r) return [];
        var o = r[t];
        return void 0 === o ? [] : "function" == typeof o ? n ? [o.listener || o] : [o] : n ? function(e) {
          for (var t = new Array(e.length), n = 0; n < t.length; ++n) t[n] = e[n].listener || e[n];
          return t
        }(o) : h(o, o.length)
      }

      function m(e) {
        var t = this._events;
        if (void 0 !== t) {
          var n = t[e];
          if ("function" == typeof n) return 1;
          if (void 0 !== n) return n.length
        }
        return 0
      }

      function h(e, t) {
        for (var n = new Array(t), r = 0; r < t; ++r) n[r] = e[r];
        return n
      }

      function g(e, t, r, o) {
        if ("function" == typeof e.on) o.once ? e.once(t, r) : e.on(t, r);
        else {
          if ("function" != typeof e.addEventListener) throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type ' + n(e));
          e.addEventListener(t, (function n(i) {
            o.once && e.removeEventListener(t, n), r(i)
          }))
        }
      }
      Object.defineProperty(s, "defaultMaxListeners", {
        enumerable: !0,
        get: function() {
          return c
        },
        set: function(e) {
          if ("number" != typeof e || e < 0 || u(e)) throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received ' + e + ".");
          c = e
        }
      }), s.init = function() {
        void 0 !== this._events && this._events !== Object.getPrototypeOf(this)._events || (this._events = Object.create(null), this._eventsCount = 0), this._maxListeners = this._maxListeners || void 0
      }, s.prototype.setMaxListeners = function(e) {
        if ("number" != typeof e || e < 0 || u(e)) throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received ' + e + ".");
        return this._maxListeners = e, this
      }, s.prototype.getMaxListeners = function() {
        return l(this)
      }, s.prototype.emit = function(e) {
        for (var t = [], n = 1; n < arguments.length; n++) t.push(arguments[n]);
        var r = "error" === e,
          o = this._events;
        if (void 0 !== o) r = r && void 0 === o.error;
        else if (!r) return !1;
        if (r) {
          var i;
          if (t.length > 0 && (i = t[0]), i instanceof Error) throw i;
          var u = new Error("Unhandled error." + (i ? " (" + i.message + ")" : ""));
          throw u.context = i, u
        }
        var s = o[e];
        if (void 0 === s) return !1;
        if ("function" == typeof s) a(s, this, t);
        else {
          var c = s.length,
            f = h(s, c);
          for (n = 0; n < c; ++n) a(f[n], this, t)
        }
        return !0
      }, s.prototype.addListener = function(e, t) {
        return p(this, e, t, !1)
      }, s.prototype.on = s.prototype.addListener, s.prototype.prependListener = function(e, t) {
        return p(this, e, t, !0)
      }, s.prototype.once = function(e, t) {
        return f(t), this.on(e, y(this, e, t)), this
      }, s.prototype.prependOnceListener = function(e, t) {
        return f(t), this.prependListener(e, y(this, e, t)), this
      }, s.prototype.removeListener = function(e, t) {
        var n, r, o, i, a;
        if (f(t), void 0 === (r = this._events)) return this;
        if (void 0 === (n = r[e])) return this;
        if (n === t || n.listener === t) 0 == --this._eventsCount ? this._events = Object.create(null) : (delete r[e], r.removeListener && this.emit("removeListener", e, n.listener || t));
        else if ("function" != typeof n) {
          for (o = -1, i = n.length - 1; i >= 0; i--)
            if (n[i] === t || n[i].listener === t) {
              a = n[i].listener, o = i;
              break
            } if (o < 0) return this;
          0 === o ? n.shift() : function(e, t) {
            for (; t + 1 < e.length; t++) e[t] = e[t + 1];
            e.pop()
          }(n, o), 1 === n.length && (r[e] = n[0]), void 0 !== r.removeListener && this.emit("removeListener", e, a || t)
        }
        return this
      }, s.prototype.off = s.prototype.removeListener, s.prototype.removeAllListeners = function(e) {
        var t, n, r;
        if (void 0 === (n = this._events)) return this;
        if (void 0 === n.removeListener) return 0 === arguments.length ? (this._events = Object.create(null), this._eventsCount = 0) : void 0 !== n[e] && (0 == --this._eventsCount ? this._events = Object.create(null) : delete n[e]), this;
        if (0 === arguments.length) {
          var o, i = Object.keys(n);
          for (r = 0; r < i.length; ++r) "removeListener" !== (o = i[r]) && this.removeAllListeners(o);
          return this.removeAllListeners("removeListener"), this._events = Object.create(null), this._eventsCount = 0, this
        }
        if ("function" == typeof(t = n[e])) this.removeListener(e, t);
        else if (void 0 !== t)
          for (r = t.length - 1; r >= 0; r--) this.removeListener(e, t[r]);
        return this
      }, s.prototype.listeners = function(e) {
        return d(this, e, !0)
      }, s.prototype.rawListeners = function(e) {
        return d(this, e, !1)
      }, s.listenerCount = function(e, t) {
        return "function" == typeof e.listenerCount ? e.listenerCount(t) : m.call(e, t)
      }, s.prototype.listenerCount = m, s.prototype.eventNames = function() {
        return this._eventsCount > 0 ? o(this._events) : []
      }
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        for (var n = 0; n < t.length; n++) {
          var r = t[n];
          r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, a(r.key), r)
        }
      }

      function a(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var u = r(5),
        s = r(18),
        c = r(2),
        f = r(10),
        l = ["app", "user", "device", "os", "geo", "isp", "net", "properties"],
        p = function(e, t, n) {
          return t && i(e.prototype, t), n && i(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
          }), e
        }((function e() {
          (function(e, t) {
            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
          })(this, e), this.name = "reporter", this.eventQueue = []
        }), [{
          key: "getReportCfg",
          value: function() {
            var e = this.ctx.getConfig().reportConfig;
            return (0, c.isObject)(e) || (e = {}), (!e.flushTime || e.flushTime < 0 || e.flushTime > 1e4) && (e.flushTime = 3e3), (!e.maxEventCount || e.maxEventCount < 1 || e.maxEventCount > 100) && (e.maxEventCount = 20), e
          }
        }, {
          key: "report",
          value: function(e) {
            var t = this;
            this.ctx = e, this.init(e), clearTimeout(this.timer), this.pushToQueue();
            var n = this.getReportCfg();
            this.eventQueue.length >= n.maxEventCount ? this.flushEventQueue() : this.timer = setTimeout((function() {
              t.flushEventQueue()
            }), n.flushTime)
          }
        }, {
          key: "pushToQueue",
          value: function() {
            var e = this.ctx,
              t = this.eventQueue,
              n = e.getRumEvent();
            if (n.event_type === u.RumEventType.EXCEPTION) {
              var r = n.message,
                o = n.stack,
                i = (0, s.getErrorID)({
                  message: r,
                  stack: o
                }),
                a = this.eventQueue.find((function(e) {
                  if (e.event_type === u.RumEventType.EXCEPTION) {
                    var t = e.message,
                      n = e.stack;
                    return (0, s.getErrorID)({
                      message: t,
                      stack: n
                    }) === i
                  }
                }));
              if (a) return void a.times++
            }
            if (n.event_type === u.RumEventType.ACTION) {
              var c = n.target_name,
                f = this.eventQueue.find((function(e) {
                  if (e.event_type === u.RumEventType.ACTION) return c === e.target_name
                }));
              if (f) return void f.times++
            }
            t.push(n)
          }
        }, {
          key: "flushEventQueue",
          value: function() {
            var e = this.ctx,
              t = this.eventQueue;
            if (t.length) {
              var n = e.getViews(),
                r = n[n.length - 1];
              n.forEach((function(e) {
                e.id === r.id && t.filter((function(t) {
                  var n;
                  return (null === (n = t.view) || void 0 === n ? void 0 : n.id) === e.id
                })).forEach((function(e) {
                  delete e.view
                }))
              }));
              var o = e.session;
              (!o || o.getSampled()) && this.mergeEvent(e, t, r), this.eventQueue = []
            }
          }
        }, {
          key: "mergeEvent",
          value: function(e, t, n) {
            for (var r, o = e.getConfig(), i = e.session, a = i.getSessionId(), u = 0; u < t.length; u++) {
              var s = t[u];
              s.session_id === a ? delete s.session_id : (t.splice(u, 1), u--)
            }
            if (0 !== t.length) {
              var p = {
                app: {
                  id: o.pid,
                  env: o.env || "prod",
                  version: o.version,
                  type: ""
                },
                user: {
                  id: i.getUserId()
                },
                session: {
                  id: a
                },
                net: {
                  model: null === (r = o.net) || void 0 === r ? void 0 : r.model
                },
                view: n,
                events: t
              };
              l.forEach((function(e) {
                var t = o[e];
                (0, c.isObject)(t) && Object.keys(t).forEach((function(n) {
                  var r = t[n];
                  "user" === e && "id" === n || ("properties" === e ? p[e] = (0, f.verifyProperties)(t) : ((0, c.isString)(r) || (0, c.isNumber)(r)) && (e in p || (p[e] = {}), p[e][n] = r))
                }))
              })), ("function" != typeof o.beforeReport || (p = o.beforeReport(p))) && this.request(e, p)
            }
          }
        }, {
          key: "init",
          value: function(e) {}
        }]);
      t.default = p
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.getErrorID = void 0, t.getErrorID = function(e) {
        var t = e.message,
          n = void 0 === t ? "" : t,
          r = e.stack;
        return n + (void 0 === r ? "" : r)
      }
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        for (var n = 0; n < t.length; n++) {
          var r = t[n];
          r.enumerable = r.enumerable || !1, r.configurable = !0, "value" in r && (r.writable = !0), Object.defineProperty(e, a(r.key), r)
        }
      }

      function a(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var u = r(5),
        s = r(7),
        c = r(2),
        f = function(e, t, n) {
          return t && i(e.prototype, t), n && i(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
          }), e
        }((function e(t) {
          (function(e, t) {
            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
          })(this, e), this.client = new s.default, t && this.init && this.init(t)
        }), [{
          key: "sendEvent",
          value: function(e) {
            this.client && this.client.sendEvent(e)
          }
        }, {
          key: "getConfig",
          value: function() {
            if (this.client) return this.client.getContext().getConfig()
          }
        }, {
          key: "sendCustom",
          value: function(e) {
            if (e.name && e.type) {
              var t = Object.assign(Object.assign({}, e), {
                event_type: u.RumEventType.CUSTOM
              });
              this.sendEvent(t)
            }
          }
        }, {
          key: "sendView",
          value: function(e) {
            if ((0, c.isObject)(e)) {
              if (e.type || (e.type = "custom"), "custom" === e.type) {
                var t = 0;
                if (["t1", "t2", "t3"].forEach((function(n) {
                    (0, c.isNumber)(e[n]) || delete e[n], n in e && t++
                  })), 0 === t) return
              }
              var n = Object.assign(Object.assign({}, e), {
                event_type: u.RumEventType.VIEW
              });
              this.sendEvent(n)
            }
          }
        }, {
          key: "sendException",
          value: function(e) {
            if (e.name && e.message) {
              var t = e.name,
                n = e.message,
                r = e.stack,
                o = Object.assign(Object.assign({
                  times: 1,
                  name: t,
                  message: n,
                  stack: r
                }, e), {
                  event_type: u.RumEventType.EXCEPTION,
                  type: "custom",
                  source: "custom"
                });
              this.sendEvent(o)
            }
          }
        }, {
          key: "sendResource",
          value: function(e) {
            if (e.name && e.type && (0, c.isNumber)(e.duration)) {
              var t = Object.assign(Object.assign({
                times: 1
              }, e), {
                event_type: u.RumEventType.RESOURCE
              });
              this.sendEvent(t)
            }
          }
        }]);
      t.default = f
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      })
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      })
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      })
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      })
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.delay = t.debounce = t.generateEventId = t.generateSpanId = t.generateTraceId = t.generateGUID = t.interceptFunction = void 0;
      var r = n(2);

      function o() {
        var e = "";
        try {
          if (crypto && crypto.randomUUID) e = crypto.randomUUID();
          else if (crypto && crypto.getRandomValues) {
            var t = new Uint8Array(16);
            crypto.getRandomValues(t), t[6] = 15 & t[6] | 64, t[8] = 63 & t[8] | 128, e = t.reduce((function(e, t) {
              return e + t.toString(16).padStart(2, "0")
            }), "").replace(/^(.{8})(.{4})(.{4})(.{4})(.{12})$/, "$1-$2-$3-$4-$5")
          }
        } catch (e) {}
        return e || (e = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function(e) {
          var t = 16 * Math.random() | 0;
          return ("x" == e ? t : 3 & t | 8).toString(16)
        }))), e
      }

      function i() {
        for (var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 16, t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 16, n = "", r = 0; r < e; r++) n += Math.floor(Math.random() * t).toString(t);
        return n
      }
      t.interceptFunction = function(e, t, n) {
        var o = e[t];
        e[t] = function() {
          for (var e = arguments.length, t = new Array(e), i = 0; i < e; i++) t[i] = arguments[i];
          if (n.apply(this, t), (0, r.isFunction)(o)) return o.apply(this, t)
        }
      }, t.generateGUID = o, t.generateTraceId = function() {
        var e = o().replace(/-/g, "");
        return "0" === e[0] && (e = "1" + e.substring(1)), "0" === e[16] && (e = e.substring(0, 16) + "1" + e.substring(17)), e
      }, t.generateSpanId = i, t.generateEventId = function(e) {
        var t = i();
        return "00-".concat(e, "-").concat(t)
      }, t.debounce = function(e, t) {
        var n;
        if ((0, r.isFunction)(e)) return function() {
          var r = this,
            o = arguments;
          n && clearTimeout(n), n = setTimeout((function() {
            e.apply(r, o)
          }), t)
        }
      }, t.delay = function(e, t) {
        if ((0, r.isFunction)(e)) {
          for (var n = arguments.length, o = new Array(n > 2 ? n - 2 : 0), i = 2; i < n; i++) o[i - 2] = arguments[i];
          return setTimeout.apply(void 0, [e, +t || 0].concat(o))
        }
      }
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.formatNumber = t.performDraw = t.ONE_DAY = t.ONE_HOUR = t.ONE_MINUTE = t.ONE_SECOND = void 0, t.ONE_SECOND = 1e3, t.ONE_MINUTE = 60 * t.ONE_SECOND, t.ONE_HOUR = 60 * t.ONE_MINUTE, t.ONE_DAY = 24 * t.ONE_HOUR, t.performDraw = function(e) {
        return 0 !== e && 100 * Math.random() <= e
      }, t.formatNumber = function(e) {
        var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 3,
          n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0;
        if (!e) return e;
        var r = e.toString(),
          o = r.indexOf(".");
        r = -1 !== o ? r.substring(0, t + o + 1) : r.substring(0);
        var i = parseFloat(r);
        return i >= n ? i : void 0
      }
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.parseTracingOptions = t.makeTracingHeaders = t.isTraceOption = void 0;
      var r = n(12),
        o = n(2);

      function i(e) {
        return e && (0, r.isMatchOption)(e.match) && (0, o.isArray)(e.propagatorTypes)
      }
      t.isTraceOption = i, t.makeTracingHeaders = function(e, t, n, r) {
        var i = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : {},
          a = {},
          u = n ? "1" : "0";
        return r.includes("sw8") && (r = ["sw8"]), r.forEach((function(n) {
          switch (n) {
            case "jaeger":
              a["uber-trace-id"] = "".concat(e, ":").concat(t, ":0:").concat(u);
              break;
            case "b3":
              a.b3 = "".concat(e, "-").concat(t, "-").concat(u);
              break;
            case "b3multi":
              a["X-B3-TraceId"] = e, a["X-B3-SpanId"] = t, a["X-B3-Sampled"] = u;
              break;
            case "sw8":
              if ((0, o.isFunction)(btoa)) {
                var r = btoa(e),
                  s = btoa(t),
                  c = btoa(i.appId),
                  f = btoa(i.appVersion),
                  l = btoa(i.viewName),
                  p = btoa(i.host);
                a.sw8 = "".concat(u, "-").concat(r, "-").concat(s, "-", 0, "-").concat(c, "-").concat(f, "-").concat(l, "-").concat(p)
              }
              break;
            case "tracecontext":
            default:
              a.traceparent = "00-".concat(e, "-").concat(t, "-0").concat(u), i.tracestate && (a.tracestate = i.tracestate)
          }
        })), i.baggage && (a.baggage = i.baggage), a
      }, t.parseTracingOptions = function(e) {
        var t = ["tracecontext"];
        if ((0, o.isBoolean)(e) || !e) return {
          enable: !!e,
          sample: 100,
          propagatorTypes: t,
          allowedUrls: [],
          tracestate: !0,
          baggage: !1
        };
        var n = e.enable,
          a = void 0 === n || n,
          u = e.sample,
          s = void 0 === u ? 100 : u,
          c = e.propagatorTypes,
          f = void 0 === c ? t : c,
          l = e.allowedUrls,
          p = void 0 === l ? [] : l,
          v = e.tracestate,
          y = void 0 === v || v,
          d = e.baggage,
          m = void 0 !== d && d,
          h = [];
        return (0, o.isArray)(p) && p.length && p.forEach((function(e) {
          (0, r.isMatchOption)(e) ? h.push({
            match: e,
            propagatorTypes: f
          }): i(e) && h.push(e)
        })), {
          enable: !(0, o.isBoolean)(a) || a,
          sample: (0, o.isNumber)(s) ? s : 100,
          propagatorTypes: f,
          allowedUrls: h,
          tracestate: y,
          baggage: m
        }
      }
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        for (var n, r = 0; r < t.length; r++)(n = t[r]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, a(n.key), n)
      }

      function a(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var u = r(0),
        s = r(3),
        c = r(4),
        f = function(e, t, n) {
          return t && i(e.prototype, t), n && i(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
          }), e
        }((function e() {
          var t = this;
          (function(e, t) {
            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
          })(this, e), this.name = "pv-collector", this.onUnload = function() {
            var e = (0, s.getCurView)(t.ctx);
            t.ctx.removeView(e.id)
          }, this.sendPv = function() {
            var e = (0, s.getCurPage)();
            if (e) {
              var n = e.route,
                r = e.__rum_view_id;
              if (!t.prevPage || n !== t.prevPage.route || r !== t.prevPage.viewId) {
                var o = r ? "route_back" : "initial_load";
                r = e.__rum_view_id = t.ctx.session.getViewId(), t.prevPage = {
                  route: n,
                  viewId: r
                };
                var i = t.getViewName(n);
                t.ctx.addView({
                  id: r,
                  name: i
                }), t.sendEvent({
                  event_type: u.RumEventType.VIEW,
                  type: "pv",
                  url: n,
                  name: i,
                  loading_type: o
                })
              }
            }
          }
        }), [{
          key: "setup",
          value: function(e, t) {
            this.ctx = e, this.sendEvent = t, (0, c.addPageListener)("onLoad", this.sendPv), (0, c.addPageListener)("onUnload", this.onUnload)
          }
        }, {
          key: "getViewName",
          value: function(e) {
            var t = this.ctx.getConfig().parseViewName,
              n = e;
            return (0, u.isFunction)(t) && (n = t(e)), n
          }
        }, {
          key: "destroy",
          value: function() {
            (0, c.removePageListener)("onLoad", this.sendPv)
          }
        }]);
      t.default = f
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        for (var n, r = 0; r < t.length; r++)(n = t[r]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, a(n.key), n)
      }

      function a(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var u = r(0),
        s = r(1),
        c = r(3),
        f = r(4),
        l = (0, s.getPerformance)(),
        p = function(e, t, n) {
          return t && i(e.prototype, t), n && i(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
          }), e
        }((function e() {
          var t = this;
          (function(e, t) {
            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
          })(this, e), this.name = "perf-collector", this.currentViewId = "", this.pageIdToViewId = {}, this.delayHandler = function(e) {
            var n = (0, u.isArray)(e) ? e : e.getEntries();
            Array.isArray(n) && 0 !== n.length && setTimeout((function() {
              n.forEach((function(e) {
                if (e.pageId) {
                  var n = t.pageIdToViewId["pageId:".concat(e.pageId)];
                  if (n && t.perfMap[n]) return void t.perfMap[n].entries.push(e)
                }
                if (t.currentViewId) {
                  var r = t.perfMap[t.currentViewId];
                  if (r) {
                    var o = e.path;
                    o || "navigation" !== e.entryType || (o = e.name), r.path !== o || r.entries.push(e)
                  }
                }
              }))
            }), 200)
          }, this.onLoad = function() {
            var e = (0, c.getCurPage)(),
              n = (0, c.getCurView)(t.ctx),
              r = e.__rum_view_id,
              o = (0, u.isFunction)(e.getPageId) ? e.getPageId() : null;
            r in t.perfMap || (t.currentViewId = r, o && (t.pageIdToViewId[o.toString()] = r), t.perfMap[r] = {
              view: n,
              path: e.is || e.route,
              pageId: o,
              entries: [],
              timer: setTimeout((function() {
                t.flushByViewId(r, "timeout")
              }), 1e4)
            })
          }, this.flushByViewId = function(e) {
            var n = t.perfMap[e];
            n && (t.perfMap[e] = !1, clearTimeout(n.timer), n.entries.length && t.parsePerf(n))
          }
        }), [{
          key: "setup",
          value: function(e, t) {
            var n = e.getConfig().collectors,
              r = this;
            if (!1 !== (void 0 === n ? {} : n).perf && (this.ctx = e, this.sendEvent = t, l && l.getEntries)) {
              var o;
              if (this.perfMap = {}, "wechat" === s.appName || "alipay" === s.appName) o = ["navigation", "render"];
              else {
                if ("bytedance" !== s.appName) return;
                o = ["launch", "paint", "navigation"]
              }
              this.delayHandler(l.getEntries().filter((function(e) {
                return o.includes(e.entryType)
              }))), this.perfObserver = l.createObserver(this.delayHandler), this.perfObserver.observe({
                entryTypes: o
              }), this.onUnload = function() {
                var e = this.__rum_view_id;
                e && r.flushByViewId(e)
              }, (0, f.addPageListener)("onLoad", this.onLoad), (0, f.addPageListener)("onHide", this.onUnload), (0, f.addPageListener)("onUnload", this.onUnload)
            }
          }
        }, {
          key: "parsePerf",
          value: function(e) {
            var t, n, r, o, i, a = e.entries,
              u = e.view;
            a.forEach((function(e) {
              if (e.startTime) switch (e.name) {
                case "firstRender":
                case "first-render":
                  n = e;
                  break;
                case "firstPaint":
                case "first-paint":
                  r = e;
                  break;
                case "firstContentfulPaint":
                case "first-contentful-paint":
                  o = e;
                  break;
                case "largestContentfulPaint":
                case "largest-contentful-paint":
                  if (i && i.startTime > e.startTime) break;
                  i = e;
                  break;
                default:
                  "navigation" === e.entryType && (t = e)
              }
            }));
            var s = {};
            t && (n && (s.first_render = n.duration ? n.duration : n.startTime - t.startTime), r && (s.first_paint = r.startTime - t.startTime), o && (s.first_contentful_paint = o.startTime - t.startTime), i && (s.largest_contentful_paint = i.startTime - t.startTime), this.sendPerf(s, {
              view: u,
              timestamp: t.startTime,
              timing_data: JSON.stringify(t)
            }))
          }
        }, {
          key: "sendPerf",
          value: function(e, t) {
            for (var n = Object.keys(e), r = 0; r < n.length; r++) {
              var o = n[r],
                i = e[o];
              (0 >= i || isNaN(i) || 6e5 < i) && delete e[o]
            }
            if (Object.keys(e)) {
              var a = this.ctx.session.getBaseEvent();
              this.sendEvent(Object.assign(Object.assign(Object.assign(Object.assign({}, a), {
                event_type: u.RumEventType.VIEW,
                type: "perf"
              }), t), e))
            }
          }
        }, {
          key: "destroy",
          value: function() {
            this.perfObserver && this.perfObserver.disconnect(), (0, f.removePageListener)("onLoad", this.onLoad), (0, f.removePageListener)("onHide", this.onUnload), (0, f.removePageListener)("onUnload", this.onUnload)
          }
        }]);
      t.default = p
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        for (var n, r = 0; r < t.length; r++)(n = t[r]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, a(n.key), n)
      }

      function a(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var u = r(0),
        s = r(0),
        c = r(30),
        f = r(1),
        l = function(e, t, n) {
          return t && i(e.prototype, t), n && i(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
          }), e
        }((function e() {
          var t = this;
          (function(e, t) {
            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
          })(this, e), this.name = "exception-collector", this.origin = {
            consoleError: console.error
          }, this.events = {
            onError: null,
            onUnhandledRejection: null,
            onPageNotFound: null,
            onLazyLoadError: null
          }, this.errorHandle = function(e) {
            var n, r, o, i, a, s, c = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : "";
            if (e instanceof Object) n = e.name, r = e.message, o = e.filename || e.fileName, i = e.stack, a = e.lineno || e.lineNumber, s = e.colno || e.columnNumber;
            else if ("string" == typeof e) {
              i = e;
              var f = t.getErrorByStack(e);
              f && (n = f.name, r = f.message)
            }
            if (n && r && !t.errorFilter(e)) {
              var l = t.ctx.session.getBaseEvent();
              t.sendEvent(Object.assign(Object.assign({}, l), {
                event_type: u.RumEventType.EXCEPTION,
                source: c,
                type: "error",
                name: n,
                message: r,
                file: o,
                stack: i,
                line: a,
                column: s
              }))
            }
          }
        }), [{
          key: "setup",
          value: function(e, t) {
            this.ctx = e, this.sendEvent = t, this.hackOrigin()
          }
        }, {
          key: "hackOrigin",
          value: function() {
            var e = this,
              t = this,
              n = this.ctx.getConfig().collectors,
              r = void 0 === n ? {} : n;
            !1 !== r.jsError && Object.keys(this.events).forEach((function(n) {
              e.events[n] = function(e) {
                e.reason && e.reason instanceof Error && (e = e.reason), t.errorHandle(e, n)
              }, (0, c.addAppListener)(n, e.events[n])
            })), !r.consoleError || ("swan" === f.appName && Object.defineProperty(console, "error", {
              writable: !0
            }), (0, s.interceptFunction)(console, "error", (function(e) {
              t.errorHandle(e, "console.error")
            })))
          }
        }, {
          key: "getErrorByStack",
          value: function() {
            var e = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : "",
              t = e.split("\n");
            if (!(2 > t.length))
              for (var n = 1; n < t.length; n++)
                if (t[n].trim().startsWith("at ")) {
                  var r = t[n - 1],
                    o = r.indexOf(": ");
                  return {
                    name: r.substring(0, o),
                    message: r.substring(o + 2)
                  }
                }
          }
        }, {
          key: "errorFilter",
          value: function(e) {
            var t = (this.ctx.getConfig().filters || {}).exception;
            return !!t && ((0, u.isArray)(t) || (t = [t]), (0, u.matchList)(t, e.name, !0) || (0, u.matchList)(t, e.message, !0) || (0, u.matchList)(t, e.stack, !0))
          }
        }, {
          key: "destroy",
          value: function() {
            var e = this,
              t = this.origin;
            t.consoleError && (console.error = t.consoleError), Object.keys(this.events).forEach((function(t) {
              (0, u.isFunction)(e.events[t]) && (0, c.removeAppListener)(t, e.events[t])
            }))
          }
        }]);
      t.default = l
    }, function(e, t, n) {
      function r(e) {
        return function(e) {
          if (Array.isArray(e)) return o(e)
        }(e) || function(e) {
          if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e)
        }(e) || function(e, t) {
          if (e) {
            if ("string" == typeof e) return o(e, t);
            var n = Object.prototype.toString.call(e).slice(8, -1);
            return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? o(e, t) : void 0
          }
        }(e) || function() {
          throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
        }()
      }

      function o(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var n = 0, r = Array(t); n < t; n++) r[n] = e[n];
        return r
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.removeAppListener = t.addAppListener = t.originalApp = void 0;
      var i = n(0),
        a = n(1),
        u = {
          onLaunch: [],
          "*": []
        };

      function s(e) {
        Object.keys(u).forEach((function(t) {
          "swan" === a.appName && Object.defineProperty(e, t, {
            writable: !0
          }), "*" === t || (0, i.interceptFunction)(e, t, (function() {
            for (var e = this, n = arguments.length, o = Array(n), i = 0; i < n; i++) o[i] = arguments[i];
            var a = [].concat(r(u[t]), r(u["*"]));
            a.forEach((function(t) {
              t.apply(e, o)
            }))
          }))
        }))
      }
      t.originalApp = App, App = function() {
        for (var e = arguments.length, n = Array(e), r = 0; r < e; r++) n[r] = arguments[r];
        try {
          s.apply(this, n)
        } catch (e) {}
        return t.originalApp.apply(this, n)
      }, Object.keys(t.originalApp).forEach((function(e) {
        App[e] = t.originalApp[e]
      })), t.addAppListener = function(e, t) {
        e in u || (u[e] = []), u[e].push(t)
      }, t.removeAppListener = function(e, t) {
        if (e in u) {
          var n = u[e],
            r = n.indexOf(t);
          n.splice(r, 1)
        }
      }
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i() {
        /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
        function e(e, t, n) {
          return Object.defineProperty(e, t, {
            value: n,
            enumerable: !0,
            configurable: !0,
            writable: !0
          }), e[t]
        }

        function t(e, t, n, o) {
          var i = t && t.prototype instanceof r ? t : r,
            a = Object.create(i.prototype),
            u = new y(o || []);
          return w(a, "_invoke", {
            value: f(e, n, u)
          }), a
        }

        function n(e, t, n) {
          try {
            return {
              type: "normal",
              arg: e.call(t, n)
            }
          } catch (e) {
            return {
              type: "throw",
              arg: e
            }
          }
        }

        function r() {}

        function a() {}

        function u() {}

        function s(t) {
          ["next", "throw", "return"].forEach((function(n) {
            e(t, n, (function(e) {
              return this._invoke(n, e)
            }))
          }))
        }

        function c(e, t) {
          function r(i, a, u, s) {
            var c = n(e[i], e, a);
            if ("throw" !== c.type) {
              var f = c.arg,
                l = f.value;
              return l && "object" == o(l) && b.call(l, "__await") ? t.resolve(l.__await).then((function(e) {
                r("next", e, u, s)
              }), (function(e) {
                r("throw", e, u, s)
              })) : t.resolve(l).then((function(e) {
                f.value = e, u(f)
              }), (function(e) {
                return r("throw", e, u, s)
              }))
            }
            s(c.arg)
          }
          var i;
          w(this, "_invoke", {
            value: function(e, n) {
              function o() {
                return new t((function(t, o) {
                  r(e, n, t, o)
                }))
              }
              return i = i ? i.then(o, o) : o()
            }
          })
        }

        function f(e, t, r) {
          var o = "suspendedStart";
          return function(i, a) {
            if ("executing" === o) throw Error("Generator is already running");
            if ("completed" === o) {
              if ("throw" === i) throw a;
              return {
                value: m,
                done: !0
              }
            }
            for (r.method = i, r.arg = a;;) {
              var u = r.delegate;
              if (u) {
                var s = l(u, r);
                if (s) {
                  if (s === j) continue;
                  return s
                }
              }
              if ("next" === r.method) r.sent = r._sent = r.arg;
              else if ("throw" === r.method) {
                if ("suspendedStart" === o) throw o = "completed", r.arg;
                r.dispatchException(r.arg)
              } else "return" === r.method && r.abrupt("return", r.arg);
              o = "executing";
              var c = n(e, t, r);
              if ("normal" === c.type) {
                if (o = r.done ? "completed" : "suspendedYield", c.arg === j) continue;
                return {
                  value: c.arg,
                  done: r.done
                }
              }
              "throw" === c.type && (o = "completed", r.method = "throw", r.arg = c.arg)
            }
          }
        }

        function l(e, t) {
          var r = t.method,
            o = e.iterator[r];
          if (o === m) return t.delegate = null, "throw" === r && e.iterator.return && (t.method = "return", t.arg = m, l(e, t), "throw" === t.method) || "return" !== r && (t.method = "throw", t.arg = new TypeError("The iterator does not provide a '" + r + "' method")), j;
          var i = n(o, e.iterator, t.arg);
          if ("throw" === i.type) return t.method = "throw", t.arg = i.arg, t.delegate = null, j;
          var a = i.arg;
          return a ? a.done ? (t[e.resultName] = a.value, t.next = e.nextLoc, "return" !== t.method && (t.method = "next", t.arg = m), t.delegate = null, j) : a : (t.method = "throw", t.arg = new TypeError("iterator result is not an object"), t.delegate = null, j)
        }

        function p(e) {
          var t = {
            tryLoc: e[0]
          };
          1 in e && (t.catchLoc = e[1]), 2 in e && (t.finallyLoc = e[2], t.afterLoc = e[3]), this.tryEntries.push(t)
        }

        function v(e) {
          var t = e.completion || {};
          t.type = "normal", delete t.arg, e.completion = t
        }

        function y(e) {
          this.tryEntries = [{
            tryLoc: "root"
          }], e.forEach(p, this), this.reset(!0)
        }

        function d(e) {
          if (e || "" === e) {
            var t = e[E];
            if (t) return t.call(e);
            if ("function" == typeof e.next) return e;
            if (!isNaN(e.length)) {
              var n = -1,
                r = function t() {
                  for (; ++n < e.length;)
                    if (b.call(e, n)) return t.value = e[n], t.done = !1, t;
                  return t.value = m, t.done = !0, t
                };
              return r.next = r
            }
          }
          throw new TypeError(o(e) + " is not iterable")
        }
        i = function() {
          return h
        };
        var m, h = {},
          g = Object.prototype,
          b = g.hasOwnProperty,
          w = Object.defineProperty || function(e, t, n) {
            e[t] = n.value
          },
          O = "function" == typeof Symbol ? Symbol : {},
          E = O.iterator || "@@iterator",
          S = O.asyncIterator || "@@asyncIterator",
          _ = O.toStringTag || "@@toStringTag";
        try {
          e({}, "")
        } catch (S) {
          S = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(S);
          e = function(e, t, n) {
            return e[t] = n
          }
        }
        h.wrap = t;
        var j = {},
          P = {};
        e(P, E, (function() {
          return this
        }));
        var T = Object.getPrototypeOf,
          k = T && T(T(d([])));
        k && k !== g && b.call(k, E) && (P = k);
        var x = u.prototype = r.prototype = Object.create(P);
        return a.prototype = u, w(x, "constructor", {
          value: u,
          configurable: !0
        }), w(u, "constructor", {
          value: a,
          configurable: !0
        }), a.displayName = e(u, _, "GeneratorFunction"), h.isGeneratorFunction = function(e) {
          var t = "function" == typeof e && e.constructor;
          return !!t && (t === a || "GeneratorFunction" === (t.displayName || t.name))
        }, h.mark = function(t) {
          return Object.setPrototypeOf ? Object.setPrototypeOf(t, u) : (t.__proto__ = u, e(t, _, "GeneratorFunction")), t.prototype = Object.create(x), t
        }, h.awrap = function(e) {
          return {
            __await: e
          }
        }, s(c.prototype), e(c.prototype, S, (function() {
          return this
        })), h.AsyncIterator = c, h.async = function(e, n, r, o, i) {
          void 0 === i && (i = Promise);
          var a = new c(t(e, n, r, o), i);
          return h.isGeneratorFunction(n) ? a : a.next().then((function(e) {
            return e.done ? e.value : a.next()
          }))
        }, s(x), e(x, _, "Generator"), e(x, E, (function() {
          return this
        })), e(x, "toString", (function() {
          return "[object Generator]"
        })), h.keys = function(e) {
          var t = Object(e),
            n = [];
          for (var r in t) n.push(r);
          return n.reverse(),
            function e() {
              for (; n.length;) {
                var r = n.pop();
                if (r in t) return e.value = r, e.done = !1, e
              }
              return e.done = !0, e
            }
        }, h.values = d, y.prototype = {
          constructor: y,
          reset: function(e) {
            if (this.prev = 0, this.next = 0, this.sent = this._sent = m, this.done = !1, this.delegate = null, this.method = "next", this.arg = m, this.tryEntries.forEach(v), !e)
              for (var t in this) "t" === t.charAt(0) && b.call(this, t) && !isNaN(+t.slice(1)) && (this[t] = m)
          },
          stop: function() {
            this.done = !0;
            var e = this.tryEntries[0].completion;
            if ("throw" === e.type) throw e.arg;
            return this.rval
          },
          dispatchException: function(e) {
            function t(t, r) {
              return i.type = "throw", i.arg = e, n.next = t, r && (n.method = "next", n.arg = m), !!r
            }
            if (this.done) throw e;
            for (var n = this, r = this.tryEntries.length - 1; 0 <= r; --r) {
              var o = this.tryEntries[r],
                i = o.completion;
              if ("root" === o.tryLoc) return t("end");
              if (o.tryLoc <= this.prev) {
                var a = b.call(o, "catchLoc"),
                  u = b.call(o, "finallyLoc");
                if (a && u) {
                  if (this.prev < o.catchLoc) return t(o.catchLoc, !0);
                  if (this.prev < o.finallyLoc) return t(o.finallyLoc)
                } else if (a) {
                  if (this.prev < o.catchLoc) return t(o.catchLoc, !0)
                } else {
                  if (!u) throw Error("try statement without catch or finally");
                  if (this.prev < o.finallyLoc) return t(o.finallyLoc)
                }
              }
            }
          },
          abrupt: function(e, t) {
            for (var n, r = this.tryEntries.length - 1; 0 <= r; --r)
              if ((n = this.tryEntries[r]).tryLoc <= this.prev && b.call(n, "finallyLoc") && this.prev < n.finallyLoc) {
                var o = n;
                break
              } o && ("break" === e || "continue" === e) && o.tryLoc <= t && t <= o.finallyLoc && (o = null);
            var i = o ? o.completion : {};
            return i.type = e, i.arg = t, o ? (this.method = "next", this.next = o.finallyLoc, j) : this.complete(i)
          },
          complete: function(e, t) {
            if ("throw" === e.type) throw e.arg;
            return "break" === e.type || "continue" === e.type ? this.next = e.arg : "return" === e.type ? (this.rval = this.arg = e.arg, this.method = "return", this.next = "end") : "normal" === e.type && t && (this.next = t), j
          },
          finish: function(e) {
            for (var t, n = this.tryEntries.length - 1; 0 <= n; --n)
              if ((t = this.tryEntries[n]).finallyLoc === e) return this.complete(t.completion, t.afterLoc), v(t), j
          },
          catch: function(e) {
            for (var t, n = this.tryEntries.length - 1; 0 <= n; --n)
              if ((t = this.tryEntries[n]).tryLoc === e) {
                var r = t.completion;
                if ("throw" === r.type) {
                  var o = r.arg;
                  v(t)
                }
                return o
              } throw Error("illegal catch attempt")
          },
          delegateYield: function(e, t, n) {
            return this.delegate = {
              iterator: d(e),
              resultName: t,
              nextLoc: n
            }, "next" === this.method && (this.arg = m), j
          }
        }, h
      }

      function a(e, t) {
        for (var n, r = 0; r < t.length; r++)(n = t[r]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, u(n.key), n)
      }

      function u(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      var s = this && this.__awaiter || function(e, t, n, r) {
        function o(e) {
          return e instanceof n ? e : new n((function(t) {
            t(e)
          }))
        }
        return new(n || (n = Promise))((function(n, i) {
          function a(e) {
            try {
              s(r.next(e))
            } catch (e) {
              i(e)
            }
          }

          function u(e) {
            try {
              s(r.throw(e))
            } catch (e) {
              i(e)
            }
          }

          function s(e) {
            e.done ? n(e.value) : o(e.value).then(a, u)
          }
          s((r = r.apply(e, t || [])).next())
        }))
      };
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var c = r(0),
        f = r(1),
        l = r(32),
        p = r(6),
        v = r(3),
        y = r(33),
        d = function(e, t, n) {
          return t && a(e.prototype, t), n && a(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
          }), e
        }((function e() {
          var t = this;
          (function(e, t) {
            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
          })(this, e), this.name = "api-collector", this.origin = {
            request: f.default.request,
            httpRequest: f.default.httpRequest
          }, this.sendApi = function(e, n, r) {
            return s(t, void 0, void 0, i().mark((function t() {
              var o, a, u, s, f, l, v, d, m, h, g, b, w, O;
              return i().wrap((function(t) {
                for (;;) switch (t.prev = t.next) {
                  case 0:
                    if (o = this.ctx.getConfig(), a = o.evaluateApi, u = n.statusCode, s = n.status, f = n.errMsg, l = n.errorMessage, v = n.message, (d = n.profile) && (m = this.parseProfile(d), h = JSON.stringify(d)), !(0, c.isFunction)(a)) {
                      t.next = 14;
                      break
                    }
                    return t.prev = 4, 0 === e.success && (g = new Error("request error")), t.next = 8, a(r, n, g);
                  case 8:
                    b = t.sent, e = Object.assign(Object.assign({}, e), (0, y.reviseApiAttr)(b)), t.next = 14;
                    break;
                  case 12:
                    t.prev = 12, t.t0 = t.catch(4);
                  case 14:
                    if (w = this.ctx.session.getBaseEvent(), !((O = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, w), {
                        event_type: c.RumEventType.RESOURCE,
                        type: "api",
                        status_code: s || u,
                        message: l || f || v,
                        duration: (0, p.getCurrentTime)() - e.timestamp
                      }), e), m), {
                        timing_data: h
                      })).duration > c.ONE_HOUR)) {
                      t.next = 18;
                      break
                    }
                    return t.abrupt("return");
                  case 18:
                    this.sendEvent(O);
                  case 19:
                  case "end":
                    return t.stop()
                }
              }), t, this, [
                [4, 12]
              ])
            })))
          }
        }), [{
          key: "setup",
          value: function(e, t) {
            var n = this,
              r = e.getConfig().collectors;
            !1 === (void 0 === r ? {} : r).api || (this.ctx = e, this.sendEvent = t, Object.keys(this.origin).forEach((function(e) {
              return n.hackRequest(e)
            })))
          }
        }, {
          key: "injectTracing",
          value: function(e, t) {
            var n, r = this.ctx.session,
              o = this.ctx.getConfig(),
              i = o.tracing,
              a = o.pid,
              u = o.version,
              s = void 0 === u ? "1.0.0" : u,
              l = (0, c.parseTracingOptions)(i),
              p = l.enable,
              v = l.sample,
              y = l.propagatorTypes,
              d = l.allowedUrls,
              m = l.tracestate,
              h = !(void 0 !== m) || m,
              g = l.baggage,
              b = void 0 !== g && g;
            if (p) {
              var w = (0, c.find)(d, (function(t) {
                return (0, c.matchList)([t.match], e.url)
              }));
              if (w) {
                var O = w.propagatorTypes;
                0 === O.length && (O = y);
                var E = !1;
                O.includes("sw8") && (O = ["sw8"], E = !0);
                var S = E ? (0, c.generateGUID)() : (0, c.generateTraceId)(),
                  _ = E ? (0, c.generateGUID)() : (0, c.generateSpanId)(),
                  j = (0, c.performDraw)(v),
                  P = r.getUserId(),
                  T = r.getSessionId(),
                  k = h ? "rum=v2&miniapp&".concat(a, "&").concat(T, "&").concat(P) : void 0,
                  x = b ? "rum=v2,appType=miniapp,pid=".concat(a, ",sid=").concat(T, ",uid=").concat(P) : void 0,
                  N = (0, c.makeTracingHeaders)(S, _, j, O, {
                    tracestate: k,
                    baggage: x,
                    appId: a,
                    appVersion: s,
                    viewName: null === (n = e.view) || void 0 === n ? void 0 : n.name,
                    host: "miniapp_".concat(f.appName)
                  });
                j && (e.trace_id = S, e.trace_data = JSON.stringify({
                  spanId: _,
                  sample: v,
                  sampled: j,
                  headers: N
                })), t(N)
              }
            }
          }
        }, {
          key: "hackRequest",
          value: function(e) {
            var t = this;
            (0, c.isFunction)(f.default[e]) && Object.defineProperty(f.default, e, {
              configurable: !0,
              enumerable: !0,
              writable: !0,
              value: function() {
                for (var n = arguments.length, r = Array(n), o = 0; o < n; o++) r[o] = arguments[o];
                return r[0] = t.rebuildRequestOptions(r[0]), t.origin[e].apply(this, r)
              }
            })
          }
        }, {
          key: "rebuildRequestOptions",
          value: function(e) {
            var t = this,
              n = this.ctx.getConfig().filters || {},
              r = e.url,
              o = e.success,
              i = e.fail,
              a = (0, p.getCurrentTime)();
            if ((0, l.isEndpoint)(this.ctx, r) || (0, c.urlMatch)(r, n.resource)) return e;
            var u = Object.assign({}, e),
              s = {
                view: (0, v.getCurView)(this.ctx),
                timestamp: a,
                url: r,
                name: this.getResourceName(r),
                method: e.method || "GET"
              };
            return this.injectTracing(s, (function(e) {
              u[f.HEADER_KEY] = Object.assign(Object.assign({}, u[f.HEADER_KEY]), e)
            })), u.success = function() {
              s.success = 1;
              for (var e = arguments.length, n = Array(e), r = 0; r < e; r++) n[r] = arguments[r];
              t.sendApi(s, n[0], u), o && o.apply(this, n)
            }, u.fail = function() {
              s.success = 0;
              for (var e = arguments.length, n = Array(e), r = 0; r < e; r++) n[r] = arguments[r];
              t.sendApi(s, n[0], u), i && i.apply(this, n)
            }, u
          }
        }, {
          key: "getResourceName",
          value: function(e) {
            var t = this.ctx.getConfig().parseResourceName;
            return (0, c.isFunction)(t) ? t(e) : (0, l.getPathByURL)(e)
          }
        }, {
          key: "parseProfile",
          value: function(e) {
            function t(t, n) {
              t in e && 0 < e[t] && (r[n] = e[t])
            }

            function n(t, n, o) {
              if (t in e && t in e) {
                var i = e[n] - e[t];
                0 < i && (r[o] = i)
              }
            }
            var r = {};
            return t("domainLookup", "dns_duration"), t("connect", "connect_duration"), t("SSLconnection", "ssl_duration"), t("Waiting", "first_byte_duration"), t("totalTime", "duration"), t("receivedBytedCount", "size"), n("connectStart", "connectEnd", "connect_duration"), n("SSLconnectionStart", "SSLconnectionEnd", "ssl_duration"), n("domainLookUpStart", "domainLookUpEnd", "dns_duration"), n("redirectStart", "redirectEnd", "redirect_duration"), n("responseStart", "requestStart", "first_byte_duration"), n("responseStart", "requestEnd", "download_duration"), n("redirectStart", "responseEnd", "duration"), Object.keys(r).forEach((function(e) {
              r[e] > c.ONE_HOUR && delete r[e]
            })), r
          }
        }]);
      t.default = d
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.getURL = t.isEndpoint = t.getPathByURL = void 0, t.getPathByURL = function(e) {
        var t = !!(1 < arguments.length && void 0 !== arguments[1]) && arguments[1];
        try {
          var n = o(e);
          return t ? n.hash : n.pathname
        } catch (e) {
          return ""
        }
      }, t.isEndpoint = function(e, t) {
        var n = e.getConfig().endpoint;
        try {
          return o(t).hostname === o(n).hostname
        } catch (e) {
          return !1
        }
      };
      var r = /^(?:([^:\/?#]+):\/\/)?((?:([^\/?#@]*)@)?([^\/?#:]*)(?:\:(\d*))?)?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n)*))?/i;

      function o(e) {
        var t = decodeURIComponent(e).match(r),
          n = (t[3] || "").split(":").length ? (t[2] || "").replace(/(.*\@)/, "") : t[2];
        return {
          uri: t[0],
          protocol: t[1],
          host: n,
          hostname: t[4],
          port: t[5],
          pathname: t[6],
          search: t[7],
          hash: t[8]
        }
      }
      t.getURL = o
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.reviseApiAttr = void 0;
      var r = n(0);
      t.reviseApiAttr = function(e) {
        if ((0, r.isObject)(e)) {
          var t = {};
          (0, r.isString)(e.name) && (t.name = e.name.substring(0, 1e3)), (0, r.isString)(e.message) && (t.message = e.message.substring(0, 1e3)), "success" in e && (t.success = e.success ? 1 : 0), (0, r.isNumber)(e.duration) && 0 <= e.duration && (t.duration = e.duration), (0, r.isString)(e.status_code) && (t.status_code = e.status_code.substring(0, 100)), (0, r.isNumber)(e.status_code) && (t.status_code = e.status_code), (0, r.isString)(e.snapshots) && (t.snapshots = e.snapshots.substring(0, 5e3));
          var n = (0, r.verifyProperties)(e.properties);
          return n && (t.properties = n), t
        }
      }
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        for (var n, r = 0; r < t.length; r++)(n = t[r]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, a(n.key), n)
      }

      function a(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var u = r(0),
        s = r(4),
        c = function(e, t, n) {
          return t && i(e.prototype, t), n && i(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
          }), e
        }((function e() {
          var t = this;
          (function(e, t) {
            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
          })(this, e), this.name = "action-collector", this.DELAY_TIME = 300, this.events = ["tap"], this.onEvent = function(e) {
            if (e && t.events.includes(e.type) && e.target) {
              var n = e.target,
                r = e.currentTarget,
                o = e.type,
                i = r.dataset,
                a = r.id;
              if (a || r === n || (a = n.id, i = n.dataset), !a) return;
              var s = t.ctx.session.getBaseEvent(),
                c = Object.assign(Object.assign({}, s), {
                  event_type: u.RumEventType.ACTION,
                  type: o,
                  name: a,
                  snapshots: JSON.stringify({
                    dataset: i
                  }).substring(0, 1e3)
                });
              clearTimeout(t.timer), t.lastEvent ? t.lastEvent.name === c.name ? t.lastEvent.times += 1 : t.sendAction() : t.lastEvent = c, t.timer = setTimeout(t.sendAction, t.DELAY_TIME)
            }
          }, this.sendAction = function() {
            t.sendEvent(t.lastEvent), t.lastEvent = null
          }
        }), [{
          key: "setup",
          value: function(e, t) {
            var n = e.getConfig().collectors;
            !1 === (void 0 === n ? {} : n).action || (this.ctx = e, this.sendEvent = t, (0, s.addPageListener)("*", this.onEvent))
          }
        }, {
          key: "destroy",
          value: function() {
            (0, s.removePageListener)("*", this.onEvent)
          }
        }]);
      t.default = c
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        for (var n, r = 0; r < t.length; r++)(n = t[r]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, a(n.key), n)
      }

      function a(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var u = r(0),
        s = r(1),
        c = function(e, t, n) {
          return t && i(e.prototype, t), n && i(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
          }), e
        }((function e() {
          var t = this;
          (function(e, t) {
            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
          })(this, e), this.name = "application-collector", this.observerHandler = function(e) {
            var n, r, o = (0, u.isArray)(e) ? e : e.getEntries();
            if ((0, u.isFunction)(s.default.getLaunchOptionsSync) && (r = null === (n = s.default.getLaunchOptionsSync()) || void 0 === n ? void 0 : n.scene), Array.isArray(o) && 0 < o.length)
              for (var i, a = 0; a < o.length; a++)
                if ("number" == typeof(i = o[a]).duration && 0 < i.duration) {
                  var c = t.ctx.session.getBaseEvent(),
                    f = Object.assign(Object.assign({}, c), {
                      event_type: u.RumEventType.APPLICATION,
                      state: "",
                      duration: 0,
                      snapshots: JSON.stringify(i),
                      scene: r
                    });
                  switch (i.name) {
                    case "appLaunch":
                    case "app-click":
                      f.state = "cold_lunch", f.duration = i.duration;
                      break;
                    case "downloadPackage":
                    case "miniprogram-package":
                      f.state = "download_package", f.duration = i.duration;
                      break;
                    case "evaluateScript":
                    case "app-service":
                      "__APP__" === (i.moduleName || i.packageName) && (f.state = "evaluate_script", f.duration = i.duration)
                  }
                  f.state && 0 < f.duration && 6e5 > f.duration && t.sendEvent(f)
                }
          }
        }), [{
          key: "setup",
          value: function(e, t) {
            var n = e.getConfig().collectors;
            if (!1 !== (void 0 === n ? {} : n).application) {
              this.ctx = e, this.sendEvent = t;
              var r = (0, s.getPerformance)();
              if (r && (0, u.isFunction)(r.createObserver)) {
                var o = [];
                if ("wechat" === s.appName || "alipay" === s.appName) o = ["navigation", "script", "loadPackage"];
                else {
                  if ("bytedance" !== s.appName) return;
                  o = ["launch", "evaluate", "resource"]
                }
                this.observerHandler(r.getEntries().filter((function(e) {
                  return o.includes(e.entryType)
                }))), this.perfObserver = r.createObserver(this.observerHandler), this.perfObserver.observe({
                  entryTypes: o
                })
              }
            }
          }
        }, {
          key: "destroy",
          value: function() {
            this.perfObserver && this.perfObserver.disconnect()
          }
        }]);
      t.default = c
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        for (var n, r = 0; r < t.length; r++)(n = t[r]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, a(n.key), n)
      }

      function a(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var u = r(6),
        s = r(3),
        c = function(e, t, n) {
          return t && i(e.prototype, t), n && i(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
          }), e
        }((function e() {
          (function(e, t) {
            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
          })(this, e), this.name = "default-processor"
        }), [{
          key: "process",
          value: function(e) {
            var t = e.getRumEvent(),
              n = (0, s.getCurView)(e);
            return Object.assign({
              timestamp: (0, u.getCurrentTime)(),
              session_id: e.session.getSessionId(),
              event_id: e.session.getEventId(),
              view: n
            }, t)
          }
        }]);
      t.default = c
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        for (var n, r = 0; r < t.length; r++)(n = t[r]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, a(n.key), n)
      }

      function a(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var u = r(0),
        s = r(4),
        c = function(e, t, n) {
          return t && i(e.prototype, t), n && i(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
          }), e
        }((function e() {
          var t = this;
          (function(e, t) {
            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
          })(this, e), this.name = "session-processor", this.update = function(e) {
            e && !e.isTrusted || t.ctx.session.updateSession()
          }
        }), [{
          key: "setup",
          value: function(e) {
            var t = this;
            this.ctx = e, this.update = (0, u.debounce)(this.update, u.ONE_SECOND), (0, s.addPageListener)("*", (function(e) {
              e && e.type && e.target && t.update()
            }))
          }
        }, {
          key: "process",
          value: function(e) {
            return this.update(), e.getRumEvent()
          }
        }]);
      t.default = c
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
      }

      function a(e, t) {
        for (var n, r = 0; r < t.length; r++)(n = t[r]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, u(n.key), n)
      }

      function u(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }

      function s(e, t, n) {
        return t = c(t),
          function(e, t) {
            if (t && ("object" === o(t) || "function" == typeof t)) return t;
            if (void 0 !== t) throw new TypeError("Derived constructors may only return object or undefined");
            return function(e) {
              if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
              return e
            }(e)
          }(e, function() {
            try {
              var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], (function() {})))
            } catch (e) {}
            return !!e
          }() ? Reflect.construct(t, n || [], c(e).constructor) : t.apply(e, n))
      }

      function c(e) {
        return (c = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e) {
          return e.__proto__ || Object.getPrototypeOf(e)
        })(e)
      }

      function f(e, t) {
        if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
        e.prototype = Object.create(t && t.prototype, {
          constructor: {
            value: e,
            writable: !0,
            configurable: !0
          }
        }), Object.defineProperty(e, "prototype", {
          writable: !1
        }), t && function(e, t) {
          (Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e, t) {
            return e.__proto__ = t, e
          })(e, t)
        }(e, t)
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      });
      var l = r(0),
        p = r(1),
        v = function(e) {
          function t() {
            var e;
            return i(this, t), (e = s(this, t, arguments)).name = "miniapp-reporter", e
          }
          return f(t, e),
            function(e, t, n) {
              return t && a(e.prototype, t), n && a(e, n), Object.defineProperty(e, "prototype", {
                writable: !1
              }), e
            }(t, [{
              key: "init",
              value: function() {
                var e = this;
                p.default.onAppHide((function() {
                  e.flushEventQueue()
                }))
              }
            }, {
              key: "request",
              value: function(e, t) {
                var n = e.getConfig();
                try {
                  t.app.type = l.AppType.miniapp, t._v = p.VERSION, (p.default.request || p.default.httpRequest)({
                    url: n.endpoint,
                    method: "POST",
                    dataType: "text",
                    data: JSON.stringify(t)
                  })
                } catch (e) {
                  console.warn("[arms] sendRequest fail", e)
                }
              }
            }])
        }(l.Reporter);
      t.default = v
    }, function(e, t, n) {
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.getNetType = void 0;
      var r = n(1);
      t.getNetType = function(e) {
        r.default.getNetworkType && r.default.getNetworkType({
          success: function(t) {
            var n = t.networkType || t.subtype;
            e(n ? n.toLowerCase() : void 0)
          }
        }), r.default.onNetworkStatusChange && r.default.onNetworkStatusChange((function(t) {
          var n = t.networkType || t.subtype;
          e(n ? n.toLowerCase() : void 0)
        }))
      }
    }, function(e, t, r) {
      function o(e) {
        return (o = "function" == typeof Symbol && "symbol" == n(Symbol.iterator) ? function(e) {
          return n(e)
        } : function(e) {
          return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : n(e)
        })(e)
      }

      function i(e, t) {
        return function(e) {
          if (Array.isArray(e)) return e
        }(e) || function(e, t) {
          var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
          if (null != n) {
            var r, o, i, a, u = [],
              s = !0,
              c = !1;
            try {
              if (i = (n = n.call(e)).next, 0 === t) {
                if (Object(n) !== n) return;
                s = !1
              } else
                for (; !(s = (r = i.call(n)).done) && (u.push(r.value), u.length !== t); s = !0);
            } catch (s) {
              c = !0, o = s
            } finally {
              try {
                if (!s && null != n.return && (a = n.return(), Object(a) !== a)) return
              } finally {
                if (c) throw o
              }
            }
            return u
          }
        }(e, t) || function(e, t) {
          if (e) {
            if ("string" == typeof e) return a(e, t);
            var n = Object.prototype.toString.call(e).slice(8, -1);
            return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? a(e, t) : void 0
          }
        }(e, t) || function() {
          throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
        }()
      }

      function a(e, t) {
        (null == t || t > e.length) && (t = e.length);
        for (var n = 0, r = Array(t); n < t; n++) r[n] = e[n];
        return r
      }

      function u(e, t) {
        for (var n, r = 0; r < t.length; r++)(n = t[r]).enumerable = n.enumerable || !1, n.configurable = !0, "value" in n && (n.writable = !0), Object.defineProperty(e, s(n.key), n)
      }

      function s(e) {
        var t = function(e, t) {
          if ("object" != o(e) || !e) return e;
          var n = e[Symbol.toPrimitive];
          if (void 0 !== n) {
            var r = n.call(e, t || "default");
            if ("object" != o(r)) return r;
            throw new TypeError("@@toPrimitive must return a primitive value.")
          }
          return ("string" === t ? String : Number)(e)
        }(e, "string");
        return "symbol" == o(t) ? t : t + ""
      }
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.RumSession = void 0;
      var c = r(0),
        f = r(6),
        l = r(3),
        p = r(1),
        v = "_arms_uid",
        y = "_arms_session",
        d = function(e, t, n) {
          return t && u(e.prototype, t), n && u(e, n), Object.defineProperty(e, "prototype", {
            writable: !1
          }), e
        }((function e() {
          ! function(e, t) {
            if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function")
          }(this, e)
        }), [{
          key: "init",
          value: function(e) {
            var t;
            this.ctx = e, this.sessionConfig = this.fixSessionConfig(null === (t = e.config) || void 0 === t ? void 0 : t.sessionConfig)
          }
        }, {
          key: "getSessionId",
          value: function() {
            return this.getSessionInfo().sessionId
          }
        }, {
          key: "getSampled",
          value: function() {
            return this.getSessionInfo().sampled
          }
        }, {
          key: "checkSession",
          value: function(e) {
            var t = this.sessionConfig,
              n = t.overtime,
              r = t.maxDuration,
              o = (0, f.getCurrentTime)();
            return !(e.startTime + r < o || e.lastTime + n < o)
          }
        }, {
          key: "updateSession",
          value: function() {
            var e = this.getSessionInfo();
            if (!e.isNew) {
              var t = e.startTime,
                n = (0, f.getCurrentTime)(),
                r = e.sampled ? 1 : 0;
              (0, p.setStorageSync)(y, "".concat(e.sessionId, "-").concat(r, "-").concat(t, "-").concat(n))
            }
          }
        }, {
          key: "getSessionInfo",
          value: function() {
            var e = i(((0, p.getStorageSync)(y) || "").split("-"), 4),
              t = e[0],
              n = e[1],
              r = e[2],
              o = e[3],
              a = {
                sessionId: t,
                sampled: "0" !== n,
                startTime: parseInt(r || "") || 0,
                lastTime: parseInt(o || "") || 0
              };
            return this.checkSession(a) || (a = this.resetSession()), a
          }
        }, {
          key: "getEventId",
          value: function() {
            return (0, c.generateEventId)(this.getSessionId())
          }
        }, {
          key: "getViewId",
          value: function() {
            return this.getUUID()
          }
        }, {
          key: "getUserId",
          value: function() {
            var e = (0, p.getStorageSync)(v);
            return e && 0 === e.indexOf("user_") && (e = ""), e || (e = "uid_".concat((0, c.generateSpanId)(16, 36)), (0, p.setStorageSync)(v, e)), e
          }
        }, {
          key: "resetSession",
          value: function() {
            var e = this.sessionConfig.sampleRate,
              t = this.getUUID(),
              n = (0, f.getCurrentTime)(),
              r = (0, c.performDraw)(100 * e),
              o = "".concat(t, "-").concat(r ? 1 : 0, "-").concat(n, "-").concat(n);
            return (0, p.setStorageSync)(y, o), {
              sessionId: t,
              sampled: r,
              startTime: n,
              lastTime: n,
              isNew: !0
            }
          }
        }, {
          key: "getUUID",
          value: function() {
            return (0, c.generateGUID)().replace(/-/g, "")
          }
        }, {
          key: "fixSessionConfig",
          value: function() {
            var e = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {},
              t = e.sampleRate,
              n = e.maxDuration,
              r = e.overtime;
            return (!(0, c.isNumber)(t) || 0 > t || 1 < t) && (t = 1), (!(0, c.isNumber)(n) || n < 4 * c.ONE_HOUR || n > c.ONE_DAY) && (n = c.ONE_DAY), (!(0, c.isNumber)(r) || r > c.ONE_HOUR || r < 10 * c.ONE_MINUTE) && (r = 30 * c.ONE_MINUTE), {
              sampleRate: t,
              maxDuration: n,
              overtime: r
            }
          }
        }, {
          key: "getBaseEvent",
          value: function() {
            return {
              timestamp: (0, f.getCurrentTime)(),
              session_id: this.getSessionId(),
              event_id: this.getEventId(),
              view: this.ctx ? (0, l.getCurView)(this.ctx) : void 0,
              times: 1
            }
          }
        }]);
      t.RumSession = d
    }])
  }, "object" == n(r) && "object" == n(t) ? t.exports = i() : "function" == typeof define && define.amd ? define([], i) : "object" == n(r) ? r.RumSDK = i() : o.RumSDK = i()
}), (function(e) {
  return t({} [e], e)
})), t(1739784025861));