var e = require("../../@babel/runtime/helpers/slicedToArray"),
  t = require("../../A4000F75549B04BFC2666772D6B30D65.js"),
  a = (getApp(), {
    province: [],
    city: [],
    county: [],
    town: []
  });
Component({
  properties: {
    isShowArea: {
      type: Boolean,
      value: !1
    },
    province: {
      type: String,
      value: ""
    },
    city: {
      type: String,
      value: ""
    },
    county: {
      type: String,
      value: ""
    },
    town: {
      type: String,
      value: ""
    }
  },
  data: {
    columns: [{
      values: []
    }, {
      values: []
    }, {
      values: []
    }]
  },
  methods: {
    onChange: function(t) {
      console.log("e.detail.value", t.detail.value);
      var n = t.detail,
        o = n.picker,
        i = n.index,
        u = e(t.detail.value, 4),
        l = u[0],
        s = u[1],
        r = u[2];
      u[3];
      0 == i && (this.queryCity(l.areaId), this.queryCounty(a.city[0].areaId)), 1 == i && this.queryCounty(s.areaId), 2 == i && this.GetTown(r.areaId), o.setColumnValues(1, a.city), o.setColumnValues(2, a.county), o.setColumnValues(3, a.town)
    },
    onCancel: function() {
      this.setData({
        isShowArea: !1
      })
    },
    onConfirm: function(e) {
      console.log("e.detail.value", e.detail.value), this.setData({
        isShowArea: !1
      }), this.triggerEvent("onConfirm", e.detail.value, {})
    },
    queryProvince: function() {
      this.queryCity(a.province[0].areaId)
    },
    queryCity: function(e) {
      var n = this;
      console.log("regionId", e), wx.showLoading({
        mask: !0
      }), (0, t.selectList)({
        level: 2,
        regionId: e
      }).then((function(e) {
        wx.hideLoading(), 200 == e.code && (a.city = e.data, console.log("areaData.county", a.county), n.queryCounty(a.city[0].areaId))
      }))
    },
    queryCounty: function(e) {
      var n = this;
      wx.showLoading({
        mask: !0
      }), (0, t.selectList)({
        level: 3,
        regionId: e
      }).then((function(e) {
        wx.hideLoading(), 200 == e.code && (a.county = e.data, n.setData({
          columns: [{
            values: a.province
          }, {
            values: a.city
          }, {
            values: a.county
          }]
        }))
      }))
    },
    GetTown: function(e) {
      var n = this;
      wx.showLoading({
        mask: !0
      }), (0, t.selectList)({
        level: 4,
        regionId: e
      }).then((function(e) {
        wx.hideLoading(), 200 == e.code && (console.log("获取的乡镇", e), 0 != e.data.length ? (a.town = e.data, n.setData({
          columns: [{
            values: a.province
          }, {
            values: a.city
          }, {
            values: a.county
          }, {
            values: a.town
          }]
        })) : n.setData({
          columns: [{
            values: a.province
          }, {
            values: a.city
          }, {
            values: a.county
          }, {
            values: []
          }]
        }))
      }))
    }
  },
  lifetimes: {
    attached: function() {
      var e = this;
      (0, t.selectList)({
        level: 1,
        regionId: "100000000000"
      }).then((function(t) {
        if (200 == t.code) {
          var n = t.data.filter((function(e) {
            return "台湾省" != e.areaName && "香港" != e.areaName && "澳门" != e.areaName && "苏鲁交界" != e.areaName
          }));
          a.province = n, e.queryProvince()
        }
      }))
    }
  }
});