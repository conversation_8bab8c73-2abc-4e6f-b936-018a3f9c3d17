<view class="container fadeIn" hoverClass="none" hoverStopPropagation="false">
    <custom-header :isBack="{{true}}" title="PEANUTS"></custom-header>
    <view class="scroll-view {{screenInfo}}" hoverClass="none" hoverStopPropagation="false" style="top:{{statusHeaderBarHeight}}px">
        <image binderror="" bindload="" class="bg" lazyLoad="true" src="{{imgUrl}}task/bg.jpg?v={{imgVersion}}"></image>
        <image binderror="" bindload="" class="starBg" lazyLoad="true" src="{{imgUrl}}task/star.png?v={{imgVersion}}"></image>
        <image binderror="" bindload="" class="people" lazyLoad="true" src="{{imgUrl}}task/people.png?v={{imgVersion}}"></image>
        <view class="taskBox1" hoverClass="none" hoverStopPropagation="false">
            <image binderror="" bindload="" class="t1" lazyLoad="true" src="{{imgUrl}}task/t1.png?v={{imgVersion}}" wx:if="{{UserShareInfo.length==0}}"></image>
            <image binderror="" bindload="" class="t1" lazyLoad="true" src="{{imgUrl}}task/t1-hover.png?v={{imgVersion}}" wx:else></image>
            <block wx:if="{{proClickState}}">
                <button catch:tap="handleAddDrawLogs" class="jumpShare" openType="share" wx:if="{{UserShareInfo.length==0}}"></button>
            </block>
        </view>
        <view class="taskBox2" hoverClass="none" hoverStopPropagation="false">
            <image binderror="" bindload="" class="t1" lazyLoad="true" src="{{imgUrl}}task/t2.png?v={{imgVersion}}"></image>
            <image binderror="" bindload="" class="success" lazyLoad="true" src="{{imgUrl}}task/success.png?v={{imgVersion}}" wx:if="{{UserExchangeInfo.length>0}}"></image>
            <image binderror="" bindload="" class="t1" lazyLoad="true" src="{{imgUrl}}task/t2-hover.png?v={{imgVersion}}" wx:elif="{{currentJf<10&&UserExchangeInfo.length<=0}}"></image>
            <view class="currentNumber" hoverClass="none" hoverStopPropagation="false">{{currentJf}}</view>
            <block wx:if="{{proClickState}}">
                <view catch:tap="handleExchange" class="startExchange" hoverClass="none" hoverStopPropagation="false" wx:if="{{currentJf>=10&&UserExchangeInfo.length<=0}}"></view>
            </block>
        </view>
        <image binderror="" bindload="" catch:tap="handleGoTask" class="t3" lazyLoad="true" src="{{imgUrl}}task/t3.png?v={{imgVersion}}"></image>
        <view class="t4Box">
            <view class="drawNumber" hoverClass="none" hoverStopPropagation="false">{{userInfo.drawNub}}</view>
            <image binderror="" bindload="" catch:tap="handlStartDraw" class="t4" lazyLoad="true" src="{{imgUrl}}task/t4.png?v={{imgVersion}}"></image>
            <image binderror="" bindload="" class="t4-lock" lazyLoad="true" src="{{imgUrl}}task/t4-lock.png?v={{imgVersion}}" wx:if="{{userInfo.drawNub<=0}}"></image>
        </view>
        <image binderror="" bindload="" class="clould1" lazyLoad="true" src="{{imgUrl}}task/clould1.png?v={{imgVersion}}"></image>
    </view>
    <copyright></copyright>
</view>
