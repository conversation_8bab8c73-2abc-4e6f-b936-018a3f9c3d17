require("../../../6F218526549B04BF0947ED2133340D65.js");
var t = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  e = getApp();
Page({
  data: {
    img: e.globalData.img,
    state: 3,
    orderInfo: {},
    isGiftType: null,
    id: "",
    stateList: [{
      state: 1,
      name: "待支付"
    }, {
      state: 2,
      name: "已支付积分未付款"
    }, {
      state: 21,
      name: "待审核"
    }, {
      state: 31,
      name: "待发货"
    }, {
      state: 32,
      name: "发放中"
    }, {
      state: 33,
      name: "发放成功"
    }, {
      state: 34,
      name: "发放失败"
    }, {
      state: 35,
      name: "重发处理中"
    }, {
      state: 36,
      name: "已发货"
    }, {
      state: 37,
      name: "已收货"
    }, {
      state: 82,
      name: "已关闭"
    }, {
      state: 83,
      name: "用户手动删除"
    }],
    orderStateInfo: {},
    totalPrice: "",
    orderNumber: ""
  },
  copyUrl: function() {
    wx.setClipboardData({
      data: "https://22233.cn/1689",
      success: function() {
        wx.showToast({
          icon: "none",
          title: "兑换入口已复制"
        })
      }
    })
  },
  hide_phone: function(t, e, a) {
    var o = t.length - e - a;
    console.log("len", o);
    return t.substring(0, e) + "***" + t.substring(t.length - a)
  },
  gotoSh: function() {
    var e = this;
    (0, t.confirmReceipt)({
      id: this.data.orderInfo.id
    }).then((function(t) {
      e.getOrder()
    }))
  },
  gotoCopyOrder: function() {
    wx.setClipboardData({
      data: this.data.orderInfo.orderNo,
      success: function(t) {
        wx.showToast({
          icon: "none",
          title: "单号已复制"
        })
      }
    })
  },
  gotoCopyKd: function() {
    wx.setClipboardData({
      data: this.data.orderInfo.expressCompanyName,
      success: function(t) {
        wx.getClipboardData({
          success: function(t) {
            console.log(t.data)
          }
        })
      }
    })
  },
  onLoad: function(t) {
    console.log(t), this.setData({
      id: t.id
    }), this.getOrder()
  },
  getGiftInfo: function(e) {
    return new Promise((function(a, o) {
      (0, t.getGiftDetails)({
        id: e
      }).then((function(t) {
        a(t)
      }))
    }))
  },
  getOrder: function() {
    var e = this;
    (0, t.getOrderInfo)({
      id: this.data.id
    }).then((function(t) {
      1 == t.data.orderDetail[0].giftType ? (e.setData({
        isGiftType: !0
      }), wx.setNavigationBarTitle({
        title: "虚拟商品订单详情"
      })) : (e.setData({
        isGiftType: !1
      }), wx.setNavigationBarTitle({
        title: "实物商品订单详情"
      })), console.log("res.data", t.data);
      var a = t.data || {};
      (a.orderDetail || []).forEach((function(t) {
        e.getGiftInfo(t.warehouseGiftId).then((function(o) {
          t.detail = o.data, e.setData({
            orderInfo: a
          })
        }))
      })), e.setData({
        orderNumber: e.hide_phone(t.data.orderNo, 9, 8),
        totalPrice: t.data.totalPrice.replace("积分", "能量"),
        orderStateInfo: e.data.stateList.find((function(e) {
          return e.state == t.data.orderStatus
        }))
      }), console.log("orderStateInfo", e.data.orderStateInfo)
    }))
  }
});