.OrderInfo {
    background: #f6f6f6;
    min-height: 100vh
}

.OrderInfo_top,.OrderInfo_top2,.OrderInfo_top3 {
    -webkit-align-items: center;
    align-items: center;
    background: url("https://dm-assets.supercarrier8.com/wobei/newVersion/012.png");
    background-size: 100% 100%;
    display: -webkit-flex;
    display: flex;
    height: 110rpx;
    width: 750rpx
}

.OrderInfo_top_icon {
    height: 37rpx;
    margin-left: 39rpx;
    width: 37rpx
}

.OrderInfo_top_icon2 {
    height: 31rpx;
    margin-left: 38rpx;
    width: 39rpx
}

.OrderInfo_top_icon3 {
    height: 34rpx;
    margin-left: 38rpx;
    width: 38rpx
}

.OrderInfo_top_text {
    color: #343434;
    margin-left: 14rpx
}

.OrderInfo_top_text,.OrderInfo_top_text_1 {
    font-family: Source Han Sans CN;
    font-size: 30rpx;
    font-weight: 700
}

.OrderInfo_top_text_1 {
    color: #000
}

.OrderInfo_top_text_2 {
    color: #000;
    font-family: Source <PERSON>N;
    font-size: 22rpx;
    font-weight: 500
}

.OrderInfo_goodInfo {
    background: #fff;
    padding-bottom: 40rpx;
    position: relative;
    width: 750rpx
}

.OrderInfo_goodInfo_top {
    border-bottom: 1rpx solid #ccc;
    box-sizing: border-box;
    margin: 0 auto;
    padding-top: 30rpx;
    width: 678rpx
}

.OrderInfo_goodInfo_top_item {
    display: -webkit-flex;
    display: flex;
    margin-bottom: 40rpx
}

.OrderInfo_goodInfo_top_img {
    height: 125rpx;
    width: 125rpx
}

.OrderInfo_goodInfo_top_r {
    height: 125rpx;
    margin-left: 22rpx;
    width: 520rpx
}

.OrderInfo_goodInfo_top_r_name {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: #393939;
    display: -webkit-box;
    font-family: Source Han Sans CN;
    font-size: 27rpx;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 520rpx
}

.OrderInfo_goodInfo_top_r_num {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin-top: 6rpx
}

.OrderInfo_goodInfo_top_r_num_price {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 30rpx;
    font-weight: 700
}

.OrderInfo_goodInfo_top_r_num_num {
    color: #7a7a7a;
    font-family: Source Han Sans CN;
    font-size: 20rpx;
    font-weight: 400
}

.OrderInfo_goodInfo_bot {
    box-sizing: border-box;
    margin: 30rpx auto 0;
    padding-left: 11rpx;
    width: 678rpx
}

.OrderInfo_goodInfo_bot_item {
    color: #b2b2b2;
    display: -webkit-flex;
    display: flex;
    font-family: Source Han Sans CN;
    font-size: 26rpx;
    font-weight: 500;
    line-height: 48rpx;
    position: relative
}

.value {
    color: #393939;
    width: 500rpx
}

.value,.values {
    font-size: 28rpx;
    margin-left: 20rpx
}

.values {
    color: #000
}

.OrderInfo_goodInfo_copy {
    -webkit-align-items: center;
    align-items: center;
    border: 1px solid #9daaa9;
    border-radius: 20rpx;
    display: -webkit-flex;
    display: flex;
    height: 40rpx;
    position: absolute;
    right: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 97rpx
}

.OrderInfo_goodInfo_copyImg {
    height: 24rpx;
    margin-left: 16rpx;
    width: 20rpx
}

.OrderInfo_goodInfo_copyText {
    color: #9daaa9;
    font-family: Source Han Sans CN;
    font-size: 20rpx;
    font-weight: 500;
    margin-left: 6rpx
}

.OrderInfo_logistics {
    background: #fff;
    box-sizing: border-box;
    margin-top: 7rpx;
    overflow: hidden;
    padding-bottom: 36rpx;
    position: relative;
    width: 750rpx
}

.OrderInfo_logistics_title {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 30rpx;
    font-weight: 500;
    margin-bottom: 10rpx;
    margin-left: 32rpx;
    margin-top: 22rpx
}

.OrderInfo_logistics_item {
    box-sizing: border-box;
    color: #b2b2b2;
    display: -webkit-flex;
    display: flex;
    font-family: Source Han Sans CN;
    font-size: 26rpx;
    font-weight: 500;
    line-height: 48rpx;
    padding-left: 53rpx;
    position: relative
}

.OrderInfo_logistics_copy {
    -webkit-align-items: center;
    align-items: center;
    border: 1rpx solid #9daaa9;
    border-radius: 20rpx;
    display: -webkit-flex;
    display: flex;
    height: 40rpx;
    position: absolute;
    right: 40rpx;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 97rpx
}

.text {
    text-align: justify;
    text-align-last: justify;
    width: 120rpx
}

.OrderInfo_confirmGoods {
    background: #000;
    color: #fff;
    font-family: Source Han Sans CN;
    font-size: 38rpx;
    font-weight: 700;
    height: 90rpx;
    line-height: 90rpx;
    padding-bottom: env(safe-area-inset-bottom);
    text-align: center;
    width: 750rpx
}

.OrderInfo_bot {
    bottom: 0;
    left: 0;
    position: fixed;
    width: 100%
}

.OrderInfo_confirmGoods_botton {
    border: 2rpx solid #000;
    border-radius: 33rpx;
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 28rpx;
    font-weight: 500;
    height: 68rpx;
    line-height: 68rpx;
    position: absolute;
    right: 23rpx;
    text-align: center;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 179rpx
}
