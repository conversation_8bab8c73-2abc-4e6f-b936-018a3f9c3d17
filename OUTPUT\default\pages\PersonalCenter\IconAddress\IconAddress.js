var t = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  e = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  o = s(require("../../../87624F60549B04BFE10427674BE30D65.js")),
  a = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  n = s(require("../../../B6135D02549B04BFD0753505DD930D65.js"));

function s(t) {
  return t && t.__esModule ? t : {
    default: t
  }
}
var r = getApp(),
  c = "DAkHeza7GMvAuA5FL2p8GAZxC84WoMLh";
Page({
  data: {
    img: r.globalData.img,
    latitude: null,
    longitude: null,
    poiResults: [],
    query: "",
    noListState: !1
  },
  select: function(t) {
    var e = t.currentTarget.dataset,
      o = e.lat,
      a = e.lng;
    this.convertGPS(o, a)
  },
  convertGPS: function(t, e) {
    wx.request({
      url: "".concat(n.default.ApiURL) + "/cncop/area/convert-gps?latitude=".concat(t, "&longitude=").concat(e),
      success: function(t) {
        var e = t.data.data.result;
        console.log(t), console.log("国家", e.addressComponent.country), console.log("省", e.addressComponent.province), console.log("市", e.addressComponent.city), console.log("区", e.addressComponent.district), console.log("乡镇", e.addressComponent.town), console.log("areaid", e.addressComponent.adcode), console.log("地址信息", e.formattedAddress), o.default.data.addressInfo = {
          province: e.addressComponent.province,
          city: e.addressComponent.city,
          district: e.addressComponent.district,
          adcode: e.addressComponent.adcode,
          formattedAddress: e.formattedAddress,
          isChooseAdd: !0
        }, wx.navigateBack(), console.log("fetchData.data.addressInfo", o.default.data.addressInfo)
      }
    })
  },
  search: function() {
    var t = this;
    (0, a.loadingOpen)(), wx.request({
      url: "https://api.map.baidu.com/place/v2/search?query=".concat(this.data.query ? this.data.query : "商场", "&radius=10000&location=").concat(this.data.latitude, ",").concat(this.data.longitude, "&output=json&ak=").concat(c, "&coord_type=gcj02ll"),
      success: function(e) {
        (0, a.loadingClose)(), 0 == e.data.results.length ? t.setData({
          noListState: !0
        }) : t.setData({
          poiResults: e.data.results,
          noListState: !1
        })
      }
    })
  },
  onShow: function() {
    var o = this;
    return e(t().mark((function e() {
      var n, s;
      return t().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return n = wx.createMapContext("mapId"), t.next = 3, (0, a.getUserGPS)(!0, "请授予此应用访问您的位置信息!，否则回到手动填写地址页面");
          case 3:
            s = t.sent, console.log("gpsInfo", s), wx.request({
              url: "https://api.map.baidu.com/place/v2/search?query=商场&radius=10000&location=".concat(s.latitude, ",").concat(s.longitude, "&output=json&ak=").concat(c, "&coord_type=gcj02ll&scope=2"),
              success: function(t) {
                o.setData({
                  poiResults: t.data.results
                })
              }
            }), o.setData({
              latitude: s.latitude,
              longitude: s.longitude
            }), n.addMarkers({
              markers: [{
                latitude: s.latitude,
                longitude: s.longitude
              }],
              clear: !0
            });
          case 8:
          case "end":
            return t.stop()
        }
      }), e)
    })))()
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {}
});