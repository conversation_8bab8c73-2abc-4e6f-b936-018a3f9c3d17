var t, e, n = require("../../../../@babel/runtime/helpers/assertThisInitialized"),
  r = require("../../../../@babel/runtime/helpers/inherits"),
  i = require("../../../../@babel/runtime/helpers/createSuper"),
  o = require("../../../../@babel/runtime/helpers/regeneratorRuntime"),
  s = require("../../../../@babel/runtime/helpers/toConsumableArray"),
  a = require("../../../../@babel/runtime/helpers/classCallCheck"),
  u = require("../../../../@babel/runtime/helpers/createClass"),
  c = require("../../../../@babel/runtime/helpers/createForOfIteratorHelper"),
  l = require("../../../../@babel/runtime/helpers/typeof");
module.exports = (t = {}, e = function(e, n) {
  if (!t[e]) return require(n);
  if (!t[e].status) {
    var r = t[e].m;
    r._exports = r._tempexports;
    var i = Object.getOwnPropertyDescriptor(r, "exports");
    i && i.configurable && Object.defineProperty(r, "exports", {
      set: function(t) {
        "object" === l(t) && t !== r._exports && (r._exports.__proto__ = t.__proto__, Object.keys(t).forEach((function(e) {
          r._exports[e] = t[e]
        }))), r._tempexports = t
      },
      get: function() {
        return r._tempexports
      }
    }), t[e].status = 1, t[e].func(t[e].req, r, r.exports)
  }
  return t[e].m.exports
}, function(e, n, r) {
  t[e] = {
    status: 0,
    func: n,
    req: r,
    m: {
      exports: {},
      _tempexports: {}
    }
  }
}(1739784025840, (function(t, e, f) {
  function p(t) {
    var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
      n = [];
    return Object.keys(e).forEach((function(t) {
      n.push("".concat(t, "=").concat(e[t]))
    })), 0 === n.length ? "/".concat(t) : "/".concat(t).concat(-1 !== t.indexOf("?") ? "&" : "?").concat(n.join("&"))
  }
  Object.defineProperty(f, "__esModule", {
    value: !0
  });
  var h = Object.defineProperty,
    d = Object.getOwnPropertySymbols,
    m = Object.prototype.hasOwnProperty,
    y = Object.prototype.propertyIsEnumerable,
    g = function(t, e, n) {
      return e in t ? h(t, e, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: n
      }) : t[e] = n
    },
    v = function(t) {
      var e, n, r = {
          request: function() {},
          httpRequest: function() {},
          getSystemInfoSync: function() {}
        },
        i = "unknown";
      if ("object" === ("undefined" == typeof wx ? "undefined" : l(wx))) r = wx, i = "wechat";
      else if ("object" === ("undefined" == typeof dd ? "undefined" : l(dd))) r = dd, i = "dingtalk";
      else if ("object" === ("undefined" == typeof my ? "undefined" : l(my))) r = my, i = "alipay";
      else if ("object" === ("undefined" == typeof tt ? "undefined" : l(tt))) r = tt, i = "bytedance";
      else if ("object" === ("undefined" == typeof qq ? "undefined" : l(qq))) r = qq, i = "qq";
      else if ("object" === ("undefined" == typeof swan ? "undefined" : l(swan))) r = swan, i = "swan";
      else {
        if (!t.platformSDK) throw new Error("Current platform is not default supported by SLS API, Pleace config platformSDK or contack Aliyun SLS team.");
        r = t.platformSDK
      }
      return function(t, e) {
        for (var n in e || (e = {})) m.call(e, n) && g(t, n, e[n]);
        if (d) {
          var r, i = c(d(e));
          try {
            for (i.s(); !(r = i.n()).done;) n = r.value, y.call(e, n) && g(t, n, e[n])
          } catch (t) {
            i.e(t)
          } finally {
            i.f()
          }
        }
        return t
      }({
        sdk: r,
        appName: i
      }, (e = r, {
        getStorageSync: function(t) {
          return "function" == typeof e.getStorageSync ? e.getStorageSync(t) : null
        },
        setStorageSync: function(t, n) {
          "function" == typeof e.setStorageSync && e.setStorageSync(t, n)
        },
        getCurrentPagesInterop: n = function() {
          return "function" != typeof getCurrentPages ? [] : getCurrentPages()
        },
        getNavigateBackUrl: function() {
          var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 1,
            e = getCurrentPages();
          if (!e.length) return "";
          var n = e[e.length - t];
          return p(n.route, n.options)
        },
        getCurrentUrl: function() {
          var t = n();
          if (!t.length) return "";
          var e = t.pop();
          return p(e.route, e.options)
        }
      }))
    };

  function b(t, e) {
    var n, r = "unknown";
    if (e && t[e]) n = t[e], r = e;
    else if (t.request) n = t.request, r = "request";
    else {
      if (!t.httpRequest) throw new Error("Current platform is not default supported by SLS API, Pleace config platformRequestName or contack Aliyun SLS team.");
      n = t.httpRequest, r = "httpRequest"
    }
    return {
      request: n,
      requestName: r
    }
  }
  var S = function() {
      function t(e) {
        a(this, t), this.total = 0, this.count = 0, this.peddings = [], this.total = e
      }
      return u(t, [{
        key: "append",
        value: function(t) {
          this.peddings.push(t), this.run()
        }
      }, {
        key: "run",
        value: function() {
          var t = this;
          if (this.peddings.length && !(this.count >= this.total)) {
            var e = this.peddings.shift();
            e && (this.count++, e().finally((function() {
              t.count--, t.run()
            })))
          }
        }
      }]), t
    }(),
    q = function() {
      function t(e) {
        var n, r, i, o = this;
        a(this, t), this.timer = null, this.time = 10, this.count = 10, this.arr = [], this.time = null != (n = e.time) ? n : 10, this.count = null != (r = e.count) ? r : 10, this.ccController = new S(null != (i = e.maxReqCount) ? i : 10), e.host.startsWith("http://") || e.host.startsWith("https://") ? this.url = e.host + "/logstores/" + e.logstore + "/track" : this.url = "https://" + e.project + "." + e.host + "/logstores/" + e.logstore + "/track", this.opt = e, e.installUnloadHook && "function" == typeof e.installUnloadHook && e.installUnloadHook((function() {
          o.sendImmediateInner()
        }))
      }
      return u(t, [{
        key: "assemblePayload",
        value: function(t) {
          var e = {
            __logs__: t
          };
          return this.opt.tags && (e.__tags__ = this.opt.tags), this.opt.topic && (e.__topic__ = this.opt.topic), this.opt.source && (e.__source__ = this.opt.source), JSON.stringify(e)
        }
      }, {
        key: "platformSend",
        value: function() {
          var t = this;
          if (this.opt.sendPayload && "function" == typeof this.opt.sendPayload) {
            var e = this.assemblePayload(this.arr);
            this.ccController.append((function() {
              return t.opt.sendPayload(t.url, e)
            }))
          }
        }
      }, {
        key: "transString",
        value: function(t) {
          var e = {};
          for (var n in t) "object" == l(t[n]) ? e[n] = JSON.stringify(t[n]) : e[n] = String(t[n]);
          return e
        }
      }, {
        key: "sendImmediateInner",
        value: function() {
          this.arr && this.arr.length > 0 && (this.platformSend(), null != this.timer && (clearTimeout(this.timer), this.timer = null), this.arr = [])
        }
      }, {
        key: "sendInner",
        value: function() {
          if (this.timer) this.arr.length >= this.count && (clearTimeout(this.timer), this.timer = null, this.sendImmediateInner());
          else {
            var t = this;
            this.arr.length >= this.count || this.time <= 0 ? this.sendImmediateInner() : this.timer = setTimeout((function() {
              t.sendImmediateInner()
            }), 1e3 * this.time)
          }
        }
      }, {
        key: "send",
        value: function(t) {
          var e = this.transString(t);
          this.arr.push(e), this.sendInner()
        }
      }, {
        key: "sendImmediate",
        value: function(t) {
          var e = this.transString(t);
          this.arr.push(e), this.sendImmediateInner()
        }
      }, {
        key: "sendBatchLogs",
        value: function(t) {
          var e, n = this,
            r = t.map((function(t) {
              return n.transString(t)
            }));
          (e = this.arr).push.apply(e, s(r)), this.sendInner()
        }
      }, {
        key: "sendBatchLogsImmediate",
        value: function(t) {
          var e, n = this,
            r = t.map((function(t) {
              return n.transString(t)
            }));
          (e = this.arr).push.apply(e, s(r)), this.sendImmediateInner()
        }
      }, {
        key: "overwriteTransString",
        value: function(t) {
          this.transString = t.transString
        }
      }, {
        key: "getOpt",
        value: function() {
          return this.opt
        }
      }]), t
    }(),
    k = Object.defineProperty,
    P = Object.getOwnPropertySymbols,
    w = Object.prototype.hasOwnProperty,
    _ = Object.prototype.propertyIsEnumerable,
    O = function(t, e, n) {
      return e in t ? k(t, e, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: n
      }) : t[e] = n
    },
    x = function(t, e, n) {
      return new Promise((function(r, i) {
        var o = function(t) {
            try {
              a(n.next(t))
            } catch (t) {
              i(t)
            }
          },
          s = function(t) {
            try {
              a(n.throw(t))
            } catch (t) {
              i(t)
            }
          },
          a = function(t) {
            return t.done ? r(t.value) : Promise.resolve(t.value).then(o, s)
          };
        a((n = n.apply(t, e)).next())
      }))
    };

  function I(t, e, n, r, i) {
    var o = {
      success: function(t) {
        t && (200 === t.statusCode || i && i(e, t))
      },
      fail: function(t) {
        i && i(e, t)
      }
    };
    (0, b(n, r).request)(function(t, e) {
      for (var n in e || (e = {})) w.call(e, n) && O(t, n, e[n]);
      if (P) {
        var r, i = c(P(e));
        try {
          for (i.s(); !(r = i.n()).done;) n = r.value, _.call(e, n) && O(t, n, e[n])
        } catch (t) {
          i.e(t)
        } finally {
          i.f()
        }
      }
      return t
    }({
      url: "".concat(t, "?APIVersion=0.6.0"),
      method: "POST",
      data: e
    }, o))
  }

  function j(t, e, n, r, i, s, a, u) {
    return x(this, null, o().mark((function c() {
      var l = this;
      return o().wrap((function(c) {
        for (;;) switch (c.prev = c.next) {
          case 0:
            return c.abrupt("return", new Promise((function(c) {
              return x(l, null, o().mark((function l() {
                var f, p, h, d, m, y, g;
                return o().wrap((function(o) {
                  for (;;) switch (o.prev = o.next) {
                    case 0:
                      return t = t.endsWith("/track") ? t.slice(0, -6) : t, o.next = 3, i.process(t, e, s);
                    case 3:
                      f = o.sent, p = f.data, h = f.header, d = b(n, a), m = d.request, r && (y = "alipay" == r || "dingtalk" == r ? {
                        url: t,
                        method: "POST",
                        data: p,
                        headers: h
                      } : {
                        url: t,
                        method: "POST",
                        data: p,
                        header: h
                      }, g = {
                        success: function(e) {
                          var o;
                          if (e)
                            if (200 === e.statusCode);
                            else if (u && u(y, e), 400 === e.statusCode && e.data && "RequestTimeExpired" === e.data.errorCode && !s) try {
                            var c = e.header;
                            if ("AliyunSLS" === c.Server) {
                              var l = Number(null != (o = c["x-log-time"]) ? o : 0);
                              j(t, p, n, r, i, l, a, u)
                            }
                          } catch (t) {}
                        },
                        complete: function() {
                          c()
                        }
                      }, m(Object.assign(y, g)));
                    case 8:
                    case "end":
                      return o.stop()
                  }
                }), l)
              })))
            })));
          case 1:
          case "end":
            return c.stop()
        }
      }), c)
    })))
  }
  var C = function(t) {
    r(s, t);
    var e = i(s);

    function s(t) {
      var r;
      a(this, s);
      var i = v(t).sdk,
        u = Object.assign({}, t, {
          sendPayload: function(e, s) {
            return x(n(r), null, o().mark((function n() {
              return o().wrap((function(n) {
                for (;;) switch (n.prev = n.next) {
                  case 0:
                    I(e, s, i, t.platformRequestName, t.onPutlogsError);
                  case 1:
                  case "end":
                    return n.stop()
                }
              }), n)
            })))
          }
        });
      return r = e.call(this, u)
    }
    return u(s, [{
      key: "useStsPlugin",
      value: function(t) {
        var e = this,
          n = v(this.getOpt()),
          r = n.sdk,
          i = n.appName;
        this.getOpt().sendPayload = function(n, s) {
          return x(e, null, o().mark((function e() {
            var a;
            return o().wrap((function(e) {
              for (;;) switch (e.prev = e.next) {
                case 0:
                  return a = this.getOpt(), e.next = 3, j(n, s, r, i, t, void 0, a.platformRequestName, a.onPutlogsError);
                case 3:
                case "end":
                  return e.stop()
              }
            }), e, this)
          })))
        }, this.overwriteTransString(t)
      }
    }]), s
  }(q);
  f.default = C, f.getPlatformRequestMethod = b, f.getPlatformSDKInterop = v
}), (function(t) {
  return e({} [t], t)
})), e(1739784025840));