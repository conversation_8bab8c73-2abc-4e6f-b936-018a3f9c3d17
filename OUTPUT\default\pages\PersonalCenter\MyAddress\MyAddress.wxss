.MyAddress {
    background: #f6f6f6;
    min-height: 100vh
}

.MyAddress_list {
    width: 100%
}

.MyAddress_noList {
    height: 100vh;
    overflow: hidden;
    width: 100%
}

.MyAddress_noList_icon {
    height: 101rpx;
    margin: 460rpx auto 0;
    width: 109rpx
}

.MyAddress_noList_text {
    color: silver;
    font-family: Source <PERSON> Sans CN;
    font-size: 32rpx;
    font-weight: 500;
    margin-top: 40rpx;
    text-align: center
}

.MyAddress_bot {
    bottom: 0;
    left: 0;
    position: fixed;
    width: 100%
}

.MyAddress_button {
    background: #000;
    color: #fff;
    font-family: Source <PERSON> Sans CN;
    font-size: 38rpx;
    font-weight: 700;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    width: 100%
}

.MyAddress_list_item {
    background: #fff;
    height: 226rpx;
    margin-bottom: 8rpx;
    width: 750rpx
}

.MyAddress_list_item_t {
    border-bottom: 1rpx solid #ddd;
    box-sizing: border-box;
    display: -webkit-flex;
    display: flex;
    height: 167rpx;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin: 0 auto;
    padding-left: 22rpx;
    padding-right: 18rpx;
    padding-top: 30rpx;
    width: 710rpx
}

.MyAddress_list_item_l {
    height: 167rpx;
    width: 610rpx
}

.MyAddress_list_item_l_t {
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between
}

.item_l_t_l {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex
}

.item_l_t_l_name {
    color: #333;
    font-size: 28rpx;
    max-width: 200rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.item_l_t_l_moren,.item_l_t_l_name {
    font-family: Source Han Sans CN;
    font-weight: 400
}

.item_l_t_l_moren {
    border: 1rpx solid #e4000b;
    border-radius: 4rpx;
    color: #e4000b;
    font-size: 18rpx;
    height: 27rpx;
    line-height: 27rpx;
    margin-left: 20rpx;
    text-align: center;
    width: 49rpx
}

.item_l_t_r {
    font-size: 28rpx
}

.MyAddress_list_item_l_b,.item_l_t_r {
    color: #333;
    font-family: Source Han Sans CN;
    font-weight: 400
}

.MyAddress_list_item_l_b {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    font-size: 26rpx;
    overflow: hidden;
    text-overflow: ellipsis
}

.MyAddress_list_item_r {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    width: 34rpx
}

.MyAddress_list_item_r_choose {
    height: 34rpx;
    width: 34rpx
}

.MyAddress_list_item_b {
    height: 62rpx;
    width: 100%
}

.MyAddress_list_item_b_cen {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    float: right;
    height: 62rpx;
    width: 240rpx
}

.tem_b_cen_editIcon {
    height: 21rpx;
    width: 21rpx
}

.tem_b_cen_editText {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 24rpx;
    font-weight: 400;
    margin-left: 10rpx
}

.tem_b_cen_fg {
    background: #ebebeb;
    height: 25rpx;
    margin: 0 23rpx;
    width: 1rpx
}

.tem_b_cen_delIcon {
    height: 23rpx;
    width: 23rpx
}

.tem_b_cen_delText {
    color: #999;
    font-family: Source Han Sans CN;
    font-size: 24rpx;
    font-weight: 400;
    margin-left: 10rpx
}
