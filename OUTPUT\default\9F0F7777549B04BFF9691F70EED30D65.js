Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.getMonthEndDate = function() {
  return s(new Date(r, n, (e = n, t = new Date(r, e, 1), (new Date(r, e + 1, 1) - t) / 864e5)));
  var e, t
}, exports.getMonthStartDate = function() {
  return s(new Date(r, n, 1))
}, exports.getNowDate = void 0, exports.getThirtyDay = function() {
  var e = new Date,
    t = new Date(e.getTime() - 2592e6),
    a = t.getFullYear(),
    n = t.getMonth() + 1,
    r = t.getDate();
  n < 10 && (n = "0" + n);
  r < 10 && (r = "0" + r);
  return a + "-" + n + "-" + r
}, exports.getWeekEndDate = function() {
  return s(new Date(r, n, a + (6 - t)))
}, exports.getWeekStartDate = void 0, exports.getYearFirstDay = function() {
  var e = new Date;
  return e.setDate(1), e.setMonth(0), e = s(e)
}, exports.getYearLastDay = function() {
  var e = new Date;
  return console.log("~~~~~~~~lastDay", e), e.setFullYear(e.getFullYear() + 1), console.log("setFullYear    lastDay", e), e.setDate(0), console.log("setDate   lastDay", e), e.setMonth(-1), console.log("setMonth   lastDay", e), e = s(e), console.log("lastDaylastDay"), e
}, exports.transformTime = void 0;
var e = new Date,
  t = e.getDay(),
  a = e.getDate(),
  n = e.getMonth(),
  r = (Number(n), Number(1), e.getYear());
r += r < 2e3 ? 1900 : 0;
(new Date).getTime();
var o = new Date;
o.setDate(1), o.setMonth(o.getMonth() - 1);
o.getYear(), o.getMonth();
exports.getNowDate = function() {
  var e = new Date,
    t = e.getFullYear(),
    a = e.getMonth() + 1,
    n = e.getDate(),
    r = e.getHours(),
    o = e.getMinutes(),
    s = e.getSeconds();
  e.getDay();
  return a >= 1 && a <= 9 && (a = "0" + a), n >= 0 && n <= 9 && (n = "0" + n), r >= 0 && r <= 9 && (r = "0" + r), o >= 0 && o <= 9 && (o = "0" + o), s >= 0 && s <= 9 && (s = "0" + s), t + "-" + a + "-" + n + " " + r + ":" + o + ":" + s
};
exports.transformTime = function(e) {
  var t = new Date(e),
    a = t.getFullYear(),
    n = t.getMonth() + 1;
  return n >= 1 && n <= 9 && (n = "0" + n), console.log(a + "年" + n + "月"), a + "年" + n + "月"
};

function s(e) {
  var t = e.getFullYear(),
    a = e.getMonth() + 1,
    n = e.getDate();
  return a < 10 && (a = "0" + a), n < 10 && (n = "0" + n), t + "-" + a + "-" + n
}
exports.getWeekStartDate = function() {
  return s(new Date(r, n, a - t))
};