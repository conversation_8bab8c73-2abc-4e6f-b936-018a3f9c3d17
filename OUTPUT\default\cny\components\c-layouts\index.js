var e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  n = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  r = require("../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  t = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  o = require("../../../71D07D80549B04BF17B615870C540D65.js");
Component({
  properties: {
    bgColor: {
      type: String,
      value: ""
    },
    isNeedStatusBar: {
      type: Boolean,
      value: !0
    },
    isNeedScroll: {
      type: Boolean,
      value: !1
    }
  },
  data: {
    statusHeaderBarHeight: t.statusHeaderBarHeight,
    imgUrl: t.imgUrl,
    imgVersion: t.imgVersion,
    screenInfo: (0, r.getScreenInfo)()
  },
  pageLifetimes: {
    show: function() {
      this.checkLoginStatus()
    }
  },
  methods: {
    checkLoginStatus: function() {
      var t = this;
      return n(e().mark((function n() {
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              if (!(0, r.isTokenExpired)()) {
                e.next = 3;
                break
              }
              return e.next = 3, t.initLogin();
            case 3:
              if (!wx.getStorageSync("access_token")) {
                e.next = 8;
                break
              }
              t.triggerEvent("onLogin", {
                loginStatus: !0
              }), e.next = 10;
              break;
            case 8:
              return e.next = 10, t.handleNoAccessToken();
            case 10:
            case "end":
              return e.stop()
          }
        }), n)
      })))()
    },
    initLogin: function() {
      return n(e().mark((function n() {
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return e.prev = 0, e.next = 3, (0, o.getLogin)();
            case 3:
              e.sent, e.next = 9;
              break;
            case 6:
              e.prev = 6, e.t0 = e.catch(0), wx.showToast({
                title: errorInfo.tokenError,
                icon: "none"
              });
            case 9:
            case "end":
              return e.stop()
          }
        }), n, null, [
          [0, 6]
        ])
      })))()
    },
    handleNoAccessToken: function() {
      var r = this;
      return n(e().mark((function n() {
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return console.log("没有 access_token，用户未登录，请登录。"), e.next = 3, r.initLogin();
            case 3:
            case "end":
              return e.stop()
          }
        }), n)
      })))()
    },
    handleLogin: function() {
      return n(e().mark((function n() {
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
            case "end":
              return e.stop()
          }
        }), n)
      })))()
    }
  }
});