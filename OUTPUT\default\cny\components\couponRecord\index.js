var e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  n = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  o = require("../../../71D07D80549B04BF17B615870C540D65.js"),
  a = require("../../../A5622344549B04BFC3044B435F450D65.js");
Component({
  properties: {},
  data: {
    imgUrl: n.imgUrl,
    imgVersion: n.imgVersion,
    open: !1,
    couponList: []
  },
  methods: {
    openMask: function() {
      this.setData({
        open: !0
      }), this.getCouponListFn(), a.pagePopupViews({
        page_type: "我的红包",
        page_name: "我的红包",
        popup_name: "您的优惠券",
        page_path: "cny/pages/redPacket/index"
      })
    },
    closeMask: function() {
      this.setData({
        open: !1
      })
    },
    handleClose: function() {
      this.closeMask()
    },
    getCouponListFn: function() {
      var n = this;
      return t(e().mark((function t() {
        var a, i, r, s;
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return e.prev = 0, e.next = 3, (0, o.getCouponList)();
            case 3:
              a = e.sent, i = a.code, r = a.data, 200 === i && (console.log("data: ", r), n.setData({
                couponList: null !== (s = r.CouponList) && void 0 !== s ? s : []
              })), e.next = 12;
              break;
            case 9:
              e.prev = 9, e.t0 = e.catch(0), console.log("getCouponList error: ", e.t0);
            case 12:
            case "end":
              return e.stop()
          }
        }), t, null, [
          [0, 9]
        ])
      })))()
    },
    handleStartUse: function(e) {
      var t = e.currentTarget.dataset.info,
        o = t.couponUrl,
        i = t.couponType,
        r = t.appid,
        s = t.infoTitleTwo;
      "外部小程序" === i ? wx.navigateToMiniProgram({
        appId: r,
        path: o,
        success: function(e) {}
      }) : wx.setClipboardData({
        data: o,
        success: function(e) {
          wx.showToast({
            icon: "none",
            title: n.tipInfos.copyTitle,
            duration: 5e3
          })
        }
      }), a.pagePopupClickEvents({
        page_type: "我的红包",
        page_name: "我的红包",
        popup_name: "您的优惠券",
        page_path: "cny/pages/redPacket/index",
        button_name: s + "-立即使用"
      })
    }
  }
});