var e, t = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  a = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  s = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  d = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  o = (e = require("../../../87624F60549B04BFE10427674BE30D65.js")) && e.__esModule ? e : {
    default: e
  },
  r = require("../../../83F188C3549B04BFE597E0C403C30D65.js");
var n = getApp();
Page({
  data: {
    img: n.globalData.img,
    goodsNumber: 1,
    propState: !1,
    propNum: 4,
    giftType: null,
    goodsInfoMsg: {},
    addressList: [],
    defaultAddress: {},
    energyNumber: 0
  },
  onChange: function(e) {
    this.setData({
      goodsNumber: e.detail
    })
  },
  gotoAddressList: function() {
    this.data.defaultAddress ? wx.navigateTo({
      url: "/pages/PersonalCenter/MyAddress/MyAddress?defaultAddress=" + JSON.stringify(this.data.defaultAddress)
    }) : wx.navigateTo({
      url: "/pages/PersonalCenter/MyAddress/MyAddress?defaultAddress=1"
    })
  },
  closeProp: function() {
    this.setData({
      propState: !1
    })
  },
  confirmProp: function() {
    var e = this;
    if (this.setData({
        propState: !1
      }), 4 != this.data.propNum);
    else if (3 == this.data.giftType) {
      var d = this.data.defaultAddress,
        n = d.name,
        i = d.mobile,
        u = d.province,
        f = d.city,
        l = d.area,
        c = d.address;
      (0, s.payJf)({
        gifts: [{
          giftId: this.data.goodsInfoMsg.id,
          giftCount: this.data.goodsNumber
        }],
        consigneeName: n,
        consigneeMobile: i,
        consigneeAddress: "".concat(u).concat(f).concat(l).concat(c),
        rechargeAccount: o.default.data.userInfo.mobile
      }).then(function() {
        var s = a(t().mark((function a(s) {
          return t().wrap((function(t) {
            for (;;) switch (t.prev = t.next) {
              case 0:
                return e.setData({
                  propState: !0,
                  propNum: 5
                }), t.next = 3, (0, r.getUser)();
              case 3:
              case "end":
                return t.stop()
            }
          }), a)
        })));
        return function(e) {
          return s.apply(this, arguments)
        }
      }())
    } else(0, s.payJf)({
      gifts: [{
        giftId: this.data.goodsInfoMsg.id,
        giftCount: this.data.goodsNumber
      }],
      rechargeAccount: o.default.data.userInfo.mobile
    }).then(function() {
      var s = a(t().mark((function a(s) {
        return t().wrap((function(t) {
          for (;;) switch (t.prev = t.next) {
            case 0:
              return e.setData({
                propState: !0,
                propNum: 5
              }), t.next = 3, (0, r.getUser)();
            case 3:
            case "end":
              return t.stop()
          }
        }), a)
      })));
      return function(e) {
        return s.apply(this, arguments)
      }
    }())
  },
  gotoPay: function() {
    3 != this.data.giftType || this.data.defaultAddress ? this.setData({
      propState: !0,
      propNum: 4,
      energyNumber: this.data.goodsInfoMsg.giftExchangeScore * this.data.goodsNumber
    }) : (0, d.toastModel)("请填写收货地址")
  },
  getDetail: function(e) {
    return new Promise((function(t, a) {
      (0, s.getGiftDetail)({
        id: e
      }).then((function(e) {
        t(e), console.log("onload加载完成")
      }))
    }))
  },
  onLoad: function(e) {},
  getUserAddress: function() {
    var e = this;
    (0, d.loadingOpen)(), (0, s.getUserAddressList)().then((function(t) {
      (0, d.loadingClose)(), 0 == t.data.length ? (e.setData({
        defaultAddress: {},
        addressList: []
      }), o.default.data.orderChooseAddress = {}) : (e.setData({
        addressList: t.data || []
      }), console.log("res.data.some((item)=>item.defaultFlag)"), o.default.data.orderChooseAddress.id ? e.setData({
        defaultAddress: o.default.data.orderChooseAddress
      }) : t.data.some((function(e) {
        return 1 == e.defaultFlag
      })) ? e.setData({
        defaultAddress: t.data.find((function(e) {
          return 1 == e.defaultFlag
        }))
      }) : e.setData({
        defaultAddress: t.data[0]
      })), o.default.data.orderChooseAddress = e.data.defaultAddress, console.log("数据", e.data.defaultAddress)
    }))
  },
  onReady: function() {},
  onShow: function() {
    var e = this;
    return a(t().mark((function a() {
      var s;
      return t().wrap((function(t) {
        for (;;) switch (t.prev = t.next) {
          case 0:
            return t.next = 2, e.getDetail(o.default.data.goodsInfoId);
          case 2:
            s = t.sent, e.setData({
              goodsInfoMsg: s.data,
              giftType: s.data.giftType
            }), 1 == e.data.giftType && wx.setNavigationBarTitle({
              title: "虚拟商品提交订单"
            }), 3 == e.data.giftType && wx.setNavigationBarTitle({
              title: "实物商品提交订单"
            }), console.log("orderChooseAddressState", o.default.data.orderChooseAddressState), console.log("fetchData.data.orderChooseAddress", o.default.data.orderChooseAddress), o.default.data.orderChooseAddressState ? (console.log("设置页面收货地址"), e.setData({
              defaultAddress: o.default.data.orderChooseAddress
            }), o.default.data.orderChooseAddressState = !1) : e.getUserAddress();
          case 9:
          case "end":
            return t.stop()
        }
      }), a)
    })))()
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {}
});