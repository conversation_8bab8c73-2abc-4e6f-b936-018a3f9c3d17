<block wx:if="{{isOnline}}">
    <image binderror="" bindload="" catch:tap="handleOpenMask" class="maskIcon" lazyLoad="false" src="{{imgUrl}}mask.png?v={{imgVersion}}" wx:if="{{!open}}"></image>
    <view class="maskContainer" wx:else>
        <view class="maskBox">
            <image binderror="" bindload="" catch:tap="handleOpenKv" class="maskKv" lazyLoad="false" src="{{imgUrl}}kv.png?v={{imgVersion}}"></image>
            <view class="maskBtn"></view>
            <image binderror="" bindload="" catch:tap="handleMaskClose" class="maskClose" lazyLoad="false" src="{{imgUrl}}common/close.png?v={{imgVersion}}"></image>
        </view>
    </view>
</block>
