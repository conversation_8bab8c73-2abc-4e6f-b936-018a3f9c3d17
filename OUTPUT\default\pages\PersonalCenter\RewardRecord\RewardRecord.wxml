<view class="RR">
    <block wx:if="{{list.length!=0}}">
        <view class="RR_item" wx:for="{{list}}" wx:key="index">
            <view class="RR_item_l">
                <view class="RR_item_l_img">
                    <image mode="" src="{{item.giftImg}}"></image>
                </view>
            </view>
            <view class="RR_item_r">
                <view class="RR_item_r_name">{{item.giftNameNew}}</view>
                <view class="RR_item_r_time">{{item.scanDate||item.rowCreateDate}}</view>
            </view>
            <view class="RR_item_button" style="background:#000000" wx:if="{{item.winStatus==4}}">待审核</view>
            <block wx:else>
                <view class="RR_item_buttonNew" wx:if="{{item.giftType==4}}">已领取</view>
                <view bindtap="lookImg" class="RR_item_button" data-item="{{item}}" wx:if="{{item.giftType==1&&item.giftSendType==6}}">查看</view>
                <block wx:elif="{{item.giftType==1&&item.giftSendType==7}}">
                    <view bindtap="receiveGoodsBQ" class="RR_item_button" data-item="{{item}}" style="background:#000000" wx:if="{{item.winStatus<=3}}">领取</view>
                    <view class="RR_item_buttonNew" wx:else>已领取</view>
                </block>
                <block wx:elif="{{item.giftType==1&&item.giftSendType==7||item.giftType==3}}">
                    <view bindtap="receiveGoods" class="RR_item_button" data-item="{{item}}" style="background:#000000" wx:if="{{item.winStatus<=3}}">领取</view>
                    <view class="RR_item_buttonNew" wx:else>已领取</view>
                </block>
                <view bindtap="lookVideo" class="RR_item_button" data-item="{{item}}" style="background:#000000" wx:elif="{{item.giftType==1&&item.giftSendType==4}}">查看</view>
                <view bindtap="lookQuan" class="RR_item_button" data-item="{{item}}" style="background:#000000" wx:elif="{{item.giftType==1&&item.giftSendType==5}}">查看</view>
            </block>
        </view>
    </block>
    <view class="noListData" wx:else>暂无数据</view>
    <prop activityId="{{activityId}}" bindcloseProp="closeProp" bindsubmitUserInfo="submitUserInfo" giftDetailInfo="{{giftDetailInfo}}" priceData="{{priceData}}" priceInfo="{{priceInfo}}" propNum="{{propNum}}" wx:if="{{propState}}"></prop>
</view>
