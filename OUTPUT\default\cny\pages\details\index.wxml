<view class="container fadeIn" hoverClass="none" hoverStopPropagation="false">
    <custom-header :isBack="{{true}}" title="PEANUTS"></custom-header>
    <view class="scroll-view {{screenInfo}}" hoverClass="none" hoverStopPropagation="false" style="top:{{statusHeaderBarHeight}}px">
        <image binderror="" bindload="" class="bg" lazyLoad="true" src="{{imgUrl}}75/details/bg.jpg?v={{imgVersion}}"></image>
        <view class="headerBox">
            <image binderror="" bindload="" class="headerBg" lazyLoad="true" src="{{imgUrl}}75/details/h_bg.png?v={{imgVersion}}"></image>
            <view class="avatarBox">
                <image binderror="" bindload="" class="avatarBg" lazyLoad="true" src="{{imgUrl}}75/details/header_bg.png?v={{imgVersion}}"></image>
                <image binderror="" bindload="" class="avatar" lazyLoad="true" src="{{detailInfo.avatar?detailInfo.avatar:'https://dm-assets.supercarrier8.com/wobei/banner1s.png'}}"></image>
            </view>
            <view class="usernameBox SourceHanSerifCN-Bold" wx:if="{{detailInfo.nickname}}">{{detailInfo.nickname}}</view>
        </view>
        <image binderror="" bindload="" class="upImg" lazyLoad="true" mode="aspectFill" src="{{detailInfo.photo}}"></image>
        <view class="contentBox">
            <image binderror="" bindload="" class="content_bg" lazyLoad="true" src="{{imgUrl}}75/details/content_bg.png?v={{imgVersion}}"></image>
            <view class="titleBox SourceHanSerifCN-Bold">{{detailInfo.title}}</view>
            <scroll-view scrollY class="content SourceHanSerifCN-Regular">{{detailInfo.context}}</scroll-view>
            <view class="operationBox">
                <view class="del_box">
                    <image binderror="" bindload="" catch:tap="handleDel" class="del_icon" lazyLoad="true" src="{{imgUrl}}75/details/del_icon.png?v={{imgVersion}}" wx:if="{{isDel==='record'}}"></image>
                </view>
                <view class="oBox">
                    <image binderror="" bindload="" class="operation_bg" lazyLoad="true" src="{{imgUrl}}75/details/operation_bg.png?v={{imgVersion}}"></image>
                    <view class="shareBox">
                        <button catch:tap="handleShare" class="shareBtn" openType="share"></button>
                        <image binderror="" bindload="" class="share" lazyLoad="true" src="{{imgUrl}}75/details/share.png?v={{imgVersion}}"></image>
                    </view>
                    <view class="likeBox">
                        <view class="likeNumber SourceHanSansCN-Normal">{{detailInfo.likeNub}}</view>
                        <image binderror="" bindload="" catch:tap="handleLikeStory" class="{{detailInfo.likeFlag==='1'?'love_select':'love'}}" lazyLoad="true" src="{{imgUrl}}75/details/{{detailInfo.likeFlag==='1'?'love_select':'love'}}.png?v={{imgVersion}}"></image>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <c-75-del bind:confirm="handleConfirm" id="c-75-del"></c-75-del>
</view>
