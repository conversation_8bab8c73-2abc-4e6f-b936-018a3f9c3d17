Object.defineProperty(exports, "__esModule", {
  value: !0
});
var t = require("../common/component"),
  e = require("../common/utils");
(0, t.VantComponent)({
  classes: ["title-class"],
  props: {
    title: String,
    fixed: {
      type: <PERSON><PERSON><PERSON>,
      observer: "setHeight"
    },
    placeholder: {
      type: <PERSON><PERSON><PERSON>,
      observer: "setHeight"
    },
    leftText: String,
    rightText: String,
    customStyle: String,
    leftArrow: <PERSON>olean,
    border: {
      type: Boolean,
      value: !0
    },
    zIndex: {
      type: Number,
      value: 1
    },
    safeAreaInsetTop: {
      type: <PERSON><PERSON><PERSON>,
      value: !0
    }
  },
  data: {
    height: 46
  },
  created: function() {
    var t = (0, e.getSystemInfoSync)().statusBarHeight;
    this.setData({
      statusBarHeight: t,
      height: 46 + t
    })
  },
  mounted: function() {
    this.setHeight()
  },
  methods: {
    onClickLeft: function() {
      this.$emit("click-left")
    },
    onClickRight: function() {
      this.$emit("click-right")
    },
    setHeight: function() {
      var t = this;
      this.data.fixed && this.data.placeholder && wx.nextTick((function() {
        (0, e.getRect)(t, ".van-nav-bar").then((function(e) {
          e && "height" in e && t.setData({
            height: e.height
          })
        }))
      }))
    }
  }
});