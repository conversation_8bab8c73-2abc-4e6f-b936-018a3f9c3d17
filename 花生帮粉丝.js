/**
 * 微信小程序
 * 作者：Tianxx
 * 版本：1.0
 * 日期：2025-07-21
 */
const NOTICE_SWITCH = 1; // 通知开关：1=开启，0=关闭
// 常量配置
const APPID = 'wx841a8e9e6972a9a6'; // 花生帮粉丝俱乐部小程序appid

// 解析命令行参数
const args = process.argv.slice(2);
const getArg = (name) => {
    const index = args.indexOf(`--${name}`);
    return index !== -1 && args[index + 1] ? args[index + 1] : null;
};

// 环境变量和命令行参数
const cmdWxid = getArg('wxid');
const isDebug = args.includes('--debug');
const wxidList = cmdWxid || process.env.TXX_WXID || '';

// 解析wxid列表的函数
function parseWxidList(wxidString) {
    if (!wxidString) return [];

    return wxidString
        .split('\n')                    
        .map(wxid => wxid.trim())       
        .filter(wxid => wxid.length > 0) 
        .filter(wxid => !wxid.startsWith('#')); 
}

// 引入wxcode模块
const wxcode = require('./wxcode');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 获取脚本名称（不含扩展名）
const scriptName = path.basename(__filename, '.js');
// Token缓存文件路径
const TOKEN_CACHE_FILE = path.join(__dirname, `${scriptName}_tokens.json`);

// 签名生成工具类
class SignUtils {
    // Base64编码
    static base64(data) {
        if (!data) return '';
        const str = typeof data === 'string' ? data : JSON.stringify(data);
        return Buffer.from(str, 'utf8').toString('base64');
    }

    // URL参数转对象
    static urlParamsToObj(url) {
        const params = {};
        try {
            if (url && url.indexOf('?') > 0) {
                const queryString = url.split('?')[1];
                const pairs = queryString.split('&');
                pairs.forEach(pair => {
                    const [key, value] = pair.split('=');
                    if (key) {
                        params[key] = value || '';
                    }
                });
            }
        } catch (e) {
            // 忽略解析错误
        }
        return params;
    }

    // 生成签名
    static getSign(method, url, params, timestamp) {
        // 处理URL，提取路径和查询参数
        let processedUrl = url;
        let allParams = params || {};

        if (url) {
            // 确保URL以/开头
            processedUrl = '/' + url.replace(/^\/+/, '');

            // 如果URL包含查询参数，提取并合并到params中
            if (processedUrl.indexOf('?') > 0) {
                const urlParams = this.urlParamsToObj(processedUrl);
                processedUrl = processedUrl.split('?')[0];
                allParams = Object.assign(urlParams, allParams);
            }
        }

        // 处理参数
        let paramStr = '';
        if (allParams && Object.keys(allParams).length > 0) {
            // 获取所有key并排序
            const keys = Object.keys(allParams).sort();

            // 拼接参数值
            keys.forEach(key => {
                const value = allParams[key];
                // 忽略空值
                if (value !== '' && value !== null && value !== undefined) {
                    let valueStr = value;
                    // 如果是对象，转为JSON字符串
                    if (typeof value !== 'string') {
                        if (typeof value === 'object' && value !== null) {
                            // 移除null值
                            Object.keys(value).forEach(k => {
                                if (value[k] === null) {
                                    delete value[k];
                                }
                            });
                            if (Object.keys(value).length > 0) {
                                valueStr = JSON.stringify(value);
                            } else {
                                return; // 跳过空对象
                            }
                        } else {
                            valueStr = String(value);
                        }
                    }
                    paramStr += valueStr;
                }
            });
        }

        // Base64编码参数字符串
        const encodedParams = this.base64(paramStr);

        // 拼接签名字符串：METHOD + URL + Base64(params) + timestamp
        const signStr = `${method.toUpperCase()}${processedUrl}${encodedParams}${timestamp}`;

        // Base64编码整个签名字符串
        const encodedSignStr = this.base64(signStr);

        // 计算MD5
        const sign = crypto.createHash('md5').update(encodedSignStr).digest('hex');

        if (isDebug) {
            console.log(`[DEBUG] 签名生成详情:`);
            console.log(`[DEBUG] Method: ${method.toUpperCase()}`);
            console.log(`[DEBUG] URL: ${processedUrl}`);
            console.log(`[DEBUG] Params: ${JSON.stringify(allParams)}`);
            console.log(`[DEBUG] ParamStr: ${paramStr}`);
            console.log(`[DEBUG] EncodedParams: ${encodedParams}`);
            console.log(`[DEBUG] SignStr: ${signStr}`);
            console.log(`[DEBUG] EncodedSignStr: ${encodedSignStr}`);
            console.log(`[DEBUG] Final Sign: ${sign}`);
        }

        return sign;
    }

    // 为请求生成签名
    static getSignByReq(requestConfig) {
        const timestamp = Date.now().toString();
        const method = requestConfig.method.toUpperCase();
        const url = requestConfig.url;
        const params = method === 'GET' ? requestConfig.params : requestConfig.data;

        const sign = this.getSign(method, url, params, timestamp);

        // 设置请求头
        if (!requestConfig.headers) {
            requestConfig.headers = {};
        }
        requestConfig.headers['x-request-ts'] = timestamp;
        requestConfig.headers['x-request-sign'] = sign;

        return requestConfig;
    }
}

class ScriptTemplate {
    constructor(wxid) {
        this.wxid = wxid;
        this.appid = APPID;
        this.isLogin = false;
        this.wxCode = null;
        this.openid = null;
        this.mobileInfo = null;
        this.userProfile = null;
        this.cacheExpireTime = null;

        // 花生帮相关配置
        this.enterpriseNo = '131932658387';
        this.authorizerAppid = 'wx841a8e9e6972a9a6';
        this.baseUrl = 'https://restapi.supercarrier8.com';
        this.sessionKey = null;
        this.serviceSign = null;
        this.token = null;
        this.userId = null;
    }

    // HTTP请求工具方法
    async httpRequest(url, method = 'GET', data = null, needSign = true, headers = {}) {
        return new Promise((resolve, reject) => {
            const request = require('request');

            // 构建请求配置
            const requestConfig = {
                url: url,
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Request-Lang': 'zh-CN',
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003d34) NetType/WIFI Language/zh_CN',
                    'Referer': 'https://servicewechat.com/wx841a8e9e6972a9a6/92/page-frame.html',
                    ...headers
                }
            };

            // 添加认证token
            if (this.token) {
                requestConfig.headers['authorization'] = this.token;
            }

            // 处理请求数据
            if (method.toUpperCase() === 'GET') {
                if (data) {
                    const params = new URLSearchParams(data).toString();
                    requestConfig.url += (url.includes('?') ? '&' : '?') + params;
                }
            } else {
                if (data) {
                    requestConfig.body = typeof data === 'string' ? data : JSON.stringify(data);
                    requestConfig.headers['Content-Length'] = Buffer.byteLength(requestConfig.body);
                }
            }

            // 生成签名
            if (needSign) {
                const urlPath = url.replace(this.baseUrl, '');
                const signConfig = {
                    method: method,
                    url: urlPath,
                    data: data,
                    headers: requestConfig.headers
                };
                SignUtils.getSignByReq(signConfig);
                Object.assign(requestConfig.headers, signConfig.headers);
            }

            if (isDebug) {
                console.log(`[DEBUG] HTTP请求配置:`, {
                    url: requestConfig.url,
                    method: requestConfig.method,
                    headers: requestConfig.headers,
                    body: requestConfig.body
                });
            }

            request(requestConfig, (error, _response, body) => {
                if (error) {
                    reject(error);
                    return;
                }

                try {
                    const result = JSON.parse(body);
                    if (isDebug) {
                        console.log(`[DEBUG] HTTP响应:`, result);
                    }
                    resolve(result);
                } catch (e) {
                    if (isDebug) {
                        console.log(`[DEBUG] 响应解析失败:`, body);
                    }
                    resolve(body);
                }
            });
        });
    }

    // 读取token缓存
    loadTokenCache() {
        try {
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                const cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                const userCache = cacheData[this.wxid];

                if (userCache && userCache.cacheExpireTime > Date.now()) {
                    // 只缓存token相关数据
                    this.token = userCache.token;
                    this.userId = userCache.userId;
                    this.cacheExpireTime = userCache.cacheExpireTime;
                    this.isLogin = true;

                    if (isDebug) {
                        console.log(`[DEBUG] 从缓存加载token数据成功`);
                        console.log(`[DEBUG] Token: ${this.token ? '已加载' : '未找到'}`);
                        console.log(`[DEBUG] UserID: ${this.userId}`);
                        console.log(`[DEBUG] 缓存过期时间: ${new Date(this.cacheExpireTime).toLocaleString()}`);
                    }
                    return true;
                } else if (userCache) {
                    if (isDebug) console.log(`[DEBUG] 缓存数据已过期`);
                }
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 读取缓存失败: ${error.message}`);
        }
        return false;
    }

    // 保存数据到缓存
    saveTokenCache() {
        try {
            let cacheData = {};

            // 读取现有缓存
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                try {
                    cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                } catch (e) {
                    if (isDebug) console.log(`[DEBUG] 现有缓存文件格式错误，将重新创建`);
                }
            }

            // 设置缓存过期时间（默认2小时）
            const expireTime = Date.now() + (2 * 60 * 60 * 1000);

            // 更新当前用户的缓存信息（只缓存token相关数据）
            cacheData[this.wxid] = {
                token: this.token,
                userId: this.userId,
                cacheExpireTime: expireTime,
                updateTime: Date.now()
            };

            this.cacheExpireTime = expireTime;

            // 写入文件
            fs.writeFileSync(TOKEN_CACHE_FILE, JSON.stringify(cacheData, null, 2), 'utf8');

            if (isDebug) {
                console.log(`[DEBUG] 缓存保存成功`);
                console.log(`[DEBUG] 缓存文件: ${TOKEN_CACHE_FILE}`);
                console.log(`[DEBUG] 过期时间: ${new Date(expireTime).toLocaleString()}`);
            }
        } catch (error) {
            console.log(`❌ 保存缓存失败: ${error.message}`);
        }
    }

    // 获取微信授权码并登录
    async getWxCodeAndLogin() {
        if (isDebug) console.log(`[DEBUG] 开始获取微信授权码...`);

        const codeResult = await wxcode.getWxCode(this.wxid, this.appid);
        if (!codeResult.success) {
            console.log(`获取授权码失败：${codeResult.error}`);
            return false;
        }

        this.wxCode = codeResult.code;
        if (isDebug) console.log(`[DEBUG] 获取授权码成功：${this.wxCode}`);

        this.isLogin = true;
        return true;
    }

    // 获取用户openid
    async getUserOpenid() {
        const result = await wxcode.getOpenid(this.wxid, this.appid);
        if (result.success) {
            this.openid = result.openid;
            if (isDebug) console.log(`[DEBUG] 获取openid成功：${this.openid}`);
            return this.openid;
        } else {
            console.log(`获取openid失败：${result.error}`);
            return null;
        }
    }

    // 获取手机号
    async getMobileInfo() {
        const result = await wxcode.getmobile(this.wxid, this.appid);
        if (result.success) {
            this.mobileInfo = result;
            if (isDebug) console.log(`[DEBUG] 获取手机号加密数据成功`);
            return this.mobileInfo;
        } else {
            console.log(`获取手机号失败：${result.error}`);
            return null;
        }
    }

    // 获取用户个人信息（云函数调用）
    async getUserProfile() {
        const cloudFunctionData = JSON.stringify({
            "api_name": "webapi_getuserprofile",
            "data": {
                "app_version": 68,
                "desc": "用于获取您的个人信息",
                "lang": "en",
                "version": "3.7.12"
            },
            "env": 1,
            "operate_directly": false,
            "show_confirm": true,
            "tid": Date.now() * 1000000 + Math.floor(Math.random() * 1000000), // 生成唯一tid
            "with_credentials": true
        });

        const result = await wxcode.getUserInfo(this.wxid, this.appid, cloudFunctionData);
        if (result.success) {
            if (isDebug) console.log(`[DEBUG] 获取用户个人信息成功`);
            // 解析用户信息
            try {
                const userInfo = JSON.parse(result.rawData.data);
                if (isDebug) {
                    console.log(`[DEBUG] 用户信息:`, {
                        nickName: userInfo.nickName,
                        gender: userInfo.gender,
                        avatarUrl: userInfo.avatarUrl,
                        city: userInfo.city,
                        province: userInfo.province,
                        country: userInfo.country
                    });
                }
                this.userProfile = {
                    success: true,
                    userInfo: userInfo,
                    signature: result.signature,
                    encryptedData: result.encryptedData,
                    iv: result.iv
                };
                return this.userProfile;
            } catch (e) {
                console.log(`解析用户信息失败：${e.message}`);
                return { success: false, error: e.message };
            }
        } else {
            console.log(`获取用户个人信息失败：${result.error}`);
            return null;
        }
    }

    // 验证缓存数据是否仍然有效
    async validateCache() {
        if (!this.isLogin || !this.token) return false;

        if (isDebug) console.log(`[DEBUG] 验证花生帮缓存数据有效性...`);

        try {
            // 尝试调用一个简单的接口来验证token是否有效
            // 这里可以调用一个不重要的接口来测试
            if (isDebug) console.log(`[DEBUG] 花生帮缓存数据验证通过（跳过实际验证）`);
            return true;
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 花生帮缓存数据验证失败: ${error.message}`);
        }

        if (isDebug) console.log(`[DEBUG] 花生帮缓存数据已失效`);
        this.isLogin = false;
        return false;
    }

    // 花生帮小程序登录
    async hsb_miniappLogin() {
        if (isDebug) console.log(`[DEBUG] 开始花生帮小程序登录...`);

        try {
            // 构建请求URL
            const url = `${this.baseUrl}/marketing/v1/wechat-user-auth/miniapp-login`;
            const params = {
                enterpriseNo: this.enterpriseNo,
                authorizerAppid: this.authorizerAppid,
                jsCode: this.wxCode
            };

            const result = await this.httpRequest(url, 'GET', params);

            if (result.code === 200) {
                this.sessionKey = result.data.sessionKey;
                this.openid = result.data.openid;
                this.serviceSign = result.data.serviceSign;

                if (isDebug) {
                    console.log(`[DEBUG] 小程序登录成功`);
                    console.log(`[DEBUG] SessionKey: ${this.sessionKey}`);
                    console.log(`[DEBUG] OpenID: ${this.openid}`);
                    console.log(`[DEBUG] ServiceSign: ${this.serviceSign}`);
                }
                return true;
            } else {
                console.log(`❌ 小程序登录失败: ${result.message}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ 小程序登录异常: ${error.message}`);
            return false;
        }
    }

    // 花生帮用户登录
    async hsb_customerLogin() {
        if (isDebug) console.log(`[DEBUG] 开始花生帮用户登录...`);

        try {
            const url = `${this.baseUrl}/marketing/v1/customer-login/wechat-openid`;
            const data = {
                enterpriseNo: this.enterpriseNo,
                serviceSign: this.serviceSign,
                openid: this.openid,
                appId: this.authorizerAppid,
                appType: 2
            };

            const result = await this.httpRequest(url, 'POST', data);

            if (result.code === 200) {
                this.token = result.data.token;
                this.userId = result.data.id;

                if (isDebug) {
                    console.log(`[DEBUG] 用户登录成功`);
                    console.log(`[DEBUG] Token: ${this.token}`);
                    console.log(`[DEBUG] UserID: ${this.userId}`);
                    console.log(`[DEBUG] IsNewUser: ${result.data.isNewUser}`);
                }
                return true;
            } else {
                console.log(`❌ 用户登录失败: ${result.message}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ 用户登录异常: ${error.message}`);
            return false;
        }
    }

    // 花生帮完整登录流程
    async hsb_fullLogin() {
        if (isDebug) console.log(`[DEBUG] 开始花生帮完整登录流程...`);

        // 1. 获取微信授权码
        const codeResult = await this.getWxCodeAndLogin();
        if (!codeResult) {
            console.log(`❌ 获取微信授权码失败`);
            return false;
        }

        // 2. 小程序登录
        const miniappLoginResult = await this.hsb_miniappLogin();
        if (!miniappLoginResult) {
            console.log(`❌ 小程序登录失败`);
            return false;
        }

        // 3. 用户登录
        const customerLoginResult = await this.hsb_customerLogin();
        if (!customerLoginResult) {
            console.log(`❌ 用户登录失败`);
            return false;
        }

        console.log(`✅ 花生帮登录成功`);

        // 保存token到缓存
        this.saveTokenCache();

        return true;
    }

    // 花生帮签到
    async hsb_justSign() {
        if (isDebug) console.log(`[DEBUG] 开始花生帮签到...`);

        try {
            const url = `${this.baseUrl}/marketing/sign/woBeiCus/v1/justSign`;
            const data = {
                enterpriseNo: this.enterpriseNo
            };

            const result = await this.httpRequest(url, 'POST', data);

            if (result.code === 200) {
                const reward = result.data;
                console.log(`✅ 签到成功！获得奖励: ${reward}`);
                print(`[${this.wxid}] 签到成功！获得奖励: ${reward}`, true);
                return {
                    success: true,
                    reward: reward
                };
            } else {
                console.log(`❌ 签到失败: ${result.message}`);
                print(`[${this.wxid}] 签到失败: ${result.message}`, true);
                return {
                    success: false,
                    error: result.message
                };
            }
        } catch (error) {
            console.log(`❌ 签到异常: ${error.message}`);
            print(`[${this.wxid}] 签到异常: ${error.message}`, true);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 执行完整的数据获取流程（已废弃，使用花生帮登录流程）
    async performFullLogin() {
        if (isDebug) console.log(`[DEBUG] 使用花生帮登录流程，跳过原始登录流程...`);
        return await this.hsb_fullLogin();
    }

    // 主要业务逻辑
    async run() {
        try {
            // 1. 尝试从缓存加载数据
            const cacheLoaded = this.loadTokenCache();

            if (cacheLoaded) {
                console.log(`📦 使用缓存的数据`);

                // 验证缓存数据是否仍然有效
                const cacheValid = await this.validateCache();
                if (!cacheValid) {
                    console.log(`⚠️ 缓存的数据已失效，重新获取...`);
                    const fullLoginSuccess = await this.performFullLogin();
                    if (!fullLoginSuccess) {
                        console.log(`[${this.wxid}] 完整登录失败，跳过`);
                        return;
                    }
                } else {
                    console.log(`✅ 缓存的数据有效`);
                }
            } else {
                // 2. 缓存无效或不存在，进行完整登录
                const fullLoginSuccess = await this.performFullLogin();
                if (!fullLoginSuccess) {
                    print(`[${this.wxid}] 完整登录失败，跳过`, true);
                    return;
                }
            }

            // 3. 花生帮业务逻辑
            console.log(`🎯 开始执行花生帮业务...`);

            // 检查是否已有花生帮登录信息
            if (!this.token) {
                console.log(`🔑 执行花生帮登录...`);
                const loginResult = await this.hsb_fullLogin();
                if (!loginResult) {
                    print(`[${this.wxid}] 花生帮登录失败，跳过后续操作`, true);
                    return;
                }
                // 保存登录信息到缓存
                this.saveTokenCache();
            } else {
                console.log(`✅ 使用缓存的花生帮登录信息`);
            }

            // 执行签到
            console.log(`📝 开始执行签到...`);
            const signResult = await this.hsb_justSign();

            if (signResult.success) {
                console.log(`🎉 [${this.wxid}] 今日任务完成！`);
            } else {
                console.log(`⚠️ [${this.wxid}] 签到失败，但流程继续`);
            }
        } catch (error) {
            console.log(`[${this.wxid}] 脚本执行出错：${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }
    }
}

// 主函数
async function main() {
    console.log(`🔔 脚本开始执行`);
    
    if (isDebug) {
        console.log(`[DEBUG] 调试模式已开启`);
        console.log(`[DEBUG] APPID: ${APPID}`);
    }
    
    if (!wxidList) {
        console.log(`❌ 未设置环境变量 TXX_WXID 或命令行参数 --wxid`);
        return;
    }

    // 处理单个wxid或多个wxid
    const wxids = cmdWxid ? [cmdWxid] : parseWxidList(wxidList);

    if (wxids.length === 0) {
        console.log(`❌ 没有找到有效的wxid`);
        return;
    }

    console.log(`📋 共找到 ${wxids.length} 个有效账号`);

    if (isDebug) {
        console.log(`[DEBUG] 账号列表: ${wxids.join(', ')}`);
    }

    // 逐个处理账号
    for (let i = 0; i < wxids.length; i++) {
        const wxid = wxids[i];
        console.log(`\n🚀 [${i + 1}/${wxids.length}] 开始处理账号: ${wxid}`);

        try {
            const script = new ScriptTemplate(wxid);
            await script.run();
            console.log(`✅ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理完成`);
        } catch (error) {
            console.log(`❌ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理失败: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }

        console.log('─'.repeat(60));

        // 如果不是最后一个账号，稍微延迟一下
        if (i < wxids.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    console.log(`\n🎉 所有账号处理完成！`);

    // 发送通知
    if (NOTICE_SWITCH && notice) {
        await sendMsg(notice);
    }
}

// 通知相关变量和函数
let notice = '';

function print(msg, is_notice = false) {
    let str = `${msg}`;
    console.log(str);
    if (NOTICE_SWITCH && is_notice) {
        notice += `${str}\n`;
    }
}

async function sendMsg(message) {
    try {
        let notify = '';
        try {
            notify = require('./sendNotify');
        } catch (e) {
            try {
                notify = require("../sendNotify");
            } catch (e2) {
                console.log('❌ 未找到sendNotify模块，无法发送通知');
                return;
            }
        }
        await notify.sendNotify(scriptName, message);
        console.log('📢 通知发送成功');
    } catch (error) {
        console.log(`❌ 通知发送失败: ${error.message}`);
    }
}

// 执行脚本
main().catch(console.error);
