{"entryPagePath": "pages/Home/Home", "pages": ["pages/Home/Home", "pages/SecurityCheck/SecurityCheck", "pages/privacyContract/privacyContract", "pages/Home/ProList/ProList", "pages/Home/BrandList/BrandList", "pages/notice/notice", "pages/signIn/signIn", "pages/storeClockIn/ClockInRecords/ClockInRecords", "pages/storeClockIn/storeClockIn", "pages/drawReward/drawReward", "pages/TaskCenter/TaskCenter", "pages/empower/UserAgreement/UserAgreement", "pages/empower/PrivacyPolicy/PrivacyPolicy", "pages/empower/empower", "pages/PersonalCenter/IconAddress/IconAddress", "pages/EnergyMall/SubmitOrder/SubmitOrder", "pages/EnergyMall/GoodsInfo/GoodsInfo", "pages/EnergyMall/EnergyMall", "pages/Home/VideoList/VideoList", "pages/index/index", "pages/PersonalCenter/Address/Address", "pages/PersonalCenter/MyAddress/MyAddress", "pages/PersonalCenter/PersonalCenter", "pages/PersonalCenter/EnergyDetail/EnergyDetail", "pages/PersonalCenter/EnergyRule/EnergyRule", "pages/PersonalCenter/OrderList/OrderList", "pages/PersonalCenter/RewardRecord/RewardRecord", "pages/PersonalCenter/OrderInfo/OrderInfo", "pages/PersonalCenter/SetUp/SetUp", "pages/PersonalCenter/SetName/SetName", "pages/Home/BannerInfo/BannerInfo", "pages/WebUrl/WebUrl", "pages/storeClockIn/storeList/storeList", "pages/hssws/hssws", "pages/hssws/Info/Info", "pages/PersonalCenter/RuleText/RuleText"], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "tabBar": {"custom": true, "color": "#000", "selectedColor": "#D80E34", "borderStyle": "white", "backgroundColor": "#ffffff", "list": [{"text": "首页", "pagePath": "pages/Home/Home", "iconPath": "images\\homeIconNo.png", "selectedIconPath": "images\\homeIcon.png"}, {"text": "能量商城", "pagePath": "pages/EnergyMall/EnergyMall", "iconPath": "images\\energyMallIconNo.png", "selectedIconPath": "images\\energyMallIcon.png"}, {"text": "我的", "pagePath": "pages/PersonalCenter/PersonalCenter", "iconPath": "images\\myIconNo.png", "selectedIconPath": "images\\myIcon.png"}]}, "style": "v2", "renderer": "webview", "requiredPrivateInfos": ["getLocation", "chooseLocation", "<PERSON><PERSON><PERSON><PERSON>"], "componentFramework": "exparser", "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTitleText": "", "navigationBarTextStyle": "black"}, "plugins": {}}