var n = require("../../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  t = require("../../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  i = require("../../../../D2986AA6549B04BFB4FE02A105650D65.js");
Page({
  data: {
    statusHeaderBarHeight: n.statusHeaderBarHeight,
    imgUrl: n.imgUrl,
    imgVersion: n.imgVersion,
    screenInfo: (0, t.getScreenInfo)(),
    artistData: i.artistData,
    index: 0
  },
  onLogin: function(n) {
    n.detail.loginStatus && this.initData()
  },
  initData: function() {
    console.log("初始化登录")
  },
  handleTest: function(n) {},
  onLoad: function(n) {},
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return n.shareOptions
  }
});