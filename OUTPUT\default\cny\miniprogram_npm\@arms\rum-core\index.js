require("../../../../@babel/runtime/helpers/Arrayincludes");
var e, t, n, r = require("../../../../@babel/runtime/helpers/typeof");
module.exports = (e = {}, n = function(t, n) {
  if (!e[t]) return require(n);
  if (!e[t].status) {
    var i = e[t].m;
    i._exports = i._tempexports;
    var o = Object.getOwnPropertyDescriptor(i, "exports");
    o && o.configurable && Object.defineProperty(i, "exports", {
      set: function(e) {
        "object" === r(e) && e !== i._exports && (i._exports.__proto__ = e.__proto__, Object.keys(e).forEach((function(t) {
          i._exports[t] = e[t]
        }))), i._tempexports = e
      },
      get: function() {
        return i._tempexports
      }
    }), e[t].status = 1, e[t].func(e[t].req, i, i.exports)
  }
  return e[t].m.exports
}, (t = function(t, n, r) {
  e[t] = {
    status: 0,
    func: n,
    req: r,
    m: {
      exports: {},
      _tempexports: {}
    }
  }
})(1739784025842, (function(e, t, n) {
  var r = e("@babel/runtime/helpers/interopRequireDefault");
  n.__esModule = !0;
  var i = {
      Client: !0,
      Context: !0,
      Reporter: !0,
      Shell: !0
    },
    o = r(e("./model/client"));
  n.Client = o.default;
  var s = r(e("./model/context"));
  n.Context = s.default;
  var u = r(e("./model/reporter"));
  n.Reporter = u.default;
  var a = r(e("./model/shell"));
  n.Shell = a.default;
  var c = e("./types/client");
  Object.keys(c).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === c[e] || (n[e] = c[e]))
  }));
  var l = e("./types/collector");
  Object.keys(l).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === l[e] || (n[e] = l[e]))
  }));
  var f = e("./types/processor");
  Object.keys(f).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === f[e] || (n[e] = f[e]))
  }));
  var p = e("./types/reporter");
  Object.keys(p).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === p[e] || (n[e] = p[e]))
  }));
  var v = e("./types/rum-event");
  Object.keys(v).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === v[e] || (n[e] = v[e]))
  }));
  var d = e("./types/shell");
  Object.keys(d).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === d[e] || (n[e] = d[e]))
  }));
  var h = e("./utils/base");
  Object.keys(h).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === h[e] || (n[e] = h[e]))
  }));
  var m = e("./utils/polyfills");
  Object.keys(m).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === m[e] || (n[e] = m[e]))
  }));
  var y = e("./utils/is");
  Object.keys(y).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === y[e] || (n[e] = y[e]))
  }));
  var g = e("./utils/number");
  Object.keys(g).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === g[e] || (n[e] = g[e]))
  }));
  var b = e("./utils/match");
  Object.keys(b).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === b[e] || (n[e] = b[e]))
  }));
  var E = e("./utils/trace");
  Object.keys(E).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === E[e] || (n[e] = E[e]))
  }));
  var _ = e("./utils/verify");
  Object.keys(_).forEach((function(e) {
    "default" !== e && "__esModule" !== e && (Object.prototype.hasOwnProperty.call(i, e) || e in n && n[e] === _[e] || (n[e] = _[e]))
  }))
}), (function(e) {
  return n({
    "./model/client": 1739784025843,
    "./model/context": 1739784025845,
    "./model/reporter": 1739784025847,
    "./model/shell": 1739784025851,
    "./types/client": 1739784025844,
    "./types/collector": 1739784025852,
    "./types/processor": 1739784025853,
    "./types/reporter": 1739784025854,
    "./types/rum-event": 1739784025848,
    "./types/shell": 1739784025855,
    "./utils/base": 1739784025856,
    "./utils/polyfills": 1739784025857,
    "./utils/is": 1739784025846,
    "./utils/number": 1739784025858,
    "./utils/match": 1739784025859,
    "./utils/trace": 1739784025860,
    "./utils/verify": 1739784025850
  } [e], e)
})), t(1739784025843, (function(e, t, n) {
  var r = e("@babel/runtime/helpers/interopRequireDefault");
  n.__esModule = !0, n.default = void 0;
  var i = e("../types/client"),
    o = e("events"),
    s = r(e("./context")),
    u = e("../utils/is");

  function a(e, t) {
    var n = "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
    if (n) return (n = n.call(e)).next.bind(n);
    if (Array.isArray(e) || (n = function(e, t) {
        if (e) {
          if ("string" == typeof e) return c(e, t);
          var n = Object.prototype.toString.call(e).slice(8, -1);
          return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? c(e, t) : void 0
        }
      }(e)) || t && e && "number" == typeof e.length) {
      n && (e = n);
      var r = 0;
      return function() {
        return r >= e.length ? {
          done: !0
        } : {
          done: !1,
          value: e[r++]
        }
      }
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
  }

  function c(e, t) {
    (null == t || t > e.length) && (t = e.length);
    for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
    return r
  }
  var l = function() {
    function e() {
      var e = this;
      this.emitter = new o.EventEmitter, this.collectors = void 0, this.processors = void 0, this.reporter = void 0, this.ctx = void 0, this.sendEvent = function(t) {
        e.emitter.emit(i.EventType.collect, t)
      }
    }
    var t = e.prototype;
    return t.init = function(e, t) {
      var n = this;
      this.ctx = new s.default(e, t);
      var r = this.ctx,
        o = this.collectors,
        c = this.processors,
        l = this.reporter;
      c.forEach((function(e) {
        (0, u.isFunction)(e.setup) && e.setup(r)
      })), this.emitter.on(i.EventType.collect, (function(e) {
        r.setRumEvent(e);
        for (var t, n = a(c); !(t = n()).done;) {
          var i = t.value;
          if (i.match || (i.match = function() {
              return !0
            }), i.match(r)) {
            var o = i.process(r);
            o && r.setRumEvent(o)
          }
        }
        l.report(r)
      })), o.forEach((function(e) {
        e.setup(r, n.sendEvent)
      }))
    }, t.setContext = function(e) {
      this.ctx = e
    }, t.getContext = function() {
      return this.ctx
    }, t.useCollectors = function(e) {
      this.collectors = e
    }, t.useProcessors = function(e) {
      this.processors = e
    }, t.useReporter = function(e) {
      this.reporter = e
    }, e
  }();
  n.default = l
}), (function(e) {
  return n({
    "../types/client": 1739784025844,
    "./context": 1739784025845,
    "../utils/is": 1739784025846
  } [e], e)
})), t(1739784025844, (function(e, t, n) {
  n.__esModule = !0, n.EventType = void 0, n.EventType = function(e) {
    return e.collect = "collect", e
  }({})
}), (function(e) {
  return n({} [e], e)
})), t(1739784025845, (function(e, t, n) {
  n.__esModule = !0, n.default = void 0;
  var r = function() {
    function e(e, t) {
      this.config = e, this.rumEvent = void 0, this.views = [], this.session = void 0, t && (this.session = t, this.session.init(this))
    }
    var t = e.prototype;
    return t.getConfig = function() {
      return this.config
    }, t.setConfig = function(e) {
      this.config = e
    }, t.getViews = function() {
      return this.views
    }, t.addView = function(e) {
      this.views.push(e)
    }, t.removeView = function(e) {
      this.views = this.views.filter((function(t) {
        return t.id !== e
      }))
    }, t.getRumEvent = function() {
      return this.rumEvent
    }, t.setRumEvent = function(e) {
      this.rumEvent = e
    }, e
  }();
  n.default = r
}), (function(e) {
  return n({} [e], e)
})), t(1739784025846, (function(e, t, n) {
  function i(e, t) {
    return r(e) === t
  }

  function o(e, t) {
    return Object.prototype.toString.call(e) === "[object " + t + "]"
  }
  n.__esModule = !0, n.isString = n.isRegExp = n.isObject = n.isNumber = n.isNull = n.isFunction = n.isBoolean = n.isArray = void 0, n.isToString = o, n.isTypeof = i, n.isZero = function() {
    for (var e = 0; e < arguments.length; e++)
      if (0 !== (e < 0 || arguments.length <= e ? void 0 : arguments[e])) return !1;
    return !0
  }, n.isFunction = function(e) {
    return "function" == typeof e
  }, n.isString = function(e) {
    return i(e, "string")
  }, n.isArray = function(e) {
    return o(e, "Array")
  }, n.isRegExp = function(e) {
    return o(e, "RegExp")
  }, n.isBoolean = function(e) {
    return i(e, "boolean")
  }, n.isNumber = function(e) {
    return i(e, "number") && !isNaN(e) || i(e, "bigint")
  };
  var s = n.isNull = function(e) {
    return null === e
  };
  n.isObject = function(e) {
    return !s(e) && i(e, "object")
  }
}), (function(e) {
  return n({} [e], e)
})), t(1739784025847, (function(e, t, n) {
  n.__esModule = !0, n.default = void 0;
  var r = e("../types/rum-event"),
    i = e("../utils/exception"),
    o = e("../utils/is"),
    s = e("../utils/verify"),
    u = ["app", "user", "device", "os", "geo", "isp", "net", "properties"];
  n.default = function() {
    function e() {
      this.name = "reporter", this.eventQueue = [], this.ctx = void 0, this.timer = void 0
    }
    var t = e.prototype;
    return t.getReportCfg = function() {
      var e = this.ctx.getConfig().reportConfig;
      return (0, o.isObject)(e) || (e = {}), (!e.flushTime || e.flushTime < 0 || e.flushTime > 1e4) && (e.flushTime = 3e3), (!e.maxEventCount || e.maxEventCount < 1 || e.maxEventCount > 100) && (e.maxEventCount = 20), e
    }, t.report = function(e) {
      var t = this;
      this.ctx = e, this.init(e), clearTimeout(this.timer), this.pushToQueue();
      var n = this.getReportCfg();
      this.eventQueue.length >= n.maxEventCount ? this.flushEventQueue() : this.timer = setTimeout((function() {
        t.flushEventQueue()
      }), n.flushTime)
    }, t.pushToQueue = function() {
      var e = this.ctx,
        t = this.eventQueue,
        n = e.getRumEvent();
      if (n.event_type === r.RumEventType.EXCEPTION) {
        var o = n,
          s = o.message,
          u = o.stack,
          a = (0, i.getErrorID)({
            message: s,
            stack: u
          }),
          c = this.eventQueue.find((function(e) {
            if (e.event_type === r.RumEventType.EXCEPTION) {
              var t = e,
                n = t.message,
                o = t.stack;
              return (0, i.getErrorID)({
                message: n,
                stack: o
              }) === a
            }
          }));
        if (c) return void c.times++
      }
      if (n.event_type === r.RumEventType.ACTION) {
        var l = n.target_name,
          f = this.eventQueue.find((function(e) {
            if (e.event_type === r.RumEventType.ACTION) return l === e.target_name
          }));
        if (f) return void f.times++
      }
      t.push(n)
    }, t.flushEventQueue = function() {
      var e = this.ctx,
        t = this.eventQueue;
      if (t.length) {
        var n = e.getViews(),
          r = n[n.length - 1];
        n.forEach((function(e) {
          e.id === r.id && t.filter((function(t) {
            var n;
            return (null === (n = t.view) || void 0 === n ? void 0 : n.id) === e.id
          })).forEach((function(e) {
            delete e.view
          }))
        }));
        var i = e.session;
        (!i || i.getSampled()) && this.mergeEvent(e, t, r), this.eventQueue = []
      }
    }, t.mergeEvent = function(e, t, n) {
      for (var r, i = e.getConfig(), a = e.session, c = a.getSessionId(), l = 0; l < t.length; l++) {
        var f = t[l];
        f.session_id === c ? delete f.session_id : (t.splice(l, 1), l--)
      }
      if (0 !== t.length) {
        var p = {
          app: {
            id: i.pid,
            env: i.env || "prod",
            version: i.version,
            type: ""
          },
          user: {
            id: a.getUserId()
          },
          session: {
            id: c
          },
          net: {
            model: null === (r = i.net) || void 0 === r ? void 0 : r.model
          },
          view: n,
          events: t
        };
        u.forEach((function(e) {
          var t = i[e];
          (0, o.isObject)(t) && Object.keys(t).forEach((function(n) {
            var r = t[n];
            "user" === e && "id" === n || ("properties" === e ? p[e] = (0, s.verifyProperties)(t) : ((0, o.isString)(r) || (0, o.isNumber)(r)) && (e in p || (p[e] = {}), p[e][n] = r))
          }))
        })), ("function" != typeof i.beforeReport || (p = i.beforeReport(p))) && this.request(e, p)
      }
    }, t.init = function(e) {}, e
  }()
}), (function(e) {
  return n({
    "../types/rum-event": 1739784025848,
    "../utils/exception": 1739784025849,
    "../utils/is": 1739784025846,
    "../utils/verify": 1739784025850
  } [e], e)
})), t(1739784025848, (function(e, t, n) {
  n.__esModule = !0, n.RumEventType = n.ResourceStatus = n.AppType = void 0, n.RumEventType = function(e) {
    return e.VIEW = "view", e.RESOURCE = "resource", e.EXCEPTION = "exception", e.LONG_TASK = "longTask", e.ACTION = "action", e.CUSTOM = "custom", e.APPLICATION = "application", e
  }({}), n.ResourceStatus = function(e) {
    return e[e.Unknown = -1] = "Unknown", e[e.Failed = 0] = "Failed", e[e.Success = 1] = "Success", e
  }({}), n.AppType = function(e) {
    return e.browser = "browser", e.miniapp = "miniapp", e.uniapp = "uniapp", e.minigame = "minigame", e
  }({})
}), (function(e) {
  return n({} [e], e)
})), t(1739784025849, (function(e, t, n) {
  n.__esModule = !0, n.getErrorID = void 0, n.getErrorID = function(e) {
    var t = e.message,
      n = void 0 === t ? "" : t,
      r = e.stack;
    return n + (void 0 === r ? "" : r)
  }
}), (function(e) {
  return n({} [e], e)
})), t(1739784025850, (function(e, t, n) {
  n.__esModule = !0, n.verifyProperties = function(e) {
    if ((0, r.isObject)(e)) {
      var t = Object.assign({}, e);
      Object.keys(t).forEach((function(e) {
        var n = t[e];
        if (e.length > 50) {
          var i = e.substring(0, 50);
          t[i] = n, delete t[e], e = i
        }(0, r.isString)(n) || (0, r.isNumber)(n) ? (0, r.isString)(n) && n.length > 2e3 && (t[e] = n.substring(0, 2e3)): delete t[e]
      }));
      var n = Object.keys(t);
      if (n.length) return n.length > 20 && n.forEach((function(e, n) {
        n > 19 && delete t[e]
      })), t
    }
  };
  var r = e("./is")
}), (function(e) {
  return n({
    "./is": 1739784025846
  } [e], e)
})), t(1739784025851, (function(e, t, n) {
  var r = e("@babel/runtime/helpers/interopRequireDefault");
  n.__esModule = !0, n.default = void 0;
  var i = r(e("@babel/runtime/helpers/extends")),
    o = e("../types/rum-event"),
    s = r(e("../model/client")),
    u = e("../utils/is");
  n.default = function() {
    function e(e) {
      this.client = void 0, this.client = new s.default, e && this.init && this.init(e)
    }
    var t = e.prototype;
    return t.sendEvent = function(e) {
      this.client && this.client.sendEvent(e)
    }, t.getConfig = function() {
      if (this.client) return this.client.getContext().getConfig()
    }, t.sendCustom = function(e) {
      if (e.name && e.type) {
        var t = (0, i.default)({}, e, {
          event_type: o.RumEventType.CUSTOM
        });
        this.sendEvent(t)
      }
    }, t.sendView = function(e) {
      if ((0, u.isObject)(e)) {
        if (e.type || (e.type = "custom"), "custom" === e.type) {
          var t = 0;
          if (["t1", "t2", "t3"].forEach((function(n) {
              (0, u.isNumber)(e[n]) || delete e[n], n in e && t++
            })), 0 === t) return
        }
        var n = (0, i.default)({}, e, {
          event_type: o.RumEventType.VIEW
        });
        this.sendEvent(n)
      }
    }, t.sendException = function(e) {
      if (e.name && e.message) {
        var t = e.name,
          n = e.message,
          r = e.stack,
          s = (0, i.default)({
            times: 1,
            name: t,
            message: n,
            stack: r
          }, e, {
            event_type: o.RumEventType.EXCEPTION,
            type: "custom",
            source: "custom"
          });
        this.sendEvent(s)
      }
    }, t.sendResource = function(e) {
      if (e.name && e.type && (0, u.isNumber)(e.duration)) {
        var t = (0, i.default)({
          times: 1
        }, e, {
          event_type: o.RumEventType.RESOURCE
        });
        this.sendEvent(t)
      }
    }, e
  }()
}), (function(e) {
  return n({
    "../types/rum-event": 1739784025848,
    "../model/client": 1739784025843,
    "../utils/is": 1739784025846
  } [e], e)
})), t(1739784025852, (function(e, t, n) {
  n.__esModule = !0
}), (function(e) {
  return n({} [e], e)
})), t(1739784025853, (function(e, t, n) {
  n.__esModule = !0
}), (function(e) {
  return n({} [e], e)
})), t(1739784025854, (function(e, t, n) {
  n.__esModule = !0
}), (function(e) {
  return n({} [e], e)
})), t(1739784025855, (function(e, t, n) {
  n.__esModule = !0
}), (function(e) {
  return n({} [e], e)
})), t(1739784025856, (function(e, t, n) {
  n.__esModule = !0, n.debounce = function(e, t) {
    var n;
    if ((0, r.isFunction)(e)) return function() {
      var r = this,
        i = arguments;
      n && clearTimeout(n), n = setTimeout((function() {
        e.apply(r, i)
      }), t)
    }
  }, n.delay = function(e, t) {
    if ((0, r.isFunction)(e)) {
      for (var n = arguments.length, i = new Array(n > 2 ? n - 2 : 0), o = 2; o < n; o++) i[o - 2] = arguments[o];
      return setTimeout.apply(void 0, [e, +t || 0].concat(i))
    }
  }, n.generateEventId = function(e) {
    var t = o();
    return "00-" + e + "-" + t
  }, n.generateGUID = i, n.generateSpanId = o, n.generateTraceId = function() {
    var e = i().replace(/-/g, "");
    return "0" === e[0] && (e = "1" + e.substring(1)), "0" === e[16] && (e = e.substring(0, 16) + "1" + e.substring(17)), e
  }, n.interceptFunction = function(e, t, n) {
    var i = e[t];
    e[t] = function() {
      for (var e = arguments.length, t = new Array(e), o = 0; o < e; o++) t[o] = arguments[o];
      if (n.apply(this, t), (0, r.isFunction)(i)) return i.apply(this, t)
    }
  };
  var r = e("./is");

  function i() {
    var e = "";
    try {
      if (crypto && crypto.randomUUID) e = crypto.randomUUID();
      else if (crypto && crypto.getRandomValues) {
        var t = new Uint8Array(16);
        crypto.getRandomValues(t), t[6] = 15 & t[6] | 64, t[8] = 63 & t[8] | 128, e = t.reduce((function(e, t) {
          return e + t.toString(16).padStart(2, "0")
        }), "").replace(/^(.{8})(.{4})(.{4})(.{4})(.{12})$/, "$1-$2-$3-$4-$5")
      }
    } catch (e) {}
    return e || (e = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function(e) {
      var t = 16 * Math.random() | 0;
      return ("x" == e ? t : 3 & t | 8).toString(16)
    }))), e
  }

  function o(e, t) {
    void 0 === e && (e = 16), void 0 === t && (t = 16);
    for (var n = "", r = 0; r < e; r++) n += Math.floor(Math.random() * t).toString(t);
    return n
  }
}), (function(e) {
  return n({
    "./is": 1739784025846
  } [e], e)
})), t(1739784025857, (function(e, t, n) {
  n.__esModule = !0, n.assign = function(e) {
    for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), r = 1; r < t; r++) n[r - 1] = arguments[r];
    return n.forEach((function(t) {
      for (var n in t) Object.prototype.hasOwnProperty.call(t, n) && (e[n] = t[n])
    })), e
  }, n.endsWith = function(e, t) {
    return e.slice(-t.length) === t
  }, n.find = function(e, t) {
    for (var n = 0; n < e.length; n += 1) {
      var r = e[n];
      if (t(r, n)) return r
    }
  }, n.startsWith = function(e, t) {
    return e.slice(0, t.length) === t
  }
}), (function(e) {
  return n({} [e], e)
})), t(1739784025858, (function(e, t, n) {
  n.__esModule = !0, n.ONE_SECOND = n.ONE_MINUTE = n.ONE_HOUR = n.ONE_DAY = void 0, n.formatNumber = function(e, t, n) {
    if (void 0 === t && (t = 3), void 0 === n && (n = 0), !e) return e;
    var r = e.toString(),
      i = r.indexOf(".");
    r = -1 !== i ? r.substring(0, t + i + 1) : r.substring(0);
    var o = parseFloat(r);
    return o >= n ? o : void 0
  }, n.performDraw = function(e) {
    return 0 !== e && 100 * Math.random() <= e
  };
  var r = n.ONE_SECOND = 1e3,
    i = n.ONE_MINUTE = 60 * r,
    o = n.ONE_HOUR = 60 * i;
  n.ONE_DAY = 24 * o
}), (function(e) {
  return n({} [e], e)
})), t(1739784025859, (function(e, t, n) {
  n.__esModule = !0, n.isMatchOption = function(e) {
    return (0, r.isString)(e) || (0, r.isFunction)(e) || (0, r.isRegExp)(e)
  }, n.matchList = o, n.urlMatch = function(e, t) {
    void 0 === t && (t = []);
    try {
      return (0, r.isArray)(t) || (t = [t]), o([/^(https?:)?\/\/.*rum|retcode|log-global|log\.aliyuncs\.com/, /^(https?:)?\/\/.*\.mmstat\.com/, /^(https?:)?\/\/.*hm\.baidu\.com/, /^(https?:)?\/\/.*google-analytics\.com/, /data:(.+?)(;base64)?,(.+)$/].concat(t), e)
    } catch (e) {
      return !1
    }
  };
  var r = e("./is"),
    i = e("./polyfills");

  function o(e, t, n) {
    return void 0 === n && (n = !0), e.some((function(e) {
      try {
        if ("function" == typeof e) return e(t);
        if (e instanceof RegExp) return e.test(t);
        if ("string" == typeof e) return n ? (0, i.startsWith)(t, e) : e === t
      } catch (e) {}
      return !1
    }))
  }
}), (function(e) {
  return n({
    "./is": 1739784025846,
    "./polyfills": 1739784025857
  } [e], e)
})), t(1739784025860, (function(e, t, n) {
  n.__esModule = !0, n.isTraceOption = o, n.makeTracingHeaders = function(e, t, n, r, o) {
    void 0 === o && (o = {});
    var s = {},
      u = n ? "1" : "0";
    return r.includes("sw8") && (r = ["sw8"]), r.forEach((function(n) {
      switch (n) {
        case "jaeger":
          s["uber-trace-id"] = e + ":" + t + ":0:" + u;
          break;
        case "b3":
          s.b3 = e + "-" + t + "-" + u;
          break;
        case "b3multi":
          s["X-B3-TraceId"] = e, s["X-B3-SpanId"] = t, s["X-B3-Sampled"] = u;
          break;
        case "sw8":
          if ((0, i.isFunction)(btoa)) {
            var r = btoa(e),
              a = btoa(t),
              c = btoa(o.appId),
              l = btoa(o.appVersion),
              f = btoa(o.viewName),
              p = btoa(o.host);
            s.sw8 = u + "-" + r + "-" + a + "-0-" + c + "-" + l + "-" + f + "-" + p
          }
          break;
        case "tracecontext":
        default:
          s.traceparent = "00-" + e + "-" + t + "-0" + u, o.tracestate && (s.tracestate = o.tracestate)
      }
    })), o.baggage && (s.baggage = o.baggage), s
  }, n.parseTracingOptions = function(e) {
    var t = ["tracecontext"];
    if ((0, i.isBoolean)(e) || !e) return {
      enable: !!e,
      sample: 100,
      propagatorTypes: t,
      allowedUrls: [],
      tracestate: !0,
      baggage: !1
    };
    var n = e.enable,
      s = void 0 === n || n,
      u = e.sample,
      a = void 0 === u ? 100 : u,
      c = e.propagatorTypes,
      l = void 0 === c ? t : c,
      f = e.allowedUrls,
      p = void 0 === f ? [] : f,
      v = e.tracestate,
      d = void 0 === v || v,
      h = e.baggage,
      m = void 0 !== h && h,
      y = [];
    return (0, i.isArray)(p) && p.length && p.forEach((function(e) {
      (0, r.isMatchOption)(e) ? y.push({
        match: e,
        propagatorTypes: l
      }): o(e) && y.push(e)
    })), {
      enable: !(0, i.isBoolean)(s) || s,
      sample: (0, i.isNumber)(a) ? a : 100,
      propagatorTypes: l,
      allowedUrls: y,
      tracestate: d,
      baggage: m
    }
  };
  var r = e("./match"),
    i = e("./is");

  function o(e) {
    return e && (0, r.isMatchOption)(e.match) && (0, i.isArray)(e.propagatorTypes)
  }
}), (function(e) {
  return n({
    "./match": 1739784025859,
    "./is": 1739784025846
  } [e], e)
})), n(1739784025842));