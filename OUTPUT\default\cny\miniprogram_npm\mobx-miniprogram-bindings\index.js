var r = require("../../../@babel/runtime/helpers/typeof"),
  n = function(r, n) {
    return function() {
      return n || r((n = {
        exports: {}
      }).exports, n), n.exports
    }
  },
  e = n((function() {})),
  t = n((function(n) {
    Object.defineProperty(n, "__esModule", {
        value: !0
      }),
      function(r, n) {
        for (var e in n) Object.defineProperty(r, e, {
          enumerable: !0,
          get: n[e]
        })
      }(n, {
        createActions: function() {
          return u
        },
        createDataFieldsReactions: function() {
          return c
        }
      });
    var e = require("mobx-miniprogram");

    function t(r, n) {
      (null == n || n > r.length) && (n = r.length);
      for (var e = 0, t = Array(n); e < n; e++) t[e] = r[e];
      return t
    }

    function i(r, n, e) {
      return n in r ? Object.defineProperty(r, n, {
        value: e,
        enumerable: !0,
        configurable: !0,
        writable: !0
      }) : r[n] = e, r
    }

    function o(n) {
      return function(r) {
        if (Array.isArray(r)) return t(r)
      }(n) || function(n) {
        if (("undefined" == typeof Symbol ? "undefined" : r(Symbol)) < "u" && null != n[Symbol.iterator] || null != n["@@iterator"]) return Array.from(n)
      }(n) || function(r, n) {
        if (r) {
          if ("string" == typeof r) return t(r, void 0);
          var e = Object.prototype.toString.call(r).slice(8, -1);
          if ("Object" === e && r.constructor && (e = r.constructor.name), "Map" === e || "Set" === e) return Array.from(e);
          if ("Arguments" === e || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)) return t(r, void 0)
        }
      }(n) || function() {
        throw TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
      }()
    }

    function a(n) {
      return n && ("undefined" == typeof Symbol ? "undefined" : r(Symbol)) < "u" && n.constructor === Symbol ? "symbol" : r(n)
    }
    var u = function(r, n) {
        var e = n.store,
          t = n.actions;
        if (t) {
          if (void 0 === e) throw Error("[mobx-miniprogram] no store specified");
          Array.isArray(t) ? t.forEach((function(n) {
            if (r[n]) throw Error("[mobx-miniprogram] multiple action definition");
            r[n] = function() {
              for (var r = arguments.length, t = Array(r), i = 0; i < r; i++) t[i] = arguments[i];
              return e[n].apply(e, o(t))
            }
          })) : "object" === (void 0 === t ? "undefined" : a(t)) && Object.keys(t).forEach((function(n) {
            var i = t[n];
            if ("string" != typeof n && "number" != typeof n) throw Error("[mobx-miniprogram] unrecognized field definition");
            r[n] = function() {
              for (var r = arguments.length, n = Array(r), t = 0; t < r; t++) n[t] = arguments[t];
              return e[i].apply(e, o(n))
            }
          }))
        }
      },
      c = function(n, t) {
        var o = t.store,
          u = t.fields,
          c = t.structuralComparison,
          s = t.namespace || "";
        if (s && "string" != typeof s) throw Error("[mobx-miniprogram] namespace only expect string");
        var f = Object.assign({}, n[s = s.replace(RegExp(" ", "gm"), "")]),
          d = c ? e.comparer.structural : void 0,
          m = null,
          p = function() {
            if (null !== m) {
              var r = m;
              m = null, n.setData(r)
            }
          },
          l = function(n, t) {
            var o, a;
            (m || (m = {}, ("undefined" == typeof wx ? "undefined" : r(wx)) < "u" ? wx.nextTick(p) : Promise.resolve().then(p)), "" !== s) ? (o = function(r) {
              for (var n = 1; n < arguments.length; n++) {
                var e = null != arguments[n] ? arguments[n] : {},
                  t = Object.keys(e);
                "function" == typeof Object.getOwnPropertySymbols && (t = t.concat(Object.getOwnPropertySymbols(e).filter((function(r) {
                  return Object.getOwnPropertyDescriptor(e, r).enumerable
                })))), t.forEach((function(n) {
                  i(r, n, e[n])
                }))
              }
              return r
            }({}, f), a = null != (a = i({}, n, (0, e.toJS)(t))) ? a : {}, Object.getOwnPropertyDescriptors ? Object.defineProperties(o, Object.getOwnPropertyDescriptors(a)) : function(r, n) {
              var e = Object.keys(r);
              if (Object.getOwnPropertySymbols) {
                var t = Object.getOwnPropertySymbols(r);
                e.push.apply(e, t)
              }
              return e
            }(Object(a)).forEach((function(r) {
              Object.defineProperty(o, r, Object.getOwnPropertyDescriptor(a, r))
            })), f = o, m[s] = f) : m[n] = (0, e.toJS)(t)
          },
          b = [];
        if (Array.isArray(u)) {
          if (void 0 === o) throw Error("[mobx-miniprogram] no store specified");
          b = u.map((function(r) {
            return (0, e.reaction)((function() {
              return o[r]
            }), (function(n) {
              l(r, n)
            }), {
              equals: d,
              fireImmediately: !0
            })
          }))
        } else "object" === (void 0 === u ? "undefined" : a(u)) && u && (b = Object.keys(u).map((function(r) {
          var t = u[r];
          if ("function" == typeof t) return (0, e.reaction)((function() {
            return t.call(n, o)
          }), (function(n) {
            l(r, n)
          }), {
            equals: d,
            fireImmediately: !0
          });
          if ("string" != typeof r && "number" != typeof r) throw Error("[mobx-miniprogram] unrecognized field definition");
          if (void 0 === o) throw Error("[mobx-miniprogram] no store specified");
          return (0, e.reaction)((function() {
            return o[t]
          }), (function(n) {
            l(String(r), n)
          }), {
            equals: d,
            fireImmediately: !0
          })
        })));
        return {
          updateStoreBindings: p,
          destroyStoreBindings: function() {
            b.forEach((function(r) {
              return r()
            }))
          }
        }
      }
  })),
  i = n((function(r) {
    Object.defineProperty(r, "__esModule", {
      value: !0
    }), Object.defineProperty(r, "behavior", {
      enumerable: !0,
      get: function() {
        return i
      }
    }), e();
    var n = t(),
      i = Behavior({
        definitionFilter: function(r) {
          r.methods = r.methods || {};
          var e = r.storeBindings;
          r.methods._mobxMiniprogramBindings = function() {
            return e
          }, e && (Array.isArray(e) ? e.forEach((function(e) {
            (0, n.createActions)(r.methods, e)
          })) : (0, n.createActions)(r.methods, e))
        },
        lifetimes: {
          attached: function() {
            var r = this;
            if ("function" == typeof r._mobxMiniprogramBindings) {
              var e = r._mobxMiniprogramBindings();
              if (!e) return void(r._mobxMiniprogramBindings = null);
              Array.isArray(e) ? r._mobxMiniprogramBindings = e.map((function(e) {
                var t = (0, n.createDataFieldsReactions)(r, e);
                return t.updateStoreBindings(), t
              })) : (r._mobxMiniprogramBindings = (0, n.createDataFieldsReactions)(this, e), r._mobxMiniprogramBindings.updateStoreBindings())
            }
          },
          detached: function() {
            this._mobxMiniprogramBindings && (Array.isArray(this._mobxMiniprogramBindings) ? this._mobxMiniprogramBindings.forEach((function(r) {
              r.destroyStoreBindings()
            })) : this._mobxMiniprogramBindings.destroyStoreBindings())
          }
        },
        methods: {
          updateStoreBindings: function() {
            this._mobxMiniprogramBindings && "function" != typeof this._mobxMiniprogramBindings && (Array.isArray(this._mobxMiniprogramBindings) ? this._mobxMiniprogramBindings.forEach((function(r) {
              r.updateStoreBindings()
            })) : this._mobxMiniprogramBindings.updateStoreBindings())
          }
        }
      })
  }));
Object.defineProperty(exports, "__esModule", {
    value: !0
  }),
  function(r, n) {
    for (var e in n) Object.defineProperty(r, e, {
      enumerable: !0,
      get: n[e]
    })
  }(exports, {
    BehaviorWithStore: function() {
      return c
    },
    ComponentWithStore: function() {
      return u
    },
    createStoreBindings: function() {
      return s
    },
    initStoreBindings: function() {
      return d
    },
    storeBindingsBehavior: function() {
      return f
    }
  }), e();
var o = i(),
  a = t();

function u(r) {
  return Array.isArray(r.behaviors) || (r.behaviors = []), r.behaviors.unshift(o.behavior), Component(r)
}

function c(r) {
  return Array.isArray(r.behaviors) || (r.behaviors = []), r.behaviors.unshift(o.behavior), Behavior(r)
}
var s = function(r, n) {
    return (0, a.createActions)(r, n), (0, a.createDataFieldsReactions)(r, n)
  },
  f = o.behavior,
  d = function(r, n) {
    var e, t = r.self,
      i = r.lifetime;
    return i("attached", (function() {
      (e = (0, a.createDataFieldsReactions)(t, n)).updateStoreBindings()
    })), i("detached", (function() {
      e.destroyStoreBindings()
    })), {
      updateStoreBindings: function() {
        e && e.updateStoreBindings()
      }
    }
  };