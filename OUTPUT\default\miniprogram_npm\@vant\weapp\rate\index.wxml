<view bind:touchmove="onTouchMove" class="{{utils.bem('rate')}} custom-class">
    <view class="{{utils.bem('rate__item')}}" style="{{style( {paddingRight:index!==count-1?utils.addUnit(gutter):null} )}}" wx:for="{{innerCountArray}}" wx:key="index">
        <van-icon bind:click="onSelect" class="{{utils.bem( 'rate__icon',[ {disabled:disabled,full:index+1<=innerValue} ] )}}" color="{{disabled?disabledColor:index+1<=innerValue?color:voidColor}}" customClass="icon-class" data-score="{{index}}" name="{{index+1<=innerValue?icon:voidIcon}}" style="{{style( { fontSize:utils.addUnit(size) } )}}"></van-icon>
        <van-icon bind:click="onSelect" class="{{utils.bem( 'rate__icon',[ 'half',{disabled:disabled,full:index+0.5<=innerValue} ] )}}" color="{{disabled?disabledColor:index+0.5<=innerValue?color:voidColor}}" customClass="icon-class" data-score="{{index-0.5}}" name="{{index+0.5<=innerValue?icon:voidIcon}}" style="{{style( { fontSize:utils.addUnit(size) } )}}" wx:if="{{allowHalf}}"></van-icon>
    </view>
</view>

<wxs module="utils" src="..\wxs\utils.wxs"/>
<wxs module="style" src="..\wxs\style.wxs"/>