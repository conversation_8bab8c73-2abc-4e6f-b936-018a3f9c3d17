var e = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  n = require("../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  t = require("../../../A5622344549B04BFC3044B435F450D65.js");
Component({
  properties: {},
  data: {
    imgUrl: e.imgUrl,
    imgVersion: e.imgVersion,
    screenInfo: (0, n.getScreenInfo)(),
    open: !1,
    uploadImg: null,
    title: null,
    content: null,
    sunCode: null,
    storyId: null
  },
  methods: {
    openMask: function(e, n, a, o, p) {
      this.setData({
        open: !0,
        uploadImg: e,
        title: n,
        content: a,
        sunCode: o,
        storyId: p
      }), t.pagePopupViews({
        page_type: "",
        page_name: "故事征集",
        popup_name: "上传成功",
        page_path: "cny/pages/uploads/index"
      })
    },
    closeMask: function() {
      this.setData({
        open: !1
      })
    },
    handleSave: function() {
      this.triggerEvent("saveImg", {
        flag: !0
      }), t.pagePopupClickEvents({
        page_type: "故事征集",
        page_name: "故事征集",
        popup_name: "上传成功",
        page_path: "cny/pages/uploads/index",
        button_name: "保存"
      })
    },
    handleGoDetails: function() {
      this.closeMask(), wx.navigateTo({
        url: "/cny/pages/details/index?isDel=record&storyId=" + this.data.storyId
      }), t.pagePopupClickEvents({
        page_type: "故事征集",
        page_name: "故事征集",
        popup_name: "上传成功",
        page_path: "cny/pages/uploads/index",
        button_name: "前往故事详情"
      })
    }
  }
});