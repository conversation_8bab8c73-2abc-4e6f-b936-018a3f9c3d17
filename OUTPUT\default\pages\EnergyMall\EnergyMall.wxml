<view class="EnergyMall">
    <view class="EnergyMall_top">
        <view class="EnergyMall_top_box">
            <view class="EnergyMall_top_box_seachIcon">
                <image mode="" src="{{img}}newVersion/013.png"></image>
            </view>
            <input maxlength="50" value="{{searchKey}}" placeholder="请输入搜索关键词" placeholderClass="placeholder" type="text"></input>
            <view bindtap="seachList" class="EnergyMall_top_box_seach">搜索</view>
        </view>
    </view>
    <view class="EnergyMall_banner">
        <view class="EnergyMall_banner_img">
            <image mode="" src="{{img}}newVersion/014.png"></image>
        </view>
    </view>
    <view class="EnergyMall_seach">
        <view class="EnergyMall_seach_items">
            <view bindtap="changeTab" class="EnergyMall_seach_item" data-index="{{index}}" data-item="{{item}}" wx:for="{{tabList}}" wx:key="index">
                <view style="color:{{tabActive==index?'#343434':'#B4B4B4'}}">{{item.groupName}}</view>
                <view class="OrderList_top_item_x" wx:if="{{tabActive==index}}"></view>
            </view>
        </view>
        <view class="EnergyMall_seach_energyitems">
            <view bindtap="changeEnergy" class="{{energyActive==index?'EnergyMall_seach_energyitemAct':'EnergyMall_seach_energyitem'}}" data-index="{{index}}" data-item="{{item}}" wx:for="{{energyList}}" wx:key="index">{{item.lookValue}}</view>
        </view>
    </view>
    <view class="EnergyMall_list">
        <view bindtap="gotoGoodsInfo" class="EnergyMall_list_item" data-item="{{item}}" wx:for="{{giftList}}" wx:key="index">
            <view class="EnergyMall_list_item_top">
                <image mode="" src="{{item.giftImg}}"></image>
                <view class="EnergyMall_list_item_radio">
                    <view class="EnergyMall_list_item_radio_num">{{item.giftExchangeScore}}</view>
                    <view class="EnergyMall_list_item_radio_text">兑换能量</view>
                </view>
            </view>
            <view class="EnergyMall_list_item_bot2">
                <view class="EnergyMall_list_item_bot2_r">{{item.giftName}}</view>
            </view>
        </view>
    </view>
    <footer class="footerMall"></footer>
    <view class="footHeight"></view>
    <view class="tabbarProps">
        <tabbar actice="mall"></tabbar>
    </view>
</view>
