<view bind:tap="onClick" class="custom-class {{utils.bem( 'cell',[ size,{center:center,required:required,borderless:!border,clickable:isLink||clickable} ] )}}" hoverClass="van-cell--hover hover-class" hoverStayTime="70" style="{{customStyle}}">
    <van-icon class="van-cell__left-icon-wrap" customClass="van-cell__left-icon" name="{{icon}}" wx:if="{{icon}}"></van-icon>
    <slot name="icon" wx:else></slot>
    <view class="van-cell__title title-class" style="{{computed.titleStyle( {titleWidth:titleWidth,titleStyle:titleStyle} )}}">
        <block wx:if="{{title}}">{{title}}</block>
        <slot name="title" wx:else></slot>
        <view class="van-cell__label label-class" wx:if="{{label||useLabelSlot}}">
            <slot name="label" wx:if="{{useLabelSlot}}"></slot>
            <block wx:elif="{{label}}">{{label}}</block>
        </view>
    </view>
    <view class="van-cell__value value-class">
        <block wx:if="{{value||value===0}}">{{value}}</block>
        <slot wx:else></slot>
    </view>
    <van-icon class="van-cell__right-icon-wrap right-icon-class" customClass="van-cell__right-icon" name="{{arrowDirection?'arrow'+'-'+arrowDirection:'arrow'}}" wx:if="{{isLink}}"></van-icon>
    <slot name="right-icon" wx:else></slot>
    <slot name="extra"></slot>
</view>

<wxs module="utils" src="..\wxs\utils.wxs"/>
<wxs module="computed" src="index.wxs"/>