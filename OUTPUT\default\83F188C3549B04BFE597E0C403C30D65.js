Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.getUser = void 0;
var e = n(require("87624F60549B04BFE10427674BE30D65.js")),
  t = n(require("B6135D02549B04BFD0753505DD930D65.js")),
  r = require("A4000F75549B04BFC2666772D6B30D65.js");

function n(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
exports.getUser = function() {
  return new Promise((function(n, o) {
    (0, r.getUserInfo)({
      openId: e.default.data.openid,
      enterpriseNo: t.default.enterpriseNo
    }).then((function(t) {
      e.default.data.userInfo = t.data, n(t)
    }))
  }))
};