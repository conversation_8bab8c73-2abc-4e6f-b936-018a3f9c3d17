<view class="SubmitOrder">
    <view class="SubmitOrder_top" wx:if="{{giftType==3}}">
        <view class="SubmitOrder_top_t">
            <view class="SubmitOrder_top_t_icon">
                <image mode="" src="{{img}}newVersion/016.png"></image>
            </view>
            <view class="SubmitOrder_top_t_text">收货地址</view>
        </view>
        <view bindtap="gotoAddressList" class="SubmitOrder_top_b" wx:if="{{!defaultAddress.id}}">
            <view class="SubmitOrder_top_b_text">新建收货地址</view>
            <view class="SubmitOrder_top_b_jian">
                <image mode="" src="{{img}}leftJian.png"></image>
            </view>
        </view>
        <view bindtap="gotoAddressList" class="SubmitOrder_top_b" wx:else>
            <view class="SubmitOrder_top_b_left">
                <view class="SubmitOrder_top_b_left_top">
                    <view class="SubmitOrder_top_b_left_top_l">
                        <view class="top_l_name">{{defaultAddress.name}}</view>
                        <view class="top_l_icon" wx:if="{{defaultAddress.defaultFlag==1}}">默认</view>
                    </view>
                    <view class="SubmitOrder_top_b_left_top_r">{{defaultAddress.mobile}}</view>
                </view>
                <view class="SubmitOrder_top_b_left_bot">{{defaultAddress.province}}{{defaultAddress.city}}{{defaultAddress.area}}{{defaultAddress.address}}</view>
            </view>
            <view class="SubmitOrder_top_b_jian">
                <image mode="" src="{{img}}leftJian.png"></image>
            </view>
        </view>
    </view>
    <view class="SubmitOrder_xian" wx:if="{{giftType==3}}">
        <image mode="" src="{{img}}newVersion/017.png"></image>
    </view>
    <view class="SubmitOrder_goods">
        <view class="SubmitOrder_goods_box">
            <view class="SubmitOrder_goods_top">
                <view class="SubmitOrder_goods_top_l">
                    <image mode="" src="{{goodsInfoMsg.giftImg}}"></image>
                </view>
                <view class="SubmitOrder_goods_top_r">
                    <view class="SubmitOrder_goods_top_r_title">{{goodsInfoMsg.giftName}}</view>
                    <view class="SubmitOrder_goods_top_r_num">{{goodsInfoMsg.giftExchangeScore}}<text style="font-size:24rpx;">能量</text>
                    </view>
                </view>
            </view>
            <view class="SubmitOrder_goods_jishu" wx:if="{{giftType==3}}">
                <view class="SubmitOrder_goods_jishu_box">
                    <van-stepper integer bind:change="onChange" value="{{goodsNumber}}"></van-stepper>
                </view>
            </view>
            <view class="SubmitOrder_goods_text" wx:if="{{giftType==3}}">（兑换成功后，7个工作日发货）</view>
            <view style="height:40rpx;" wx:if="{{giftType==1}}"></view>
        </view>
        <view class="SubmitOrder_goods_bot">
            <view>商品能量：<text style="font-size:32rpx;">{{goodsInfoMsg.giftExchangeScore}}</text>
            </view>
            <view>总能量：<text style="font-size:32rpx;color:#000;font-weight:bold;">{{goodsInfoMsg.giftExchangeScore*goodsNumber}}</text>
            </view>
        </view>
    </view>
    <view class="SubmitOrder_bot">
        <footer></footer>
        <view class="SubmitOrder_button">
            <view class="SubmitOrder_button_l" wx:if="{{giftType==3}}">
                <view class="SubmitOrder_button_hj">合计：<text style="font-size:38rpx;">{{goodsInfoMsg.giftExchangeScore*goodsNumber}}</text>能量</view>
                <view class="SubmitOrder_button_gj">共计{{goodsNumber}}件</view>
            </view>
            <view class="SubmitOrder_button_ls" wx:else>
                <view class="SubmitOrder_button_hj">合计：<text style="font-size:38rpx;">{{goodsInfoMsg.giftExchangeScore*goodsNumber}}</text>能量</view>
            </view>
            <view bindtap="gotoPay" class="SubmitOrder_button_r">支付订单</view>
        </view>
    </view>
    <prop bindcloseProp="closeProp" bindconfirmProp="confirmProp" energyNumber="{{energyNumber}}" propNum="{{propNum}}" wx:if="{{propState}}"></prop>
</view>
