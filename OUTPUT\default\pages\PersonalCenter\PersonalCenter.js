var e, t = (e = require("../../87624F60549B04BFE10427674BE30D65.js")) && e.__esModule ? e : {
    default: e
  },
  o = require("../../8F86AFB2549B04BFE9E0C7B593D30D65.js");
var r = getApp();
Page({
  data: {
    img: r.globalData.img,
    propState: !1,
    propNum: null,
    list: [{
      src: "orderIcon.png",
      name: "我的订单",
      url: "/pages/PersonalCenter/OrderList/OrderList"
    }, {
      src: "nlIcon.png",
      name: "能量明细",
      url: "/pages/PersonalCenter/EnergyDetail/EnergyDetail"
    }, {
      src: "addressIcon.png",
      name: "我的地址",
      url: "/pages/PersonalCenter/MyAddress/MyAddress"
    }, {
      src: "priceIcon.png",
      name: "奖励记录",
      url: "/pages/PersonalCenter/RewardRecord/RewardRecord"
    }, {
      src: "ContactUs.png",
      name: "联系我们",
      isProp: !0
    }, {
      src: "icon_w.png",
      name: "规则说明",
      url: "/pages/PersonalCenter/RuleText/RuleText"
    }],
    phoneEmpowerState: !0,
    userInfo: {},
    phone: ""
  },
  hide_phone: function(e, t, o) {
    for (var r = e.length - t - o, n = "", a = 0; a < r; a++) n += "*";
    return e.substring(0, t) + n + e.substring(e.length - o)
  },
  closeProp: function() {
    this.setData({
      propState: !1
    })
  },
  gotoEmpower: function() {
    wx.navigateTo({
      url: "/pages/empower/empower"
    })
  },
  gotoSetUp: function() {
    this.data.phoneEmpowerState ? wx.navigateTo({
      url: "/pages/PersonalCenter/SetUp/SetUp"
    }) : this.setData({
      propState: !0,
      propNum: 6
    })
  },
  gotoMpUrl: function(e) {
    console.log(e.currentTarget.dataset.item);
    var t = e.currentTarget.dataset.item;
    this.data.phoneEmpowerState ? t.isProp ? this.setData({
      propState: !0,
      propNum: 30
    }) : wx.navigateTo({
      url: t.url
    }) : this.setData({
      propState: !0,
      propNum: 6
    })
  },
  onLoad: function(e) {},
  onReady: function() {},
  onShow: function() {
    console.log("wb_mxProp", (0, o.get)("wb_mxProp")), console.log("wb_mxPropState", (0, o.get)("wb_mxPropState")), (0, o.get)("wb_mxProp") ? (0, o.get)("wb_mxPropState") && (0, o.set)("wb_mxProp", 2) : (0, o.set)("wb_mxProp", 1), this.setData({
      userInfo: t.default.data.userInfo
    }), "" == this.data.userInfo.mobile ? this.setData({
      phoneEmpowerState: !1
    }) : this.setData({
      phoneEmpowerState: !0,
      phone: this.hide_phone(this.data.userInfo.mobile, 3, 4)
    }), console.log("用户信息", this.data.userInfo)
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {}
});