var e = "_deadtime";
module.exports = {
  set: function(t, r, a) {
    wx.setStorageSync(t, r);
    var n = parseInt(a);
    if (n > 0) {
      var o = Date.parse(new Date);
      o = o / 1e3 + n, wx.setStorageSync(t + e, o + "")
    } else wx.removeStorageSync(t + e)
  },
  get: function(t, r) {
    var a = parseInt(wx.getStorageSync(t + e));
    if (a && parseInt(a) < Date.parse(new Date) / 1e3) return r || (wx.removeStorage({
      key: t
    }), void wx.removeStorage({
      key: t + e
    }));
    var n = wx.getStorageSync(t);
    return n || r
  }
};