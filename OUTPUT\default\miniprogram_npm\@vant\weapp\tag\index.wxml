<view class="custom-class {{utils.bem( 'tag',[ type,size,{mark:mark,plain:plain,round:round} ] )}}" style="{{computed.rootStyle( {plain:plain,color:color,textColor:textColor} )}}">
    <slot></slot>
    <van-icon bind:click="onClose" customClass="van-tag__close" name="cross" wx:if="{{closeable}}"></van-icon>
</view>

<wxs module="utils" src="..\wxs\utils.wxs"/>
<wxs module="computed" src="index.wxs"/>