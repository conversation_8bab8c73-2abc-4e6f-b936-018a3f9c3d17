<view class="container fadeIn" hoverClass="none" hoverStopPropagation="false">
    <custom-header :isBack="{{true}}" title="PEANUTS"></custom-header>
    <view class="scroll-view {{screenInfo}}" hoverClass="none" hoverStopPropagation="false" style="top:{{statusHeaderBarHeight}}px">
        <image binderror="" bindload="" class="bg" lazyLoad="true" src="{{imgUrl}}75/record/bg.jpg?v={{imgVersion}}"></image>
        <scroll-view scrollY class="scroll-view-box">
            <view catch:tap="handleOpen" class="collectionBox" data-info="{{item}}" wx:for="{{listData}}" wx:key="index">
                <image binderror="" bindload="" class="collection-bg" lazyLoad="true" src="{{imgUrl}}75/collection/bg.png?v={{imgVersion}}"></image>
                <view class="cBox">
                    <image binderror="" bindload="" class="c-bg" lazyLoad="true" mode="aspectFill" src="{{item.img}}"></image>
                </view>
                <view class="iBox SourceHanSerifCN-Bold">{{item.name}}</view>
                <view class="infoBox">
                    <view class="iPriceBox SourceHanSerifCN-Bold">¥ {{item.price}}</view>
                    <image binderror="" bindload="" class="buyIcon" lazyLoad="true" src="{{imgUrl}}75/collection/buy.png?v={{imgVersion}}"></image>
                </view>
            </view>
        </scroll-view>
    </view>
</view>
