var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/asyncToGenerator"),
  a = require("../../1AA24145549B04BF7CC42942C3240D65.js"),
  n = require("../../6F218526549B04BF0947ED2133340D65.js"),
  o = require("../../A4000F75549B04BFC2666772D6B30D65.js"),
  r = s(require("../../B6135D02549B04BFD0753505DD930D65.js")),
  i = s(require("../../87624F60549B04BFE10427674BE30D65.js"));

function s(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
var d = getApp();
Page({
  data: {
    propState: !1,
    propNum: 1,
    img: d.globalData.img,
    codeUrl: "",
    codeValue: "",
    gpsInfo: {},
    ipMsg: {},
    isGPS: !1,
    labelOrderId: "",
    templateId: "",
    bakcgroundImg: "",
    welComeTitle: "欢迎使用",
    ruleText: "",
    queryResults: null,
    queryState: null,
    firstData: "",
    shopName: "",
    shopData: "",
    userInfo: null,
    activityId: "",
    priceMsg: {},
    energyNumber: "",
    isShowActButton: 0,
    isShowRule: 0,
    isOldCode: !1,
    isOldCodeState: !1,
    areaCodeInfo: {},
    areaCodeState: !1
  },
  dictionaryItems: function() {
    return new Promise((function(e, t) {
      (0, o.dictionaryItems)({
        code: "interaction-address"
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  showRule: function() {
    this.data.isOldCode ? this.setData({
      propState: !0,
      propNum: 100
    }) : this.setData({
      propState: !0,
      propNum: 0
    })
  },
  closeProp: function() {
    this.setData({
      propState: !1
    })
  },
  showGZH: function() {
    this.setData({
      propNum: 2
    })
  },
  miniappLogin: function(e) {
    return new Promise((function(t, a) {
      (0, o.miniappLogin)({
        authorizerAppid: d.globalData.appId,
        jsCode: e
      }).then((function(e) {
        t(e)
      }))
    }))
  },
  getToken: function(e) {
    return new Promise((function(t, a) {
      (0, o.wechatOpenid)({
        serviceSign: e.serviceSign,
        openid: e.openid,
        appId: d.globalData.appId,
        appType: 2
      }).then((function(e) {
        i.default.data.registerUserInfo = e.data, t(e)
      }))
    }))
  },
  getUser: function() {
    var e = this;
    return new Promise((function(t, a) {
      (0, o.getUserInfo)({
        openId: i.default.data.openid,
        enterpriseNo: r.default.enterpriseNo
      }).then((function(a) {
        200 == a.code && (e.setData({
          userInfo: a.data
        }), i.default.data.userInfo = a.data), t(a)
      }))
    }))
  },
  getConvertGps: function() {
    var e = this;
    return new Promise((function(t, a) {
      (0, o.convertGps)({
        latitude: e.data.gpsInfo.latitude,
        longitude: e.data.gpsInfo.longitude
      }).then((function(e) {
        t(e)
      }))
    }))
  },
  getUserData: function() {
    var a = this;
    return t(e().mark((function t() {
      var o, r, s, d, c, u, p, l;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.next = 2, (0, n.getUserGPS)(!1);
          case 2:
            if (o = e.sent, console.log("gpsInfo——地址信息", o), !o.latitude || !o.longitude) {
              e.next = 13;
              break
            }
            return a.setData({
              isGPS: !0,
              gpsInfo: o
            }), e.next = 8, a.getConvertGps();
          case 8:
            200 == (r = e.sent).code ? a.setData({
              areaCodeInfo: r.data.result,
              areaCodeState: !0
            }) : a.setData({
              areaCodeState: !1
            }), console.log("areaCodeInfo__gps解析出来的code", r), e.next = 18;
            break;
          case 13:
            return e.next = 15, a.getIp();
          case 15:
            s = e.sent, a.setData({
              isGPS: !1,
              ipMsg: s,
              areaCodeState: !1
            }), console.log("ipMsg", s);
          case 18:
            return (0, n.loadingOpen)(), e.next = 21, (0, n.getCode)();
          case 21:
            return d = e.sent, console.log(d), e.next = 25, a.miniappLogin(d);
          case 25:
            return c = e.sent, console.log("获取用户openid", c), i.default.data.openid = c.data.openid, e.next = 30, a.getToken(c.data);
          case 30:
            return u = e.sent, console.log("获取用户token", u), i.default.data.token = u.data.token, e.next = 35, a.getUser();
          case 35:
            if (p = e.sent, console.log("获取用户信息", p), -1 == a.data.codeUrl.indexOf("http://tmt1.cc/")) {
              e.next = 43;
              break
            }
            return console.log("扫描的是旧码"), (0, n.loadingClose)(), 11 == a.data.codeUrl.replace("http://tmt1.cc/", "").length ? a.setData({
              isOldCode: !0,
              isOldCodeState: !0
            }) : a.setData({
              isOldCode: !0,
              isOldCodeState: !1
            }), e.abrupt("return");
          case 43:
            return a.setData({
              isOldCode: !1
            }), e.next = 46, a.getAnti();
          case 46:
            return l = e.sent, e.next = 49, a.getTemplate();
          case 49:
            if ("300103" != l.data.replyType && "300104" != l.data.replyType && "300105" != l.data.replyType && "300106" != l.data.replyType || a.data.labelOrderId && a.getOrderStatus(), !a.data.templateId) {
              e.next = 53;
              break
            }
            return e.next = 53, a.getComponentConfig();
          case 53:
            (0, n.loadingClose)();
          case 54:
          case "end":
            return e.stop()
        }
      }), t)
    })))()
  },
  getPhone: function(e) {
    return console.log("code", e), new Promise((function(t, a) {
      (0, o.miniappGetPhoneNumber)({
        appid: d.globalData.appId,
        code: e
      }).then((function(e) {
        t(e)
      }))
    }))
  },
  gxPhone: function(e) {
    return new Promise((function(t, a) {
      (0, o.fullInfo)({
        mobile: e
      }).then((function(e) {
        t(e)
      }))
    }))
  },
  getPhoneNumber: function(a) {
    var o = this;
    return t(e().mark((function t() {
      var r;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            if (console.log("e", a), "getPhoneNumber:ok" != a.detail.errMsg) {
              e.next = 13;
              break
            }
            return e.next = 4, o.getPhone(a.detail.code);
          case 4:
            if (r = e.sent, console.log("phoneMsg", r), 200 == r.code) {
              e.next = 8;
              break
            }
            return e.abrupt("return", (0, n.toastModel)(r.message));
          case 8:
            return e.next = 10, o.gxPhone(r.data.phoneNumber);
          case 10:
            o.gotoActivities(), e.next = 14;
            break;
          case 13:
            o.gotoActivities();
          case 14:
          case "end":
            return e.stop()
        }
      }), t)
    })))()
  },
  gotoActivities: function() {
    var a = this;
    return t(e().mark((function r() {
      return e().wrap((function(r) {
        for (;;) switch (r.prev = r.next) {
          case 0:
            return r.next = 2, a.getUser();
          case 2:
            a.data.isOldCode ? (0, o.participationNine)({
              weChatOpenId: i.default.data.openid,
              digitCode: a.data.codeUrl + "000000",
              activityId: d.globalData.activityId,
              activityType: 11,
              isNewUser: i.default.data.registerUserInfo.isNewUser,
              gps: a.data.isGPS ? "".concat(a.data.gpsInfo.latitude, ",").concat(a.data.gpsInfo.longitude) : "",
              address: a.data.areaCodeState ? a.data.areaCodeInfo.formattedAddress : "",
              areaCode: a.data.areaCodeState ? a.data.areaCodeInfo.addressComponent.adcode : "",
              gpsType: 3
            }).then((function(e) {
              200 == e.code ? a.setData({
                propState: !0,
                propNum: 1,
                priceMsg: e.data,
                energyNumber: e.data[0].winData[0].giftUnitScore
              }) : 22354046 == e.code ? (0, n.toastModel)("数码已经参与活动，请勿重复参与") : (0, n.toastModel)(e.message)
            })) : (0, o.participation)({
              weChatOpenId: i.default.data.openid,
              digitCode: a.data.codeValue,
              activityId: a.data.activityId,
              activityType: a.data.activityType,
              lang: "zh-CN",
              gps: a.data.isGPS ? "".concat(a.data.gpsInfo.latitude, ",").concat(a.data.gpsInfo.longitude) : "",
              address: a.data.areaCodeState ? a.data.areaCodeInfo.formattedAddress : "",
              areaCode: a.data.areaCodeState ? a.data.areaCodeInfo.addressComponent.adcode : "",
              gpsType: 3
            }).then(function() {
              var n = t(e().mark((function t(n) {
                return e().wrap((function(e) {
                  for (;;) switch (e.prev = e.next) {
                    case 0:
                      a.setData({
                        propState: !0,
                        propNum: 1,
                        priceMsg: n.data,
                        energyNumber: n.data[0].winData[0].giftUnitScore
                      });
                    case 1:
                    case "end":
                      return e.stop()
                  }
                }), t)
              })));
              return function(e) {
                return n.apply(this, arguments)
              }
            }());
          case 3:
          case "end":
            return r.stop()
        }
      }), r)
    })))()
  },
  onLoad: function(e) {
    console.log("options", e), this.setData({
      codeUrl: decodeURIComponent(e.q)
    }), console.log("codeUrl", this.data.codeUrl)
  },
  getIp: function() {
    return new Promise((function(e, t) {
      wx.request({
        url: "https://h5api.supercarrier8.com/custom/getClientInfo",
        method: "post",
        success: function(t) {
          console.log("res", t), e(t)
        },
        fail: function(t) {
          e(res)
        }
      })
    }))
  },
  onShow: function() {
    var a, n, o = this;
    wx.getPrivacySetting ? wx.getPrivacySetting({
      success: (n = t(e().mark((function t(a) {
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              if (!a.needAuthorization) {
                e.next = 2;
                break
              }
              return e.abrupt("return", wx.navigateTo({
                url: "/pages/privacyContract/privacyContract"
              }));
            case 2:
              o.getUserData();
            case 3:
            case "end":
              return e.stop()
          }
        }), t)
      }))), function(e) {
        return n.apply(this, arguments)
      }),
      fail: (a = t(e().mark((function t(a) {
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              o.getUserData();
            case 1:
            case "end":
              return e.stop()
          }
        }), t)
      }))), function(e) {
        return a.apply(this, arguments)
      })
    }) : this.getUserData()
  },
  getTemplate: function() {
    var e = this;
    return new Promise((function(t, a) {
      (0, o.getTemplate)({
        activityId: "",
        activityType: "",
        channel: "1",
        digitCode: e.data.codeUrl,
        enterpriseNo: d.globalData.enterpriseNo,
        lang: "zh-CN",
        passParams: {
          gps: e.data.isGPS ? "".concat(e.data.gpsInfo.longitude, ",").concat(e.data.gpsInfo.latitude) : e.data.ipMsg.data.ip,
          gpsType: "2"
        },
        platform: "2"
      }).then((function(a) {
        e.setData({
          labelOrderId: a.data.codeBase.labelOrderId,
          templateId: a.data.h5Template.templateId,
          codeValue: a.data.codeBase.digitCode,
          activityId: a.data.h5Template.activityId,
          activityType: a.data.h5Template.activityType
        }), t(a)
      }))
    }))
  },
  getComponentConfig: function() {
    var e = this;
    return new Promise((function(t, a) {
      (0, o.getComponentConfig)({
        enterpriseNo: d.globalData.enterpriseNo,
        templateId: e.data.templateId,
        businessType: "",
        preview: "",
        previewVerNo: ""
      }).then((function(a) {
        console.log("res", a);
        var n = a.data.config.pages[0].components[0].props,
          o = n.find((function(e) {
            return "background" == e.propKey
          }));
        console.log("背景图信息", o);
        var r = n.find((function(e) {
          return "welcome" == e.propKey
        }));
        console.log("欢迎标题数据", r);
        var i = n.find((function(e) {
          return "actRuleContent" == e.propKey
        }));
        console.log("活动规则数据", i);
        var s = n.find((function(e) {
          return "isShowActButton" == e.propKey
        }));
        console.log("是否展示参与活动", s);
        var d = n.find((function(e) {
          return "isShowRule" == e.propKey
        }));
        console.log("是否展示活动规则", d), e.setData({
          bakcgroundImg: decodeURIComponent(o.propValue),
          welComeTitle: decodeURIComponent(r.propValue),
          ruleText: decodeURIComponent(i.propValue),
          isShowActButton: s.propValue,
          isShowRule: d.propValue
        }), e.data.ruleText = e.data.ruleText.replace(/\<img/gi, '<img style="width:100%;height:auto;display:block;"'), e.data.ruleText = e.data.ruleText.replace("px", "rpx"), e.setData({
          ruleText: e.data.ruleText
        }), t(a)
      }))
    }))
  },
  getAnti: function() {
    var n = this;
    return new Promise((function(r, i) {
      (0, o.getAnti)({
        channelType: "V",
        code: n.data.codeUrl,
        enterpriseNo: d.globalData.enterpriseNo,
        requestId: (0, a.newGuid)(),
        languageType: "zh-CN",
        fromInfo: n.data.isGPS ? "".concat(n.data.gpsInfo.latitude, ",").concat(n.data.gpsInfo.longitude) : n.data.ipMsg.data.ip,
        toInfo: n.data.codeUrl
      }).then(function() {
        var a = t(e().mark((function t(a) {
          var o;
          return e().wrap((function(e) {
            for (;;) switch (e.prev = e.next) {
              case 0:
                200 == a.code && (o = null, a.data.content ? (a.data.content = a.data.content.replace(/\<img/gi, '<img style="width:100%;height:auto;display:block;"'), a.data.content = a.data.content.replace("px", "rpx"), o = a.data.content) : (a.data.message = a.data.message.replace(/\<img/gi, '<img style="width:100%;height:auto;display:block;"'), a.data.message = a.data.message.replace("px", "rpx"), o = a.data.message), n.setData({
                  queryResults: o,
                  firstData: a.data.date
                }), a.data.result && 1 == a.data.num ? n.setData({
                  queryState: !0
                }) : n.setData({
                  queryState: !1
                })), r(a);
              case 2:
              case "end":
                return e.stop()
            }
          }), t)
        })));
        return function(e) {
          return a.apply(this, arguments)
        }
      }())
    }))
  },
  getOrderStatus: function() {
    var e = this;
    return new Promise((function(t, a) {
      (0, o.getOrderStatus)({
        id: e.data.labelOrderId
      }).then((function(a) {
        var n, o;
        200 == a.code && e.setData({
          shopName: null === (n = a.data) || void 0 === n ? void 0 : n.licenseeEn,
          shopData: null === (o = a.data) || void 0 === o ? void 0 : o.endDate
        });
        t(a)
      }))
    }))
  }
});