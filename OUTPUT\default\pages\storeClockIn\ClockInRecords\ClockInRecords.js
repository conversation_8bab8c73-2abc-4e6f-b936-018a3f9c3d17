var t, e = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  a = require("../../../9F0F7777549B04BFF9691F70EED30D65.js"),
  o = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  n = (t = require("../../../87624F60549B04BFE10427674BE30D65.js")) && t.__esModule ? t : {
    default: t
  };
getApp();
Page({
  data: {
    nowYear: "",
    topList: [{
      name: "本周",
      index: 0
    }, {
      name: "本月",
      index: 1
    }, {
      name: "本年",
      index: 2
    }],
    list: [],
    topIndex: 0,
    clockTimeEnd: (0, a.getWeekEndDate)(),
    clockTimeStart: (0, a.getWeekStartDate)(),
    pageIndex: 1,
    pageSize: 20
  },
  changeTopTab: function(t) {
    var e = t.currentTarget.dataset.item;
    console.log("index", e), this.setData({
      topIndex: e.index
    }), 0 == this.data.topIndex && this.setData({
      clockTimeStart: (0, a.getWeekStartDate)(),
      clockTimeEnd: (0, a.getWeekEndDate)()
    }), 1 == this.data.topIndex && this.setData({
      clockTimeStart: (0, a.getMonthStartDate)(),
      clockTimeEnd: (0, a.getMonthEndDate)()
    }), 2 == this.data.topIndex && this.setData({
      clockTimeStart: this.data.nowYear + "-01-01",
      clockTimeEnd: this.data.nowYear + "-12-31"
    }), this.setData({
      pageIndex: 1,
      list: []
    }), this.getList(), console.log(e.name), console.log("开始时间", this.data.clockTimeStart), console.log("结束时间", this.data.clockTimeEnd)
  },
  onLoad: function(t) {},
  onReady: function() {},
  onShow: function() {
    var t = (new Date).getYear();
    t += t < 2e3 ? 1900 : 0, console.log("当前年", t), console.log("fetchData", n.default.data.userInfo.mobile), this.setData({
      nowYear: t
    }), console.log("本周开始", (0, a.getWeekStartDate)()), console.log("本周结束", (0, a.getWeekEndDate)()), console.log("本月开始", (0, a.getMonthStartDate)()), console.log("本月结束", (0, a.getMonthEndDate)()), this.getList()
  },
  getList: function() {
    var t = this;
    (0, o.loadingOpen)(), (0, e.recordPage)({
      clockTimeEnd: this.data.clockTimeEnd,
      clockTimeStart: this.data.clockTimeStart,
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize,
      telephone: n.default.data.userInfo.mobile
    }).then((function(e) {
      (0, o.loadingClose)(), t.setData({
        pageIndex: t.data.pageIndex += 1,
        totalPage: Math.ceil(e.data.total / t.data.pageSize),
        list: t.data.list.concat(e.data.list)
      })
    }))
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {
    this.data.pageIndex <= this.data.totalPage ? this.getList() : (0, o.toastModel)("暂无更多数据了~")
  },
  onShareAppMessage: function() {}
});