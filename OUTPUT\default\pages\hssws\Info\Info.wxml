<view class="GoodsInfo">
    <view class="GoodsInfo_top">
        <view class="GoodsInfo_top_img">
            <view class="GoodsInfo_top_img_i">
                <image mode="" src="{{activityInfo.activityImg}}"></image>
            </view>
            <view class="EnergyMall_list_item_radio">
                <view class="EnergyMall_list_item_radio_num">{{activityInfo.consumeIntegral}}</view>
                <view class="EnergyMall_list_item_radio_text">申请能量</view>
            </view>
        </view>
        <view class="GoodsInfo_top_bot">
            <view class="GoodsInfo_top_bot_text">{{activityInfo.activityName}}</view>
        </view>
    </view>
    <view class="GoodsInfo_info">
        <view class="GoodsInfo_tz" wx:if="{{activityInfo.lotteryFlag!=1}}">
            <view class="GoodsInfo_tz_icon">
                <image mode="" src="{{img}}newVersion/072.png"></image>
            </view>
            <view class="GoodsInfo_tz_text">中奖名单将于 {{activityInfo.lotteryOpenTime}} 公布</view>
        </view>
    </view>
    <view class="GoodsInfo_gb" wx:if="{{activityInfo.lotteryFlag==1}}">
        <view class="GoodsInfo_gb_box">
            <view wx:for="{{activityInfo.applyList}}" wx:key="index">{{item}}</view>
        </view>
    </view>
    <view class="GoodsInfo_title">商品详情</view>
    <view class="GoodsInfo_imgs">
        <rich-text class="cnt" nodes="{{activityInfo.activityInfo}}"></rich-text>
    </view>
    <view class="GoodsInfo_imgsBot">
        <image mode="" src="https://dm-assets.supercarrier8.com/wobei/sws/infoImg.png"></image>
    </view>
    <view class="GoodsInfo_buttons" wx:if="{{activityInfo.buttonFlag==2}}">申请已结束</view>
    <block wx:else>
        <view bindtap="gotoSq" class="GoodsInfo_button" wx:if="{{activityInfo.buttonFlag==0}}">点击参与申请</view>
        <view class="GoodsInfo_buttons" wx:else>申请已完成</view>
    </block>
    <prop bindcloseProp="closeProp" bindconfirmProp="confirmProp" propNum="{{propNum}}" timeMsg="{{timeMsg}}" wx:if="{{propState}}"></prop>
</view>
