.addReceiving {
    background: #f6f6f6;
    height: 100vh;
    width: 100%
}

.addReceiving_top {
    background: #fff;
    height: 687rpx
}

.addReceiving_bot {
    bottom: 0;
    left: 0;
    position: fixed;
    width: 100%
}

.addReceiving_top_item {
    box-sizing: border-box;
    height: 106rpx;
    padding-top: 23rpx;
    width: 100%
}

.addReceiving_top_items {
    height: 83rpx;
    margin: 0 auto;
    width: 698rpx
}

.addReceiving_top_item_l {
    color: #999;
    text-align: center;
    width: 174rpx
}

.addReceiving_top_item_l,.addReceiving_top_item_r {
    float: left;
    font-family: HiraginoSansGB;
    font-size: 28rpx;
    font-weight: 400;
    height: 83rpx;
    line-height: 83rpx
}

.addReceiving_top_item_r {
    border-bottom: 1rpx solid #ccc;
    box-sizing: border-box;
    color: #333;
    padding-left: 14rpx;
    position: relative;
    width: 524rpx
}

.addReceiving_top_item_r_icon {
    height: 28rpx;
    right: 84rpx;
    width: 14rpx
}

.addReceiving_top_item_r_icon,.addReceiving_top_item_r_x {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.addReceiving_top_item_r_x {
    background: #9d9d9d;
    height: 53rpx;
    right: 63rpx;
    width: 1rpx
}

.addReceiving_top_item_r_addIcon {
    height: 41rpx;
    position: absolute;
    right: 11rpx;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 35rpx
}

.inputPhones {
    height: 83rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.inputPhones,.placeholder {
    color: #333;
    font-family: HiraginoSansGB;
    font-size: 28rpx;
    font-weight: 400
}

.addReceiving_top_moren {
    -webkit-align-items: center;
    align-items: center;
    box-sizing: border-box;
    display: -webkit-flex;
    display: flex;
    height: 152rpx;
    overflow: hidden;
    padding-left: 60rpx;
    width: 100%
}

.addReceiving_top_moren_L {
    height: 40rpx;
    width: 88rpx
}

.addReceiving_top_moren_r {
    color: #333;
    font-family: HiraginoSansGB;
    font-size: 28rpx;
    font-weight: 400;
    height: 50rpx;
    line-height: 50rpx;
    margin-left: 14rpx
}

.addReceiving_button {
    background: #000;
    color: #fff;
    font-family: Source Han Sans CN;
    font-size: 38rpx;
    font-weight: 700;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    width: 100%
}
