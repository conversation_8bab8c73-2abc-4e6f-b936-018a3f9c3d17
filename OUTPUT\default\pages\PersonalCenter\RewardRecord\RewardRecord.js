var t, e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  a = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  i = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  n = require("../../../A4000F75549B04BFC2666772D6B30D65.js"),
  o = (t = require("../../../87624F60549B04BFE10427674BE30D65.js")) && t.__esModule ? t : {
    default: t
  };
var r = getApp();
Page({
  data: {
    img: r.globalData.img,
    list: [],
    pageIndex: 1,
    pageSize: 10,
    totalPage: 0,
    propState: !1,
    propNum: 0,
    activityId: "",
    giftDetailInfo: {},
    priceInfo: {}
  },
  closeProp: function() {
    this.setData({
      propState: !1
    }), 16 == this.data.propNum && (console.log("关闭美图秀秀弹框"), this.setData({
      pageIndex: 1,
      list: [],
      totalPage: 0
    }), this.getList())
  },
  lookImg: function(t) {
    var e = t.currentTarget.dataset.item;
    console.log("item", e), 6 == e.giftSendType ? this.setData({
      priceInfo: e,
      propState: !0,
      propNum: 14
    }) : this.setData({
      priceInfo: e,
      propState: !0,
      propNum: 15
    })
  },
  submitUserInfo: function(t) {
    var e = this;
    console.log("领取实物数据", t), (0, i.loadingOpen)(), (0, n.receiveReward)({
      participationId: this.data.priceInfo.participationId,
      activityId: this.data.priceInfo.activityId,
      winId: this.data.priceInfo.winId,
      consigneeName: t.detail.userName,
      consigneeMobile: t.detail.userPhone,
      consigneeAddress: t.detail.consigneeAddress
    }).then((function(t) {
      (0, i.loadingClose)(), (0, i.toastModel)("提交成功"), e.setData({
        propState: !1,
        list: [],
        pageIndex: 1
      }), e.getList()
    }))
  },
  receiveGoodsBQ: function(t) {
    var e = this,
      a = t.currentTarget.dataset.item;
    console.log(a), (0, i.loadingOpen)(), (0, n.receiveLinkReward)({
      activityId: a.activityId,
      participationId: a.participationId,
      winId: a.winId
    }).then((function(t) {
      (0, i.loadingClose)(), 200 == t.code ? (e.setData({
        propState: !1,
        list: [],
        pageIndex: 1
      }), e.getList()) : (0, i.toastModel)(t.message)
    }))
  },
  receiveGoods: function(t) {
    var e = t.currentTarget.dataset.item;
    console.log(e), this.setData({
      propState: !0,
      propNum: 17,
      priceInfo: e
    })
  },
  getGiftInfo: function(t) {
    return new Promise((function(e, a) {
      (0, n.getGiftDetails)({
        id: t
      }).then((function(t) {
        e(t)
      }))
    }))
  },
  lookVideo: function(t) {
    var i = this;
    return a(e().mark((function a() {
      var n, o;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return n = t.currentTarget.dataset.item, console.log(n), e.next = 4, i.getGiftInfo(n.giftId);
          case 4:
            o = e.sent, console.log("giftDetailInfo", o), i.setData({
              propState: !0,
              propNum: 16,
              priceInfo: n,
              priceData: n,
              activityId: n.activityId,
              giftDetailInfo: o.data
            });
          case 7:
          case "end":
            return e.stop()
        }
      }), a)
    })))()
  },
  lookQuan: function(t) {
    var i = this;
    return a(e().mark((function a() {
      var n, o;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return n = t.currentTarget.dataset.item, console.log(n), e.next = 4, i.getGiftInfo(n.giftId);
          case 4:
            o = e.sent, console.log("giftDetailInfo", o), i.setData({
              propState: !0,
              propNum: 9,
              priceInfo: n,
              giftDetailInfo: o.data
            });
          case 7:
          case "end":
            return e.stop()
        }
      }), a)
    })))()
  },
  getList: function() {
    var t = this;
    (0, i.loadingOpen)(), (0, n.getRewardDetails)({
      uId: o.default.data.userInfo.id,
      pageIndex: this.data.pageIndex,
      pageSize: this.data.pageSize
    }).then((function(e) {
      (0, i.loadingClose)(), e.data.list.forEach((function(t) {
        t.giftNameNew = t.giftName.replace("积分", "能量")
      })), t.setData({
        pageIndex: t.data.pageIndex += 1,
        list: t.data.list.concat(e.data.list),
        totalPage: Math.ceil(e.data.total / t.data.pageSize)
      })
    }))
  },
  onLoad: function(t) {},
  onReady: function() {},
  onShow: function() {
    this.getList()
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {
    this.data.pageIndex <= this.data.totalPage ? this.getList() : (0, i.toastModel)("暂无更多数据了~")
  }
});