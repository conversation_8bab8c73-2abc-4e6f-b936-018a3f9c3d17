var e, n = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  a = require("../../../@babel/runtime/helpers/asyncToGenerator"),
  r = (e = require("../../../A5622344549B04BFC3044B435F450D65.js")) && e.__esModule ? e : {
    default: e
  },
  o = require("../../../95D1B746549B04BFF3B7DF41DA740D65.js"),
  t = require("../../../6D59C885549B04BF0B3FA082E5940D65.js"),
  i = require("../../../71D07D80549B04BF17B615870C540D65.js");
require("../../../03E40C94549B04BF6582649340450D65.js").mount(), (0, o.preloadFonts)(), Page({
  data: {
    statusHeaderBarHeight: t.statusHeaderBarHeight,
    imgUrl: t.imgUrl,
    imgVersion: t.imgVersion,
    screenInfo: (0, o.getScreenInfo)()
  },
  onLoad: function(e) {
    return a(n().mark((function a() {
      var u, l, p, c, d, s, g, _, f, m, h;
      return n().wrap((function(n) {
        for (;;) switch (n.prev = n.next) {
          case 0:
            if (!(0, o.isTokenExpired)()) {
              n.next = 13;
              break
            }
            return n.prev = 1, n.next = 4, (0, i.getLogin)();
          case 4:
            u = n.sent, console.log("loginInfo: ", u), n.next = 11;
            break;
          case 8:
            n.prev = 8, n.t0 = n.catch(1), wx.showToast({
              title: t.errorInfo.tokenError,
              icon: "none"
            });
          case 11:
            n.next = 13;
            break;
          case 13:
            if (!(0, o.getAccessToken)()) {
              n.next = 26;
              break
            }
            return n.prev = 14, n.next = 17, (0, i.getUserInfo)();
          case 17:
            l = n.sent, p = l.code, c = l.data, 200 === p && ((0, o.setStorageSync)("userInfo", c.userInfo), (0, o.setStorageSync)("openid", c.userInfo.openId), (0, o.setStorageSync)("currentJf", c.currentJf), r.default.init({
              projectId: "14",
              projectName: "史努比",
              api_key: "115e840655b87fc84d67406c7b4646c3fb143f4f02bba502eb5a945eb358",
              openid: null !== (d = c.userInfo.openId) && void 0 !== d ? d : "",
              unionid: "",
              ad_id: null !== (s = null == e || null === (g = e.query) || void 0 === g ? void 0 : g.ad_id) && void 0 !== s ? s : "",
              url_link_id: null !== (_ = null == e || null === (f = e.query) || void 0 === f ? void 0 : f.url_link_id) && void 0 !== _ ? _ : "",
              short_link_id: null !== (m = null == e || null === (h = e.query) || void 0 === h ? void 0 : h.short_link_id) && void 0 !== m ? m : "",
              debug: !0
            })), n.next = 26;
            break;
          case 23:
            n.prev = 23, n.t1 = n.catch(14), console.log("index userInfo error: ", n.t1);
          case 26:
            return n.next = 28, (0, o.checkVersionUpdate)();
          case 28:
            r.default.pageView({
              refer_page_type: "",
              refer_page_name: "",
              page_type: "首页",
              page_name: "首页",
              module_name: "",
              module_rank: "1",
              page_path: "cny/pages/home/<USER>"
            });
          case 29:
          case "end":
            return n.stop()
        }
      }), a, null, [
        [1, 8],
        [14, 23]
      ])
    })))()
  },
  handleOpenRule: function() {
    this.selectComponent("#c-75-rule").openMask(), r.default.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "首页",
      page_name: "首页",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/home/<USER>",
      button_name: "首页-活动规则"
    })
  },
  handleStoryCollection: function() {
    wx.getStorageSync("privacy_flag") ? wx.navigateTo({
      url: "/cny/pages/uploads/index"
    }) : this.selectComponent("#c-75-privacy").openMask(), r.default.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "首页",
      page_name: "首页",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/home/<USER>",
      button_name: "首页-故事征集"
    })
  },
  handleRank: function() {
    wx.navigateTo({
      url: "/cny/pages/rankList/index"
    }), r.default.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "首页",
      page_name: "首页",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/home/<USER>",
      button_name: "首页-排行榜"
    })
  },
  handleRecord: function() {
    wx.navigateTo({
      url: "/cny/pages/record/index"
    }), r.default.pageClickEvent({
      refer_page_type: "",
      refer_page_name: "",
      page_type: "首页",
      page_name: "首页",
      module_name: "",
      module_rank: "1",
      page_path: "cny/pages/home/<USER>",
      button_name: "首页-我的红包"
    })
  },
  handlePrivacy: function(e) {
    e.detail.flag && this.handleStoryCollection()
  },
  onReady: function() {},
  onShow: function() {},
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {
    return t.shareOptions
  }
});