var e, a = (e = require("../../../87624F60549B04BFE10427674BE30D65.js")) && e.__esModule ? e : {
    default: e
  },
  t = require("../../../6F218526549B04BF0947ED2133340D65.js"),
  s = require("../../../A4000F75549B04BFC2666772D6B30D65.js");
var o = getApp();
Page({
  data: {
    img: o.globalData.img,
    regionMsg: "请选择所在地区",
    checked: !0,
    isShowArea: !1,
    IsDefault: 1,
    province: "",
    provinceName: "",
    city: "",
    cityName: "",
    county: "",
    countyName: "",
    area: "",
    areaId: "",
    UserName: "",
    UserPhone: "",
    mailbox: "<EMAIL>",
    AreaAddress: "",
    PostID: "",
    optionsAdd: null,
    exitId: "",
    editState: !1
  },
  gotoChooseAdd: function() {
    wx.navigateTo({
      url: "/pages/PersonalCenter/IconAddress/IconAddress"
    })
  },
  onChange: function() {
    console.log("a"), this.setData({
      checked: !this.data.checked
    })
  },
  changeShowArea: function() {
    console.log("daaaaaa"), this.setData({
      isShowArea: !0
    })
  },
  onCityPicker: function(e) {
    var a = e.detail;
    console.log("onCityPicker", e.detail), console.log("测试数据", a[3] ? a[3].areaId : ""), this.setData({
      provinceName: a[0].areaName,
      province: a[0].areaId,
      cityName: a[1].areaName,
      city: a[1].areaId,
      countyName: a[2].areaName,
      county: a[2].areaId,
      area: "".concat(a[0].areaName, "、").concat(a[1].areaName, "、").concat(a[2].areaName),
      areaId: a[2].areaId
    }), console.log("数据", this.data.area)
  },
  setUserName: function(e) {
    this.setData({
      UserName: e.detail.value
    })
  },
  setUserPhone: function(e) {
    this.setData({
      UserPhone: e.detail.value
    })
  },
  setmailbox: function(e) {
    this.setData({
      mailbox: e.detail.value
    })
  },
  setAreaAddress: function(e) {
    this.setData({
      AreaAddress: e.detail.value
    })
  },
  setAddress: function() {
    "" != this.data.UserName ? "" != this.data.UserPhone ? 11 == this.data.UserPhone.length && /^0?1[0-9][0-9]\d{8}$/.test(this.data.UserPhone) ? "" != this.data.area ? "" != this.data.AreaAddress ? this.EditUserPost() : (0, t.toastModel)("请輸入详细地址") : (0, t.toastModel)("请选择所在地区") : (0, t.toastModel)("请输入正确的手机号") : (0, t.toastModel)("请填写联系方式") : (0, t.toastModel)("请填写收件人")
  },
  EditUserPost: function() {
    var e = {
      id: this.data.editState ? this.data.exitId : "",
      name: this.data.UserName,
      mobile: this.data.UserPhone,
      province: this.data.provinceName,
      city: this.data.cityName,
      area: this.data.countyName,
      address: this.data.AreaAddress,
      areaId: this.data.areaId,
      defaultFlag: this.data.checked ? "1" : 0
    };
    (0, s.saveUserAddress)(e).then((function(e) {
      200 == e.code ? wx.navigateBack() : (0, t.toastModel)(e.message)
    }))
  },
  delAddress: function() {
    if (this.data.editState) {
      var e = {
        AddressId: this.data.PostID,
        UserId: get("regData").Id
      };
      wx.showLoading({
        mask: !0
      }), DeleteAddress(e).then((function(e) {
        wx.hideLoading(), e.data.IsSuccess ? wx.navigateBack({
          url: "/pages/pointsmall/receiGoods/receiGoods"
        }) : wx.showToast({
          icon: "none",
          title: e.data.Message
        })
      }))
    } else wx.navigateBack({
      url: "/pages/pointsmall/receiGoods/receiGoods"
    })
  },
  onLoad: function(e) {
    if (console.log("options", e), e.addressInfo) {
      wx.setNavigationBarTitle({
        title: "编辑地址"
      });
      var a = JSON.parse(e.addressInfo);
      console.log("addressInfo", a), this.setData({
        editState: !0,
        exitId: a.id,
        UserName: a.name,
        UserPhone: a.mobile,
        provinceName: a.province,
        cityName: a.city,
        countyName: a.area,
        areaId: a.areaId,
        AreaAddress: a.address,
        checked: 1 == a.defaultFlag,
        area: "".concat(a.province, "、").concat(a.city, "、").concat(a.area)
      })
    } else wx.setNavigationBarTitle({
      title: "新增地址"
    }), this.setData({
      editState: !1,
      exitId: ""
    })
  },
  onReady: function() {},
  onShow: function() {
    if (a.default.data.addressInfo.isChooseAdd) {
      var e = a.default.data.addressInfo,
        t = e.province,
        s = e.city,
        o = e.district,
        d = (e.town, e.adcode),
        i = e.formattedAddress;
      this.setData({
        area: "".concat(t, "、").concat(s, "、").concat(o),
        provinceName: t,
        cityName: s,
        countyName: o,
        areaId: d,
        AreaAddress: i
      }), a.default.data.addressInfo.isChooseAdd = !1
    }
  },
  onHide: function() {},
  onUnload: function() {},
  onPullDownRefresh: function() {},
  onReachBottom: function() {},
  onShareAppMessage: function() {}
});