<view class="MyAddress">
    <view class="MyAddress_list" wx:if="{{list.length!=0}}">
        <view class="MyAddress_list_item" wx:for="{{list}}" wx:key="index">
            <view class="MyAddress_list_item_t">
                <view class="MyAddress_list_item_l">
                    <view class="MyAddress_list_item_l_t">
                        <view class="item_l_t_l">
                            <view class="item_l_t_l_name">{{item.name}}</view>
                            <view class="item_l_t_l_moren" wx:if="{{item.defaultFlag==1}}">默认</view>
                        </view>
                        <view class="item_l_t_r">{{item.mobile}}</view>
                    </view>
                    <view class="MyAddress_list_item_l_b">{{item.province}}{{item.city}}{{item.area}}{{item.address}}</view>
                </view>
                <view class="MyAddress_list_item_r" wx:if="{{isSubOrderJoin}}">
                    <view bindtap="chooseOrderAddress" class="MyAddress_list_item_r_choose" data-item="{{item}}">
                        <image mode="" src="{{img}}newVersion/015.png" wx:if="{{defaultAddress.id!=item.id}}"></image>
                        <image mode="" src="{{img}}newVersion/027.png" wx:else></image>
                    </view>
                </view>
            </view>
            <view class="MyAddress_list_item_b">
                <view class="MyAddress_list_item_b_cen">
                    <view bindtap="gotoExitAddress" class="tem_b_cen_editIcon" data-item="{{item}}">
                        <image mode="" src="{{img}}newVersion/023.png"></image>
                    </view>
                    <view bindtap="gotoExitAddress" class="tem_b_cen_editText" data-item="{{item}}">编辑</view>
                    <view class="tem_b_cen_fg"></view>
                    <view bindtap="delAddress" class="tem_b_cen_delIcon" data-item="{{item}}">
                        <image mode="" src="{{img}}delIcon.png"></image>
                    </view>
                    <view bindtap="delAddress" class="tem_b_cen_delText" data-item="{{item}}">删除</view>
                </view>
            </view>
        </view>
    </view>
    <view class="MyAddress_noList" wx:else>
        <view class="MyAddress_noList_icon">
            <image mode="" src="{{img}}noAddress.png"></image>
        </view>
        <view class="MyAddress_noList_text">暂无地址</view>
    </view>
    <view style="height:300rpx;" wx:if="{{list.length!=0}}"></view>
    <view class="MyAddress_bot">
        <footer></footer>
        <view bindtap="gotoAddress" class="MyAddress_button">新增地址</view>
    </view>
</view>
