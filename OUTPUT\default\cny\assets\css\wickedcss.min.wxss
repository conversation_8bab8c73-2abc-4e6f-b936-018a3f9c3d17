.barrelRoll,.fadeIn,.floater,.heartbeat,.pulse,.rollerLeft,.rollerRight,.rotateIn,.rotateInLeft,.rotateInRight,.rotation,.shake,.sideToSide,.slideDown,.slideLeft,.slideRight,.slideUp,.spinner,.wiggle,.zoomer,.zoomerOut {
    visibility: visible!important
}

.rotation {
    animation-duration: 4s;
    -webkit-animation-duration: 4s;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    animation-name: rotation;
    -webkit-animation-name: rotation;
    animation-timing-function: linear;
    -webkit-animation-timing-function: linear
}

.rotation,.sideToSide {
    -webkit-animation-iteration-count: infinite
}

@-webkit-keyframes rotation {
    from {
        -webkit-transform: rotate(0) translateX(50%) rotate(0)
    }

    to {
        -webkit-transform: rotate(1turn) translateX(50%) rotate(-1turn)
    }
}

@keyframes rotation {
    from {
        -webkit-transform: rotate(0) translateX(50%) rotate(0);
        transform: rotate(0) translateX(50%) rotate(0)
    }

    to {
        -webkit-transform: rotate(1turn) translateX(50%) rotate(-1turn);
        transform: rotate(1turn) translateX(50%) rotate(-1turn)
    }
}

.sideToSide {
    animation-duration: 3s;
    -webkit-animation-duration: 3s;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    animation-name: sideToSide;
    -webkit-animation-name: sideToSide;
    animation-timing-function: ease;
    -webkit-animation-timing-function: ease
}

@-webkit-keyframes sideToSide {
    0%,100% {
        -webkit-transform: translate(100%,0)
    }

    50% {
        -webkit-transform: translate(-100%,0)
    }
}

@keyframes sideToSide {
    0%,100% {
        -webkit-transform: translate(100%,0);
        transform: translate(100%,0)
    }

    50% {
        -webkit-transform: translate(-100%,0);
        transform: translate(-100%,0)
    }
}

.zoomer {
    -webkit-animation-duration: 1s;
    -webkit-animation-iteration-count: 1;
    animation-iteration-count: 1;
    animation-name: zoomer;
    -webkit-animation-name: zoomer
}

.zoomer,.zoomerOut {
    -webkit-animation-timing-function: cubic-bezier(.5,.2,.3,1)
}

.zoomer,.zoomerOut {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-iteration-count: 1;
    animation-timing-function: cubic-bezier(.5,.2,.3,1)
}

@-webkit-keyframes zoomer {
    0% {
        -webkit-transform: scale(.3)
    }

    100% {
        -webkit-transform: scale(1)
    }
}

@keyframes zoomer {
    0% {
        -webkit-transform: scale(.3);
        transform: scale(.3)
    }

    100% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

.zoomerOut {
    -webkit-animation-duration: 1s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-iteration-count: 1;
    animation-iteration-count: 1;
    animation-name: zoomerOut;
    -webkit-animation-name: zoomerOut;
    -webkit-animation-timing-function: cubic-bezier(.5,.2,.3,1)
}

.rollerRight,.zoomerOut {
    -webkit-animation-fill-mode: forwards
}

@-webkit-keyframes zoomerOut {
    0% {
        -webkit-transform: scale(1)
    }

    100% {
        -webkit-transform: scale(0)
    }
}

@keyframes zoomerOut {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    100% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }
}

.spinner {
    -webkit-animation-duration: 2s;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    animation-name: spinner;
    -webkit-animation-name: spinner
}

.pulse,.spinner {
    -webkit-animation-timing-function: linear
}

.pulse,.spinner {
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
    animation-timing-function: linear
}

@-webkit-keyframes spinner {
    from {
        -webkit-transform: rotate(0)
    }

    to {
        -webkit-transform: rotate(1turn)
    }
}

@keyframes spinner {
    from {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.pulse {
    -webkit-animation-duration: 2s;
    animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
    animation-name: pulse;
    -webkit-animation-name: pulse;
    -webkit-animation-timing-function: linear
}

@keyframes pulse {
    0%,100% {
        opacity: .9;
        -webkit-transform: scale(.9);
        transform: scale(.9)
    }

    50% {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@-webkit-keyframes pulse {
    0%,100% {
        opacity: .9;
        -webkit-transform: scale(.95)
    }

    50% {
        opacity: 1;
        -webkit-transform: scale(1)
    }
}

.shake {
    -webkit-animation-duration: .4s;
    animation-name: shake;
    -webkit-animation-name: shake
}

.barrelRoll,.shake {
    -webkit-animation-iteration-count: 1;
    -webkit-animation-timing-function: ease
}

.barrelRoll,.shake {
    -webkit-animation-duration: .4s;
    animation-duration: .4s;
    animation-iteration-count: 1;
    animation-timing-function: ease
}

@keyframes shake {
    0%,100% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    16%,50%,83% {
        -webkit-transform: translateX(-10px);
        transform: translateX(-10px)
    }

    33%,66% {
        -webkit-transform: translateX(10px);
        transform: translateX(10px)
    }
}

@-webkit-keyframes shake {
    0%,100% {
        -webkit-transform: translateX(0)
    }

    16%,50%,83% {
        -webkit-transform: translateX(-10px)
    }

    33%,66% {
        -webkit-transform: translateX(10px)
    }
}

.barrelRoll {
    -webkit-animation-duration: .4s;
    -webkit-animation-iteration-count: 1;
    animation-name: barrelRoll;
    -webkit-animation-name: barrelRoll;
    -webkit-animation-timing-function: ease
}

@keyframes barrelRoll {
    from {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@-webkit-keyframes barrelRoll {
    from {
        -webkit-transform: rotate(0)
    }

    to {
        -webkit-transform: rotate(1turn)
    }
}

.floater {
    animation-duration: 1.5s;
    -webkit-animation-duration: 1.5s;
    animation-name: floater;
    -webkit-animation-name: floater
}

.floater,.wiggle {
    -webkit-animation-iteration-count: infinite
}

.floater,.wiggle {
    animation-iteration-count: infinite
}

@keyframes floater {
    0%,100% {
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }

    50% {
        -webkit-transform: translateY(8%);
        transform: translateY(8%)
    }
}

@-webkit-keyframes floater {
    0%,100% {
        -webkit-transform: translateY(0)
    }

    50% {
        -webkit-transform: translateY(8%)
    }
}

.wiggle {
    animation-duration: 2.5s;
    -webkit-animation-duration: 2.5s;
    -webkit-animation-iteration-count: infinite;
    animation-name: wiggle;
    -webkit-animation-name: wiggle
}

@keyframes wiggle {
    0%,100% {
        -webkit-transform: rotate(-4deg);
        transform: rotate(-4deg)
    }

    50% {
        -webkit-transform: rotate(4deg);
        transform: rotate(4deg)
    }
}

@-webkit-keyframes wiggle {
    0%,100% {
        -webkit-transform: rotate(-4deg)
    }

    50% {
        -webkit-transform: rotate(4deg)
    }
}

.pound {
    animation-duration: .5s;
    -webkit-animation-duration: .5s;
    animation-name: pound;
    -webkit-animation-name: pound;
    visibility: visible!important
}

.heartbeat,.pound {
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-timing-function: ease
}

.heartbeat,.pound {
    animation-iteration-count: infinite;
    animation-timing-function: ease
}

@keyframes pound {
    to {
        -webkit-transform: scale(1.2);
        transform: scale(1.2)
    }
}

@-webkit-keyframes pound {
    to {
        -webkit-transform: scale(1.2);
        transform: scale(1.2)
    }
}

.heartbeat {
    animation-duration: 3s;
    -webkit-animation-duration: 3s;
    -webkit-animation-iteration-count: infinite;
    animation-name: heartbeat;
    -webkit-animation-name: heartbeat;
    -webkit-animation-timing-function: ease
}

@keyframes heartbeat {
    0%,100% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    10% {
        -webkit-transform: scale(1.2);
        transform: scale(1.2)
    }

    20% {
        -webkit-transform: scale(1.4);
        transform: scale(1.4)
    }
}

@-webkit-keyframes heartbeat {
    0%,100% {
        -webkit-transform: scale(1)
    }

    10% {
        -webkit-transform: scale(1.2)
    }

    20% {
        -webkit-transform: scale(1.4)
    }
}

.rollerRight {
    animation-duration: 2s;
    -webkit-animation-duration: 2s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    animation-name: rollerRight;
    -webkit-animation-name: rollerRight
}

.rollerLeft,.rollerRight {
    -webkit-animation-iteration-count: 1;
    -webkit-animation-timing-function: ease
}

.rollerLeft,.rollerRight {
    animation-iteration-count: 1;
    animation-timing-function: ease
}

@keyframes rollerRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-200px) rotate(0);
        transform: translateX(-200px) rotate(0)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0) rotate(2turn);
        transform: translateX(0) rotate(2turn)
    }
}

@-webkit-keyframes rollerRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-200px) rotate(0)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0) rotate(2turn)
    }
}

.rollerLeft {
    animation-duration: 2s;
    -webkit-animation-duration: 2s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-iteration-count: 1;
    animation-name: rollerLeft;
    -webkit-animation-name: rollerLeft;
    -webkit-animation-timing-function: ease
}

.fadeOut,.rollerLeft {
    -webkit-animation-fill-mode: forwards
}

@keyframes rollerLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(200px) rotate(0);
        transform: translateX(200px) rotate(0)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0) rotate(-2turn);
        transform: translateX(0) rotate(-2turn)
    }
}

@-webkit-keyframes rollerLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(200px) rotate(0)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0) rotate(-2turn)
    }
}

.slideDown {
    -webkit-animation-duration: 1s;
    animation-name: slideDown;
    -webkit-animation-name: slideDown
}

.slideDown,.slideUp {
    -webkit-animation-timing-function: ease
}

.slideDown,.slideUp {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    animation-timing-function: ease
}

@keyframes slideDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@-webkit-keyframes slideDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-100%)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateY(0)
    }
}

.slideUp {
    -webkit-animation-duration: 1s;
    animation-name: slideUp;
    -webkit-animation-name: slideUp;
    -webkit-animation-timing-function: ease
}

@keyframes slideUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(100%);
        transform: translateY(100%)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@-webkit-keyframes slideUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(100%)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateY(0)
    }
}

.slideLeft {
    animation-duration: 1s;
    -webkit-animation-duration: 1s;
    animation-name: slideLeft;
    -webkit-animation-name: slideLeft
}

.slideLeft,.slideRight {
    -webkit-animation-timing-function: ease
}

.slideLeft,.slideRight {
    animation-timing-function: ease
}

@keyframes slideLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(150%);
        transform: translateX(150%)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@-webkit-keyframes slideLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(150%)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0)
    }
}

.slideRight {
    animation-duration: 1s;
    -webkit-animation-duration: 1s;
    animation-name: slideRight;
    -webkit-animation-name: slideRight;
    -webkit-animation-timing-function: ease
}

@keyframes slideRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-150%);
        transform: translateX(-150%)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@-webkit-keyframes slideRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-150%)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0)
    }
}

.fadeIn {
    -webkit-animation-duration: 1s;
    animation-name: fadeIn;
    -webkit-animation-name: fadeIn
}

.fadeIn,.fadeOut {
    -webkit-animation-timing-function: ease
}

.fadeIn,.fadeOut {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    animation-timing-function: ease
}

@keyframes fadeIn {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

@-webkit-keyframes fadeIn {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

.fadeOut {
    -webkit-animation-duration: 2s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    animation-name: fadeOut;
    -webkit-animation-name: fadeOut;
    -webkit-animation-timing-function: ease
}

@keyframes fadeOut {
    0% {
        opacity: 1
    }

    100% {
        opacity: 0
    }
}

@-webkit-keyframes fadeOut {
    0% {
        opacity: 1
    }

    100% {
        opacity: 0
    }
}

.rotateInRight {
    animation-duration: 3s;
    -webkit-animation-duration: 3s;
    -webkit-animation-iteration-count: 1;
    animation-iteration-count: 1;
    animation-name: rotateInRight;
    -webkit-animation-name: rotateInRight;
    -webkit-animation-timing-function: ease-in-out
}

.rotateInLeft,.rotateInRight {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

@-webkit-keyframes rotateInRight {
    from {
        -webkit-transform: rotate(0) translateX(100%) rotate(0)
    }

    to {
        -webkit-transform: rotate(1turn) translateX(0) rotate(-1turn)
    }
}

@keyframes rotateInRight {
    from {
        -webkit-transform: rotate(0) translateX(100%) rotate(0);
        transform: rotate(0) translateX(100%) rotate(0)
    }

    to {
        -webkit-transform: rotate(1turn) translateX(0) rotate(-1turn);
        transform: rotate(1turn) translateX(0) rotate(-1turn)
    }
}

.rotateInLeft {
    animation-duration: 1s;
    -webkit-animation-duration: 1s;
    -webkit-animation-iteration-count: 2;
    animation-iteration-count: 2;
    animation-name: rotateInLeft;
    -webkit-animation-name: rotateInLeft;
    -webkit-animation-timing-function: linear
}

.rotateIn,.rotateInLeft {
    -webkit-animation-iteration-count: 2
}

@-webkit-keyframes rotateInLeft {
    from {
        -webkit-transform: rotate(0) translateX(0) rotate(0)
    }

    to {
        -webkit-transform: rotate(-1turn) translateX(-3%) rotate(1turn)
    }
}

@keyframes rotateInLeft {
    0% {
        -webkit-transform: rotate(0) translateX(0) rotate(0);
        transform: rotate(0) translateX(0) rotate(0)
    }

    40% {
        -webkit-transform: rotate(-180deg) translateY(-3%) rotate(180deg);
        transform: rotate(-180deg) translateY(-3%) rotate(180deg)
    }

    80% {
        -webkit-transform: rotate(-1turn) translateX(0) rotate(1turn);
        transform: rotate(-1turn) translateX(0) rotate(1turn)
    }

    100% {
        -webkit-transform: rotate(-1turn) translateX(0) rotate(1turn);
        transform: rotate(-1turn) translateX(0) rotate(1turn)
    }
}

.rotateIn {
    animation-duration: 3s;
    -webkit-animation-duration: 3s;
    -webkit-animation-iteration-count: 1;
    animation-iteration-count: 1;
    animation-name: rotateIn;
    -webkit-animation-name: rotateIn;
    animation-timing-function: ease;
    -webkit-animation-timing-function: ease;
    -webkit-transform-origin: center;
    transform-origin: center
}

@-webkit-keyframes rotateIn {
    0% {
        opacity: 0;
        -webkit-transform: rotate3d(0,0,1,-2turn);
        transform: rotate3d(0,0,1,-2turn)
    }

    100% {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
        -webkit-transform-origin: center;
        transform-origin: center
    }
}

@keyframes rotateIn {
    0% {
        opacity: 0;
        -webkit-transform: rotate3d(0,0,1,-2turn);
        transform: rotate3d(0,0,1,-2turn)
    }

    100% {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

.bounceIn {
    -webkit-animation-duration: .8s;
    animation-duration: .8s;
    -webkit-animation-name: bounceIn;
    animation-name: bounceIn;
    -webkit-animation-timing-function: cubic-bezier(.215,.61,.355,1);
    animation-timing-function: cubic-bezier(.215,.61,.355,1)
}

@-webkit-keyframes bounceIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }

    20% {
        -webkit-transform: scale3d(1.1,1.1,1.1);
        transform: scale3d(1.1,1.1,1.1)
    }

    40% {
        -webkit-transform: scale3d(.9,.9,.9);
        transform: scale3d(.9,.9,.9)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(1.03,1.03,1.03);
        transform: scale3d(1.03,1.03,1.03)
    }

    80% {
        -webkit-transform: scale3d(.97,.97,.97);
        transform: scale3d(.97,.97,.97)
    }

    100% {
        opacity: 1;
        -webkit-transform: scale3d(1,1,1);
        transform: scale3d(1,1,1)
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }

    20% {
        -webkit-transform: scale3d(1.1,1.1,1.1);
        transform: scale3d(1.1,1.1,1.1)
    }

    40% {
        -webkit-transform: scale3d(.9,.9,.9);
        transform: scale3d(.9,.9,.9)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(1.03,1.03,1.03);
        transform: scale3d(1.03,1.03,1.03)
    }

    80% {
        -webkit-transform: scale3d(.97,.97,.97);
        transform: scale3d(.97,.97,.97)
    }

    100% {
        opacity: 1;
        -webkit-transform: scale3d(1,1,1);
        transform: scale3d(1,1,1)
    }
}