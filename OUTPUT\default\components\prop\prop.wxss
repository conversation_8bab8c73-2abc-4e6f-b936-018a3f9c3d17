.prop {
    background: rgba(0,0,0,.6);
    height: 100vh;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 99999
}

.prop_rule_text {
    font-size: 28rpx;
    margin: 0 auto;
    width: 90%
}

.prop_rule {
    background: #fff;
    min-height: 667rpx;
    width: 637rpx
}

.prop_dog,.prop_rule {
    border-radius: 20rpx;
    left: 50%;
    overflow: hidden;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
}

.prop_dog {
    height: 245rpx;
    width: 350rpx
}

.prop_tenPro {
    background: #fff;
    height: 820rpx;
    width: 660rpx
}

.prop_FWgetNL,.prop_tenPro {
    border-radius: 20rpx;
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
}

.prop_FWgetNL {
    overflow: hidden;
    width: 573rpx
}

.prop_FWgetNL2 {
    left: 50%;
    overflow: hidden;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 445rpx
}

.prop_FWgetNL2_img {
    height: 600rpx;
    width: 445rpx
}

.prop_FWgetNL2s {
    left: 50%;
    overflow: hidden;
    position: absolute;
    top: 40rpx;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 670rpx
}

.prop_FWgetNL2_img22 {
    height: 1075rpx;
    width: 670rpx
}

.prop_FWgetNL2_text {
    background: #d80e34;
    border-radius: 35rpx;
    color: #fff;
    font-family: SourceHanSansSC;
    font-size: 31rpx;
    font-weight: 700;
    height: 70rpx;
    line-height: 70rpx;
    margin: 40rpx auto 0;
    text-align: center;
    width: 219rpx
}

.prop_FWgetNL2_close {
    height: 45rpx;
    margin: 21rpx auto 0;
    width: 46rpx
}

.prop_rule_box {
    height: 450rpx;
    margin-top: 190rpx;
    overflow-y: auto;
    width: 100%
}

.prop_rule_box_rule {
    word-wrap: break-word;
    box-sizing: border-box;
    font-size: 24rpx;
    line-height: 2.5;
    padding: 0 20rpx;
    width: 100%;
    word-break: break-all;
    word-break: normal
}

.prop_rule_boxRule {
    box-sizing: border-box;
    font-size: 26rpx;
    line-height: 2;
    padding: 0 40rpx
}

.prop_rule_speak {
    color: #666;
    font-family: Source Han Sans CN;
    font-size: 22rpx;
    font-weight: 400;
    margin-top: 40rpx;
    text-align: center
}

.prop_ruleTop {
    height: 162rpx;
    left: 0;
    position: absolute;
    top: 0;
    width: 637rpx
}

.prop_rule_close {
    height: 40rpx;
    position: absolute;
    right: 15rpx;
    top: 15rpx;
    width: 40rpx
}

.prop_rule_closes {
    height: 29rpx;
    position: absolute;
    right: 24rpx;
    top: 24rpx;
    width: 29rpx
}

.prop_energy_close {
    height: 58rpx;
    position: absolute;
    right: 26rpx;
    top: 26rpx;
    width: 57rpx
}

.prop_rule_topBg {
    -webkit-align-items: center;
    align-items: center;
    background: url("http://dm-assets.supercarrier8.com/wobei/newVersion/070.png");
    background-size: 100% 100%;
    color: #fff;
    display: -webkit-flex;
    display: flex;
    font-family: Source Han Sans CN;
    font-size: 40rpx;
    font-weight: 700;
    height: 162rpx;
    -webkit-justify-content: center;
    justify-content: center;
    width: 637rpx
}

.prop_rule_text {
    box-sizing: border-box;
    height: 760rpx;
    line-height: 2;
    overflow-y: auto;
    padding: 0 27rpx 40rpx
}

.prop_rule_text_title {
    color: #000;
    font-size: 30rpx;
    font-weight: 700
}

.prop_rule_text_text {
    color: #000;
    display: -webkit-flex;
    display: flex;
    font-size: 24rpx
}

.prop_energy {
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 660rpx
}

.prop_energy_top {
    background: #fff;
    border-radius: 20rpx;
    height: 562rpx;
    overflow: hidden
}

.prop_energy_top_title {
    font-size: 54rpx;
    margin-top: 220rpx
}

.prop_energy_top_title,.prop_energy_top_title2 {
    color: #fff;
    font-family: Source Han Sans CN;
    font-weight: 700;
    text-align: center
}

.prop_energy_top_title2 {
    font-size: 36rpx
}

.prop_energy_top_iCON {
    height: 184rpx;
    margin: 46rpx auto 0;
    width: 174rpx
}

.prop_energy_top_button {
    background: #000;
    border-radius: 30rpx;
    color: #fff;
    font-family: Source Han Sans CN;
    font-size: 26rpx;
    font-weight: 700;
    height: 62rpx;
    line-height: 62rpx;
    margin: 80rpx auto 0;
    text-align: center;
    width: 220rpx
}

.prop_energy_bot {
    background: url("http://dm-assets.supercarrier8.com/wobei/newVersion/071.png");
    background-size: 100% 100%;
    height: 201rpx;
    margin: 16rpx auto 0;
    width: 100%
}

.prop_gzh {
    background: url("http://dm-assets.supercarrier8.com/wobei/newVersion/011.png");
    background-size: 100% 100%;
    border-radius: 20rpx;
    height: 678rpx;
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 500rpx
}

.prop_gzh_qrcode {
    background: #fff;
    height: 432rpx;
    margin: 36rpx auto 0;
    width: 432rpx
}

.prop_gzh_text {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 28rpx;
    font-weight: 700;
    margin-top: 26rpx;
    text-align: center
}

.prop_chooseHead {
    background: #fff;
    border-radius: 20rpx;
    height: 842rpx;
    left: 50%;
    overflow: hidden;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 637rpx
}

.prop_chooseHead_top {
    background: url("http://dm-assets.supercarrier8.com/wobei/newVersion/010.png");
    background-size: 100% 100%;
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 40rpx;
    font-weight: 700;
    height: 172rpx;
    line-height: 172rpx;
    text-align: center;
    width: 637rpx
}

.prop_chooseHead_items {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap
}

.prop_chooseHead_item {
    margin-left: 50rpx;
    margin-top: 46rpx;
    width: 140rpx
}

.prop_chooseHead_item_icon {
    height: 119rpx;
    margin: 0 auto;
    width: 119rpx
}

.prop_chooseHead_item_name {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 20rpx;
    font-weight: 500;
    margin-top: 10rpx;
    text-align: center
}

.prop_chooseHead_sure {
    background: #000;
    border-radius: 42rpx;
    color: #fff;
    font-family: Source Han Sans CN;
    font-size: 26rpx;
    font-weight: 700;
    height: 62rpx;
    line-height: 62rpx;
    margin: 90rpx auto 0;
    text-align: center;
    width: 220rpx
}

.prop_exchange {
    background: #fff;
    border-radius: 10rpx;
    box-sizing: border-box;
    height: 413rpx;
    left: 50%;
    padding-top: 70rpx;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 535rpx
}

.prop_exchange_title {
    color: #343434;
    font-family: Source Han Sans CN;
    font-size: 36rpx;
    font-weight: 700;
    text-align: center
}

.prop_exchange_buts {
    display: -webkit-flex;
    display: flex;
    height: 86rpx;
    -webkit-justify-content: space-around;
    justify-content: space-around;
    margin-top: 54rpx
}

.prop_exchange_buts_l {
    border: 2rpx solid #000;
    color: #000;
    height: 63rpx;
    width: 221rpx
}

.prop_exchange_buts_l,.prop_exchange_buts_r {
    border-radius: 42rpx;
    font-family: Source Han Sans CN;
    font-size: 26rpx;
    font-weight: 700;
    line-height: 62rpx;
    text-align: center
}

.prop_exchange_buts_r {
    background: #000;
    color: #fff;
    height: 62rpx;
    width: 220rpx
}

.prop_exSuccess {
    background: #fff;
    border-radius: 20rpx;
    height: 562rpx;
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 660rpx
}

.prop_exSuccess_icon {
    height: 192rpx;
    margin: 72rpx auto 52rpx;
    width: 192rpx
}

.prop_exSuccess_text {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 32rpx;
    font-weight: 400;
    text-align: center
}

.prop_exSuccess_text_title {
    color: #343434;
    font-family: Source Han Sans CN;
    font-size: 36rpx;
    font-weight: 700
}

.prop_exSuccess_text_subtitle {
    color: #646464;
    font-family: Source Han Sans CN;
    font-size: 30rpx;
    font-weight: 700
}

.prop_exSuccess_dh {
    background: #d80e34;
    border-radius: 42rpx;
    color: #fff;
    height: 84rpx;
    margin: 0 auto 29rpx;
    width: 367rpx
}

.prop_exSuccess_dh,.prop_exSuccess_look {
    box-sizing: border-box;
    font-family: Source Han Sans CN;
    font-size: 38rpx;
    font-weight: 700;
    line-height: 84rpx;
    text-align: center
}

.prop_exSuccess_look {
    border: 2rpx solid #000;
    border-radius: 42rpx;
    color: #000;
    height: 86rpx;
    margin: 0 auto;
    width: 369rpx
}

.prop_sq {
    background: #fff;
    border-radius: 30rpx;
    box-sizing: border-box;
    height: 416rpx;
    left: 50%;
    padding-top: 80rpx;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 404rpx
}

.prop_sq_text {
    font-size: 32rpx
}

.prop_sq_button,.prop_sq_text {
    font-weight: 700;
    text-align: center
}

.prop_sq_button {
    background: #000;
    border-radius: 50rpx;
    color: #fff;
    font-size: 26rpx;
    height: 62rpx;
    line-height: 62rpx;
    margin: 40rpx auto 0;
    width: 220rpx
}

.prop_sq_close {
    height: 40rpx;
    position: absolute;
    right: 30rpx;
    top: 30rpx;
    width: 40rpx
}

.prop_Cj_nlbz {
    box-sizing: border-box;
    min-height: 491rpx;
    padding-bottom: 100rpx;
    width: 660rpx
}

.prop_Cj_dkcg,.prop_Cj_nlbz {
    background: #fff;
    border-radius: 20rpx;
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
}

.prop_Cj_dkcg {
    height: 599rpx;
    width: 637rpx
}

.prop_Cj_dkcg2 {
    height: 499rpx
}

.prop_Cj_dkcg2,.prop_Cj_dkcg3 {
    background: #fff;
    border-radius: 20rpx;
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 637rpx
}

.prop_Cj_dkcg3 {
    height: 399rpx
}

.prop_Cj_nlbz_close {
    height: 25rpx;
    position: absolute;
    right: 20rpx;
    top: 20rpx;
    width: 25rpx;
    z-index: 2
}

.prop_Cj_nlbz_top {
    height: 220rpx;
    left: 0;
    position: absolute;
    top: -50rpx;
    width: 100%;
    z-index: 1
}

.prop_Cj_nlbz_top_text {
    font-size: 46rpx;
    right: 168rpx;
    text-shadow: 0 2rpx 6rpx rgba(133,63,0,.25)
}

.prop_Cj_nlbz_top_text,.prop_Cj_nlbz_top_text2 {
    color: #000;
    font-weight: 700;
    letter-spacing: 4rpx;
    position: absolute;
    top: 108rpx
}

.prop_Cj_nlbz_top_text2 {
    font-family: Source Han Sans CN;
    font-size: 40rpx;
    right: 68rpx
}

.prop_Cj_nlbz_top_text23 {
    font-family: Source Han Sans CN;
    font-size: 40rpx;
    right: 158rpx;
    top: 108rpx
}

.prop_Cj_nlbz_top_ruletext,.prop_Cj_nlbz_top_text23 {
    color: #000;
    font-weight: 700;
    letter-spacing: 4rpx;
    position: absolute
}

.prop_Cj_nlbz_top_ruletext {
    font-size: 46rpx;
    left: 148rpx;
    text-shadow: 0 2rpx 6rpx rgba(133,63,0,.25);
    top: 62rpx
}

.prop_Cj_nlbz_text {
    font-size: 28rpx;
    font-weight: 500
}

.prop_Cj_dkcg_text,.prop_Cj_nlbz_text {
    color: #000;
    font-family: Source Han Sans CN;
    margin-top: 233rpx;
    text-align: center
}

.prop_Cj_dkcg_text {
    font-size: 42rpx;
    font-weight: 700
}

.prop_Cj_dkcg_name {
    text-align: center
}

.prop_Cj_dkcg_name,.prop_Cj_dkcg_names {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 28rpx;
    font-weight: 400;
    margin: 0 auto;
    width: 80%
}

.prop_Cj_dkcg_names {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between
}

.prop_Cj_nlbz_copy {
    font-size: 26rpx;
    margin-top: 10rpx;
    text-align: center
}

.prop_Cj_nlbz_copy,.prop_Cj_nlbz_copy_text {
    font-family: Source Han Sans CN;
    font-weight: 500
}

.prop_Cj_nlbz_copy_text {
    word-wrap: break-word;
    border-bottom: 4rpx solid #9cd0ff;
    color: #000;
    font-size: 24rpx;
    font-style: italic;
    width: 400rpx;
    word-break: normal
}

.prop_Cj_nlbz_copy_icon {
    height: 29rpx;
    margin-left: 12rpx;
    width: 69rpx
}

.prop_Cj_nlbz_button {
    border-radius: 31rpx;
    width: 289rpx
}

.prop_Cj_dkcg_button,.prop_Cj_nlbz_button {
    background: #000;
    color: #fff;
    font-family: Source Han Sans CN;
    font-size: 26rpx;
    font-weight: 500;
    height: 62rpx;
    line-height: 62rpx;
    margin: 44rpx auto 0;
    text-align: center
}

.prop_Cj_dkcg_button {
    border-radius: 44rpx;
    width: 220rpx
}

.prop_Cj_nlbz_buttons {
    display: -webkit-flex;
    display: flex;
    height: 62rpx;
    -webkit-justify-content: center;
    justify-content: center;
    margin-top: 79rpx
}

.prop_Cj_nlbz_buttons_cancel {
    border: 2rpx solid #121030;
    box-sizing: border-box;
    color: #000
}

.prop_Cj_nlbz_buttons_cancel,.prop_Cj_nlbz_buttons_sure {
    border-radius: 31rpx;
    font-family: Source Han Sans CN;
    font-size: 26rpx;
    font-weight: 500;
    height: 62rpx;
    line-height: 62rpx;
    text-align: center;
    width: 220rpx
}

.prop_Cj_nlbz_buttons_sure {
    background: #000;
    color: #fff;
    margin-left: 28rpx
}

.prop_Cj_nlbz_bq {
    bottom: 16rpx;
    left: 50%;
    position: absolute;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}

.prop_Cj_nlbz_bq,.prop_Cj_nlbz_bqNew {
    color: #8a8a98;
    font-family: Arial;
    font-size: 16rpx;
    font-weight: 400;
    text-align: center
}

.prop_error {
    background: #fff;
    border-radius: 20rpx;
    height: 363rpx;
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 660rpx
}

.prop_error_top {
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    height: 181rpx;
    overflow: hidden;
    width: 660rpx
}

.prop_error_tops {
    height: 181rpx;
    left: 0;
    position: absolute;
    top: -24rpx;
    width: 660rpx
}

.prop_error_tops_text {
    color: #000;
    font-family: JiangChengYuanTi;
    font-size: 36rpx;
    font-weight: 700;
    left: 228rpx;
    position: absolute;
    text-shadow: 0 2rpx 6rpx rgba(133,63,0,.25);
    top: 94rpx
}

.prop_error_text {
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 30rpx;
    font-weight: 500;
    margin-top: 50rpx;
    text-align: center
}

.prop_head {
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 610rpx
}

.prop_head_img {
    background: #fff;
    border-radius: 20rpx;
    box-sizing: border-box;
    height: 608rpx;
    padding: 2rpx;
    width: 608rpx
}

.prop_head_text {
    background: rgba(0,0,0,.5);
    color: #fff;
    font-family: STYuanti;
    font-size: 28rpx;
    font-weight: 700;
    height: 54rpx;
    line-height: 19px;
    line-height: 54rpx;
    margin: 36rpx auto 0;
    text-align: center;
    width: 206rpx
}

.prop_hbs {
    height: 100vh;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.prop_hb {
    background: #fff;
    box-sizing: border-box;
    height: 944rpx;
    left: 50%;
    padding: 2rpx;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 640rpx
}

.borderR,.prop_hb {
    border-radius: 20rpx
}

.prop_hb_text {
    bottom: 67rpx;
    color: #fff;
    font-family: STYuanti;
    font-size: 28rpx;
    font-weight: 700;
    left: 50%;
    position: absolute;
    text-align: center;
    text-shadow: 0 4rpx 16rpx rgba(66,116,112,.23);
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}

.prop_virtually {
    background: #fff;
    border-radius: 20rpx;
    height: 1017rpx;
    left: 50%;
    overflow: hidden;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 660rpx
}

.prop_Choose {
    background: rgba(0,0,0,.5);
    height: 100vh;
    position: relative;
    width: 100%;
    z-index: 9
}

.prop_Choose_b {
    bottom: 0;
    left: 0;
    position: absolute;
    width: 100%
}

.prop_virtually_top {
    -webkit-align-items: center;
    align-items: center;
    background: url("http://dm-assets.supercarrier8.com/wobei/newVersion/010.png");
    background-size: 100% 100%;
    color: #fff;
    display: -webkit-flex;
    display: flex;
    font-family: JiangChengYuanTi;
    font-weight: 400;
    height: 172rpx;
    -webkit-justify-content: center;
    justify-content: center;
    letter-spacing: 4rpx;
    text-align: center;
    text-shadow: 0 2rpx 6rpx rgba(133,63,0,.25);
    width: 660rpx
}

.prop_virtually_top_title {
    color: #000;
    font-size: 46rpx;
    font-weight: 700;
    margin-bottom: 10rpx
}

.prop_rule_topBg_sub {
    color: #000;
    font-size: 19rpx;
    font-weight: 500
}

.prop_virtually_goods {
    height: 205rpx;
    margin: 30rpx auto;
    width: 205rpx
}

.prop_virtually_km {
    -webkit-align-items: center;
    align-items: center;
    height: 64rpx;
    margin: 0 auto 14rpx;
    position: relative
}

.prop_virtually_km,.prop_virtually_kms {
    background: #f9f6e2;
    border-radius: 20rpx;
    display: -webkit-flex;
    display: flex;
    width: 513rpx
}

.prop_virtually_kms {
    margin: 4rpx auto 0;
    min-height: 188rpx
}

.prop_virtually_km_icon {
    height: 52rpx;
    margin-left: 22rpx;
    width: 52rpx
}

.prop_virtually_km_text {
    font-size: 24rpx;
    margin-left: 27rpx
}

.prop_virtually_km_text,.prop_virtually_km_text_t {
    color: #000;
    font-family: Source Han Sans CN;
    font-weight: 400
}

.prop_virtually_km_text_t {
    font-size: 20rpx;
    line-height: 2;
    margin-left: 20rpx;
    margin-top: 8rpx;
    width: 400rpx
}

.prop_virtually_km_copy {
    height: 30rpx;
    line-height: 30rpx;
    width: 69rpx
}

.prop_virtually_km_copy,.prop_virtually_km_copys {
    border: 1px solid #000;
    border-radius: 18rpx;
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 20rpx;
    font-weight: 500;
    position: absolute;
    right: 17rpx;
    text-align: center;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.prop_virtually_km_copys {
    height: 40rpx;
    line-height: 40rpx;
    width: 120rpx
}

.prop_virtually_km_copys2 {
    background: url("https://dm-assets.supercarrier8.com/wobei/newVersion/009.png");
    background-size: 100% 100%;
    border-radius: 18rpx;
    color: #000;
    font-family: Source Han Sans CN;
    font-size: 20rpx;
    font-weight: 500;
    height: 40rpx;
    line-height: 40rpx;
    margin-left: 8rpx;
    text-align: center;
    width: 98rpx
}

.prop_virtually_km_texts {
    -webkit-align-items: center;
    align-items: center;
    box-sizing: border-box;
    display: -webkit-flex;
    display: flex;
    padding-left: 20rpx
}

.km_text_p {
    box-sizing: border-box;
    height: 64rpx;
    line-height: 64rpx;
    overflow: hidden;
    padding-left: 10rpx;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 90rpx
}

.km_text_p,.km_text_ps {
    font-family: Source Han Sans CN;
    font-size: 24rpx;
    font-weight: 400
}

.km_text_ps {
    color: #96a2a9
}

.km_text_psIcon {
    height: 10rpx;
    margin-left: 8rpx;
    margin-top: 9rpx;
    width: 18rpx
}

.prop_virtually_km_text_area {
    box-sizing: border-box;
    height: 128rpx;
    margin-left: 24rpx;
    padding-top: 21rpx;
    width: 400rpx
}

.prop_virtually_sub {
    background: #000;
    border-radius: 31rpx;
    color: #fff;
    font-family: Source Han Sans CN;
    font-size: 26rpx;
    font-weight: 500;
    height: 62rpx;
    line-height: 62rpx;
    margin: 46rpx auto 0;
    text-align: center;
    width: 220rpx
}

.prop_ClockIn {
    background: #fff;
    border-radius: 30rpx;
    box-sizing: border-box;
    left: 50%;
    min-height: 424rpx;
    padding-bottom: 70rpx;
    padding-top: 90rpx;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 516rpx
}

.prop_ClockIn_title {
    color: #000;
    font-size: 32rpx;
    font-weight: 700;
    margin: 0 auto 60rpx;
    width: 350rpx
}

.prop_ClockIn_button,.prop_ClockIn_title {
    font-family: Source Han Sans CN;
    text-align: center
}

.prop_ClockIn_button {
    background: #000;
    border-radius: 44rpx;
    color: #fff;
    font-size: 26rpx;
    font-weight: 500;
    height: 62rpx;
    line-height: 62rpx;
    margin: 0 auto;
    width: 220rpx
}

.prop_FWgetNL_top {
    height: 227rpx;
    margin: 0 auto;
    width: 351rpx
}

.prop_FWgetNL_box {
    background-image: url("http://dm-assets.supercarrier8.com/wobei/newImgs/prop_27_2.png");
    background-size: 100% 100%;
    height: 399rpx;
    overflow: hidden;
    width: 573rpx
}

.prop_FWgetNL_box_title {
    font-size: 42rpx;
    font-weight: 700;
    margin-top: 60rpx
}

.prop_FWgetNL_box_text,.prop_FWgetNL_box_title {
    color: #0f0000;
    font-family: SourceHanSansSC;
    text-align: center
}

.prop_FWgetNL_box_text {
    font-size: 28rpx;
    font-weight: 500;
    margin-top: 47rpx
}

.prop_FWgetNL_box_but {
    background-image: url("http://dm-assets.supercarrier8.com/wobei/newImgs/prop_27_3.png");
    background-size: 100% 100%;
    color: #fff;
    font-family: SourceHanSansSC;
    font-size: 34rpx;
    font-weight: 500;
    height: 87rpx;
    line-height: 87rpx;
    margin: 40rpx auto 0;
    text-align: center;
    width: 265rpx
}

.prop_FWgetNL_close {
    height: 53rpx;
    margin: 56rpx auto 0;
    width: 53rpx
}

.prop_tenPro_items {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: 240rpx
}

.prop_tenPro_item {
    margin: 0 10rpx 40rpx;
    width: 110rpx
}

.prop_tenPro_item_top {
    height: 110rpx;
    width: 110rpx
}

.prop_tenPro_item_title {
    color: #000;
    font-size: 20rpx;
    font-weight: 400
}

.prop_tenPro_buts,.prop_tenPro_item_title {
    font-family: Source Han Sans CN;
    text-align: center
}

.prop_tenPro_buts {
    background: #d20303;
    border-radius: 31rpx;
    color: #fff;
    font-size: 26rpx;
    font-weight: 500;
    height: 62rpx;
    line-height: 62rpx;
    margin: 40rpx auto 0;
    width: 333rpx
}

.prop_energy_box {
    height: 450rpx;
    margin-top: 190rpx;
    overflow-y: auto;
    width: 100%
}

.prop_energy_box_item {
    background: #f7f7f7;
    border-radius: 48rpx;
    box-sizing: border-box;
    color: #000;
    display: -webkit-flex;
    display: flex;
    font-size: 28rpx;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    line-height: 1.8;
    margin-bottom: 18rpx;
    margin-left: 10%;
    padding: 0 0 0 20rpx;
    width: 80%
}

.prop_energy_box_item .energy_tag {
    background: #f8c934;
    border-radius: 48rpx;
    text-align: center;
    width: 180rpx
}

.prop_energy_box_item .energy_tag .energy_tag_bold_num {
    font-weight: 700
}
